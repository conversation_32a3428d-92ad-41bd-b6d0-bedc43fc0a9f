"""
角色权限管理路由
重新设计的RBAC系统，包含完整的角色和权限管理功能
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app
from flask_login import login_required, current_user
from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectMultipleField, SubmitField
from wtforms.validators import DataRequired, Length
from models import db, Role, Permission
from sqlalchemy import text
import logging

# 创建蓝图
bp = Blueprint('rbac', __name__)

# 设置日志
logger = logging.getLogger(__name__)

def admin_required(f):
    """装饰器：要求超级管理员权限"""
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('请先登录', 'danger')
            return redirect(url_for('login'))

        if not current_user.role:
            flash('用户没有分配角色，无法访问此功能', 'danger')
            return redirect(url_for('index'))

        if current_user.role.code != 'super_admin':
            flash(f'只有超级管理员可以访问此功能，当前角色：{current_user.role.name}', 'danger')
            return redirect(url_for('index'))

        return f(*args, **kwargs)
    return decorated_function

class RoleForm(FlaskForm):
    """角色表单"""
    name = StringField('角色名称', validators=[DataRequired(), Length(min=2, max=50)])
    code = StringField('角色代码', validators=[DataRequired(), Length(min=2, max=50)])
    description = TextAreaField('角色描述', validators=[Length(max=200)])
    permissions = SelectMultipleField('权限', coerce=int)
    submit = SubmitField('保存')

@bp.route('/roles')
@login_required
def roles():
    """角色管理主页面"""
    try:
        # 使用原始SQL查询确保数据正确
        roles_sql = text("SELECT * FROM role ORDER BY id")
        roles_result = db.session.execute(roles_sql).fetchall()
        
        # 构建角色数据
        roles_data = []
        for row in roles_result:
            # 查询该角色的权限数量
            perm_count_sql = text("SELECT COUNT(*) FROM role_permission WHERE role_id = :role_id")
            perm_count = db.session.execute(perm_count_sql, {'role_id': row[0]}).scalar()
            
            # 查询该角色的用户数量
            user_count_sql = text("SELECT COUNT(*) FROM user WHERE role_id = :role_id")
            user_count = db.session.execute(user_count_sql, {'role_id': row[0]}).scalar()
            
            roles_data.append({
                'id': row[0],
                'name': row[1],
                'code': row[2],
                'description': row[3],
                'is_system': row[4],
                'company_id': row[5],
                'created_at': row[6],
                'updated_at': row[7],
                'permission_count': perm_count or 0,
                'user_count': user_count or 0
            })
        
        logger.info(f"角色管理页面加载成功，共 {len(roles_data)} 个角色")
        return render_template('rbac/roles.html', roles=roles_data)
        
    except Exception as e:
        logger.error(f"角色管理页面加载失败: {str(e)}")
        flash(f'角色管理页面加载失败: {str(e)}', 'danger')
        return redirect(url_for('index'))

@bp.route('/roles/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_role():
    """添加角色"""
    form = RoleForm()
    
    # 获取所有权限用于表单选择
    permissions = Permission.query.all()
    form.permissions.choices = [(p.id, p.name) for p in permissions]
    
    if form.validate_on_submit():
        try:
            # 检查角色代码是否已存在
            existing_role = Role.query.filter_by(code=form.code.data).first()
            if existing_role:
                flash('角色代码已存在', 'danger')
                return render_template('rbac/role_form.html', form=form, permissions=permissions)
            
            # 创建新角色
            role = Role(
                name=form.name.data,
                code=form.code.data,
                description=form.description.data,
                is_system=False  # 用户创建的角色都不是系统角色
            )
            
            # 分配权限
            if form.permissions.data:
                selected_permissions = Permission.query.filter(Permission.id.in_(form.permissions.data)).all()
                role.permissions = selected_permissions
            
            db.session.add(role)
            db.session.commit()
            
            flash('角色添加成功', 'success')
            logger.info(f"新角色创建成功: {role.name} ({role.code})")
            return redirect(url_for('rbac.roles'))
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"角色添加失败: {str(e)}")
            flash(f'角色添加失败: {str(e)}', 'danger')
    
    return render_template('rbac/role_form.html', form=form, permissions=permissions)

@bp.route('/roles/<int:role_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_role(role_id):
    """编辑角色"""
    role = Role.query.get_or_404(role_id)
    form = RoleForm(obj=role)
    
    # 获取所有权限用于表单选择
    permissions = Permission.query.all()
    form.permissions.choices = [(p.id, p.name) for p in permissions]
    
    # 设置当前角色的权限
    if request.method == 'GET':
        form.permissions.data = [p.id for p in role.permissions]
    
    if form.validate_on_submit():
        try:
            # 检查角色代码是否与其他角色冲突
            existing_role = Role.query.filter(Role.code == form.code.data, Role.id != role_id).first()
            if existing_role:
                flash('角色代码已存在', 'danger')
                return render_template('rbac/role_form.html', form=form, permissions=permissions, role=role)
            
            # 更新角色信息
            role.name = form.name.data
            if not role.is_system:  # 系统角色不允许修改代码
                role.code = form.code.data
            role.description = form.description.data
            
            # 更新权限（超级管理员始终拥有所有权限）
            if role.code == 'super_admin':
                role.permissions = permissions
            else:
                if form.permissions.data:
                    selected_permissions = Permission.query.filter(Permission.id.in_(form.permissions.data)).all()
                    role.permissions = selected_permissions
                else:
                    role.permissions = []
            
            db.session.commit()
            
            flash('角色更新成功', 'success')
            logger.info(f"角色更新成功: {role.name} ({role.code})")
            return redirect(url_for('rbac.roles'))
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"角色更新失败: {str(e)}")
            flash(f'角色更新失败: {str(e)}', 'danger')
    
    return render_template('rbac/role_form.html', form=form, permissions=permissions, role=role)

@bp.route('/roles/<int:role_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_role(role_id):
    """删除角色"""
    try:
        role = Role.query.get_or_404(role_id)
        
        # 检查是否为系统角色
        if role.is_system:
            flash('系统角色不能删除', 'danger')
            return redirect(url_for('rbac.roles'))
        
        # 检查是否有用户使用该角色
        if role.users:
            flash(f'无法删除角色，当前有 {len(role.users)} 个用户使用该角色', 'danger')
            return redirect(url_for('rbac.roles'))
        
        # 删除角色
        role_name = role.name
        db.session.delete(role)
        db.session.commit()
        
        flash(f'角色 "{role_name}" 删除成功', 'success')
        logger.info(f"角色删除成功: {role_name}")
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"角色删除失败: {str(e)}")
        flash(f'角色删除失败: {str(e)}', 'danger')
    
    return redirect(url_for('rbac.roles'))

@bp.route('/permissions')
@login_required
def permissions():
    """权限管理页面"""
    try:
        # 使用原始SQL查询权限
        permissions_sql = text("SELECT * FROM permission ORDER BY id")
        permissions_result = db.session.execute(permissions_sql).fetchall()
        
        permissions_data = []
        for row in permissions_result:
            # 查询使用该权限的角色数量
            role_count_sql = text("SELECT COUNT(*) FROM role_permission WHERE permission_id = :perm_id")
            role_count = db.session.execute(role_count_sql, {'perm_id': row[0]}).scalar()
            
            permissions_data.append({
                'id': row[0],
                'name': row[1],
                'code': row[2],
                'description': row[3],
                'created_at': row[4],
                'updated_at': row[5],
                'role_count': role_count or 0
            })
        
        logger.info(f"权限管理页面加载成功，共 {len(permissions_data)} 个权限")
        return render_template('rbac/permissions.html', permissions=permissions_data)

    except Exception as e:
        logger.error(f"权限管理页面加载失败: {str(e)}")
        flash(f'权限管理页面加载失败: {str(e)}', 'danger')
        return redirect(url_for('index'))

@bp.route('/role-permissions')
@login_required
def role_permissions():
    """角色权限分配页面"""
    try:
        # 添加调试信息
        logger.info(f"当前用户: {current_user.username}, 角色: {current_user.role.code if current_user.role else '无角色'}")

        # 使用ORM查询而不是原生SQL
        from models import Role, Permission

        roles = Role.query.order_by(Role.id).all()
        permissions = Permission.query.order_by(Permission.id).all()

        # 构建权限矩阵
        role_permissions_map = {}
        for role in roles:
            role_permissions_map[role.id] = [p.id for p in role.permissions]

        logger.info(f"角色权限分配页面加载成功，{len(roles)} 个角色，{len(permissions)} 个权限")
        logger.info(f"角色数据: {[f'{r.name}({r.code})' for r in roles]}")
        logger.info(f"权限数据前5个: {[f'{p.name}({p.code})' for p in permissions[:5]]}")

        # 转换为模板需要的格式（元组列表）
        roles_data = [(r.id, r.name, r.code, r.description) for r in roles]
        permissions_data = [(p.id, p.name, p.code, p.description) for p in permissions]

        return render_template('rbac/role_permissions.html',
                             roles=roles_data,
                             permissions=permissions_data,
                             role_permissions_map=role_permissions_map)

    except Exception as e:
        logger.error(f"角色权限分配页面加载失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        flash(f'角色权限分配页面加载失败: {str(e)}', 'danger')
        return redirect(url_for('index'))

@bp.route('/role-permissions/update', methods=['POST'])
@login_required
def update_role_permissions():
    """更新角色权限分配"""
    try:
        from models import Role, Permission
        from flask_wtf.csrf import validate_csrf

        # 验证CSRF令牌
        try:
            validate_csrf(request.form.get('csrf_token'))
        except Exception as csrf_error:
            logger.error(f"CSRF验证失败: {csrf_error}")
            flash('安全验证失败，请重新提交', 'danger')
            return redirect(url_for('rbac.role_permissions'))

        # 添加调试信息
        logger.info(f"开始更新角色权限，当前用户: {current_user.username}")
        logger.info(f"接收到的表单数据: {dict(request.form)}")

        # 获取所有角色
        roles = Role.query.all()
        logger.info(f"找到 {len(roles)} 个角色")

        for role in roles:
            logger.info(f"处理角色: {role.name} ({role.code})")

            # 超级管理员始终拥有所有权限
            if role.code == 'super_admin':
                all_permissions = Permission.query.all()
                role.permissions = all_permissions
                logger.info(f"超级管理员自动分配所有 {len(all_permissions)} 个权限")
                continue

            # 获取该角色选中的权限
            permission_ids = request.form.getlist(f'permissions[{role.id}][]')
            logger.info(f"角色 {role.name} 选中的权限ID: {permission_ids}")

            if permission_ids:
                # 转换为整数
                permission_ids = [int(pid) for pid in permission_ids]
                selected_permissions = Permission.query.filter(Permission.id.in_(permission_ids)).all()
                role.permissions = selected_permissions
                logger.info(f"为角色 {role.name} 分配了 {len(selected_permissions)} 个权限")
            else:
                role.permissions = []
                logger.info(f"角色 {role.name} 没有选中任何权限，清空权限")

        db.session.commit()
        flash('角色权限更新成功', 'success')
        logger.info("角色权限批量更新成功")

    except Exception as e:
        db.session.rollback()
        logger.error(f"角色权限更新失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        flash(f'角色权限更新失败: {str(e)}', 'danger')

    return redirect(url_for('rbac.role_permissions'))

@bp.route('/role-permissions/update-simple', methods=['POST'])
@login_required
def update_role_permissions_simple():
    """简化版权限更新（无CSRF验证）"""
    try:
        from models import Role, Permission

        # 添加调试信息
        logger.info(f"简化版权限更新，当前用户: {current_user.username}")
        logger.info(f"接收到的表单数据: {dict(request.form)}")

        # 获取所有角色
        roles = Role.query.all()
        logger.info(f"找到 {len(roles)} 个角色")

        for role in roles:
            logger.info(f"处理角色: {role.name} ({role.code})")

            # 超级管理员始终拥有所有权限
            if role.code == 'super_admin':
                all_permissions = Permission.query.all()
                role.permissions = all_permissions
                logger.info(f"超级管理员自动分配所有 {len(all_permissions)} 个权限")
                continue

            # 获取该角色选中的权限
            permission_ids = request.form.getlist(f'permissions[{role.id}][]')
            logger.info(f"角色 {role.name} 选中的权限ID: {permission_ids}")

            if permission_ids:
                # 转换为整数
                permission_ids = [int(pid) for pid in permission_ids]
                selected_permissions = Permission.query.filter(Permission.id.in_(permission_ids)).all()
                role.permissions = selected_permissions
                logger.info(f"为角色 {role.name} 分配了 {len(selected_permissions)} 个权限")
            else:
                role.permissions = []
                logger.info(f"角色 {role.name} 没有选中任何权限，清空权限")

        db.session.commit()
        flash('角色权限更新成功', 'success')
        logger.info("角色权限批量更新成功")

        return f"""
        <h1>权限更新成功</h1>
        <p>角色权限已成功更新</p>
        <a href="/rbac/role-permissions">返回权限分配页面</a>
        """

    except Exception as e:
        db.session.rollback()
        logger.error(f"角色权限更新失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return f"""
        <h1>权限更新失败</h1>
        <p>错误: {str(e)}</p>
        <pre>{traceback.format_exc()}</pre>
        <a href="/rbac/role-permissions">返回权限分配页面</a>
        """

@bp.route('/test-data')
@login_required
def test_data():
    """测试数据显示页面（绕过权限检查）"""
    try:
        # 使用ORM查询
        from models import Role, Permission

        roles = Role.query.order_by(Role.id).all()
        permissions = Permission.query.order_by(Permission.id).all()

        # 构建权限矩阵
        role_permissions_map = {}
        for role in roles:
            role_permissions_map[role.id] = [p.id for p in role.permissions]

        # 转换为模板需要的格式
        roles_data = [(r.id, r.name, r.code, r.description) for r in roles]
        permissions_data = [(p.id, p.name, p.code, p.description) for p in permissions]

        # 直接返回模板，不检查权限
        return render_template('rbac/role_permissions.html',
                             roles=roles_data,
                             permissions=permissions_data,
                             role_permissions_map=role_permissions_map)

    except Exception as e:
        logger.error(f"测试数据页面加载失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return f"<h1>错误</h1><p>{str(e)}</p><pre>{traceback.format_exc()}</pre>"

@bp.route('/debug-user')
@login_required
def debug_user():
    """调试当前用户信息"""
    try:
        user_info = {
            'id': current_user.id,
            'username': current_user.username,
            'is_authenticated': current_user.is_authenticated,
            'role_id': current_user.role_id,
            'role_name': current_user.role.name if current_user.role else '无角色',
            'role_code': current_user.role.code if current_user.role else '无角色代码',
            'company_name': current_user.company.name if current_user.company else '无公司',
        }

        html = f"""
        <h1>当前用户调试信息</h1>
        <table border="1" style="border-collapse: collapse;">
            <tr><th>属性</th><th>值</th></tr>
            <tr><td>用户ID</td><td>{user_info['id']}</td></tr>
            <tr><td>用户名</td><td>{user_info['username']}</td></tr>
            <tr><td>是否已认证</td><td>{user_info['is_authenticated']}</td></tr>
            <tr><td>角色ID</td><td>{user_info['role_id']}</td></tr>
            <tr><td>角色名称</td><td>{user_info['role_name']}</td></tr>
            <tr><td>角色代码</td><td>{user_info['role_code']}</td></tr>
            <tr><td>公司名称</td><td>{user_info['company_name']}</td></tr>
        </table>

        <h2>权限检查</h2>
        <p>是否为超级管理员：{user_info['role_code'] == 'super_admin'}</p>

        <h2>操作</h2>
        <a href="/rbac/roles">角色管理</a> |
        <a href="/rbac/permissions">权限管理</a> |
        <a href="/rbac/role-permissions">权限分配</a> |
        <a href="/rbac/test-data">测试数据</a>
        """

        return html

    except Exception as e:
        import traceback
        return f"<h1>调试错误</h1><p>{str(e)}</p><pre>{traceback.format_exc()}</pre>"

@bp.route('/debug-data')
@login_required
def debug_data():
    """调试数据查询"""
    try:
        from models import Role, Permission
        import os

        # 检查数据库文件路径
        db_path = db.engine.url.database if hasattr(db.engine.url, 'database') else str(db.engine.url)

        # 使用ORM查询
        roles = Role.query.all()
        permissions = Permission.query.all()

        # 原生SQL查询对比
        roles_sql = text("SELECT * FROM role ORDER BY id")
        roles_result = db.session.execute(roles_sql).fetchall()

        permissions_sql = text("SELECT * FROM permission ORDER BY id")
        permissions_result = db.session.execute(permissions_sql).fetchall()

        html = f"""
        <h1>完整数据调试</h1>

        <h2>数据库信息</h2>
        <p><strong>数据库路径:</strong> {db_path}</p>
        <p><strong>当前工作目录:</strong> {os.getcwd()}</p>
        <p><strong>数据库文件是否存在:</strong> {os.path.exists('crm.db')}</p>

        <h2>ORM查询结果</h2>
        <p><strong>角色数量 (ORM):</strong> {len(roles)}</p>
        <p><strong>权限数量 (ORM):</strong> {len(permissions)}</p>

        <h3>角色列表 (ORM)</h3>
        <table border="1" style="border-collapse: collapse;">
            <tr><th>ID</th><th>名称</th><th>代码</th><th>描述</th></tr>
        """

        for role in roles:
            html += f"<tr><td>{role.id}</td><td>{role.name}</td><td>{role.code}</td><td>{role.description or 'N/A'}</td></tr>"

        html += f"""
        </table>

        <h3>权限列表 (ORM) - 前10个</h3>
        <table border="1" style="border-collapse: collapse;">
            <tr><th>ID</th><th>名称</th><th>代码</th><th>描述</th></tr>
        """

        for perm in permissions[:10]:
            html += f"<tr><td>{perm.id}</td><td>{perm.name}</td><td>{perm.code}</td><td>{perm.description or 'N/A'}</td></tr>"

        if len(permissions) > 10:
            html += f"<tr><td colspan='4'>... 还有 {len(permissions) - 10} 个权限</td></tr>"

        html += f"""
        </table>

        <h2>原生SQL查询结果</h2>
        <p><strong>角色数量 (SQL):</strong> {len(roles_result)}</p>
        <p><strong>权限数量 (SQL):</strong> {len(permissions_result)}</p>

        <h3>角色列表 (SQL)</h3>
        <table border="1" style="border-collapse: collapse;">
            <tr><th>ID</th><th>名称</th><th>代码</th><th>描述</th></tr>
        """

        for role in roles_result:
            html += f"<tr><td>{role[0]}</td><td>{role[1]}</td><td>{role[2]}</td><td>{role[3] if len(role) > 3 else 'N/A'}</td></tr>"

        html += f"""
        </table>

        <h3>权限列表 (SQL) - 前10个</h3>
        <table border="1" style="border-collapse: collapse;">
            <tr><th>ID</th><th>名称</th><th>代码</th><th>描述</th></tr>
        """

        for perm in permissions_result[:10]:
            html += f"<tr><td>{perm[0]}</td><td>{perm[1]}</td><td>{perm[2]}</td><td>{perm[3] if len(perm) > 3 else 'N/A'}</td></tr>"

        if len(permissions_result) > 10:
            html += f"<tr><td colspan='4'>... 还有 {len(permissions_result) - 10} 个权限</td></tr>"

        html += f"""
        </table>

        <h2>操作</h2>
        <a href="/rbac/role-permissions">返回权限分配页面</a> |
        <a href="/rbac/test-data">测试数据页面</a>
        """

        return html

    except Exception as e:
        import traceback
        return f"<h1>调试数据错误</h1><p>{str(e)}</p><pre>{traceback.format_exc()}</pre>"

@bp.route('/test-form', methods=['GET', 'POST'])
@login_required
def test_form():
    """测试表单提交"""
    if request.method == 'POST':
        form_data = dict(request.form)
        return f"""
        <h1>表单提交测试</h1>
        <h2>接收到的数据:</h2>
        <pre>{form_data}</pre>
        <a href="/rbac/test-form">返回测试页面</a>
        """

    return """
    <h1>表单提交测试</h1>
    <form method="POST">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
        <p>
            <label>
                <input type="checkbox" name="permissions[1][]" value="1"> 权限1
            </label>
        </p>
        <p>
            <label>
                <input type="checkbox" name="permissions[1][]" value="2"> 权限2
            </label>
        </p>
        <p>
            <label>
                <input type="checkbox" name="permissions[2][]" value="1"> 角色2-权限1
            </label>
        </p>
        <button type="submit">提交测试</button>
    </form>
    """
