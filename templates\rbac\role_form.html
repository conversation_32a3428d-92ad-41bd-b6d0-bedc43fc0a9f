{% extends 'base.html' %}

{% block title %}
{% if role %}编辑角色{% else %}添加角色{% endif %} - CRM系统
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题区域 -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">
                    <i class="fas fa-user-shield me-2"></i>
                    {% if role %}编辑角色{% else %}添加角色{% endif %}
                </h1>
                <p class="page-subtitle">
                    {% if role %}修改角色信息和权限分配{% else %}创建新的系统角色并分配权限{% endif %}
                </p>
            </div>
            <div class="text-end">
                <a href="{{ url_for('rbac.roles') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回角色列表
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- 角色表单 -->
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        {% if role %}编辑角色信息{% else %}角色基本信息{% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" data-skip-validation="true">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.name.label(class="form-label") }}
                                    {{ form.name(class="form-control") }}
                                    {% if form.name.errors %}
                                        <div class="text-danger">
                                            {% for error in form.name.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.code.label(class="form-label") }}
                                    {% if role and role.is_system %}
                                        {{ form.code(class="form-control", readonly=true) }}
                                        <small class="text-muted">系统角色代码不能修改</small>
                                    {% else %}
                                        {{ form.code(class="form-control") }}
                                    {% endif %}
                                    {% if form.code.errors %}
                                        <div class="text-danger">
                                            {% for error in form.code.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control", rows="3") }}
                            {% if form.description.errors %}
                                <div class="text-danger">
                                    {% for error in form.description.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- 权限选择 -->
                        <div class="mb-4">
                            <label class="form-label">权限分配</label>
                            {% if role and role.code == 'super_admin' %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    超级管理员自动拥有所有权限，无需手动分配
                                </div>
                            {% else %}
                                <div class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                    {% if permissions %}
                                        <div class="row">
                                            {% for permission in permissions %}
                                            <div class="col-md-6 mb-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" 
                                                           type="checkbox" 
                                                           name="permissions" 
                                                           value="{{ permission.id }}"
                                                           id="perm_{{ permission.id }}"
                                                           {% if form.permissions.data and permission.id in form.permissions.data %}checked{% endif %}>
                                                    <label class="form-check-label" for="perm_{{ permission.id }}">
                                                        <strong>{{ permission.name }}</strong>
                                                        <br><small class="text-muted">{{ permission.description }}</small>
                                                    </label>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                        
                                        <!-- 权限选择快捷操作 -->
                                        <div class="mt-3 pt-3 border-top">
                                            <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="selectAllPermissions()">
                                                <i class="fas fa-check-square me-1"></i>全选
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearAllPermissions()">
                                                <i class="fas fa-square me-1"></i>清空
                                            </button>
                                        </div>
                                    {% else %}
                                        <p class="text-muted mb-0">暂无可分配的权限</p>
                                    {% endif %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('rbac.roles') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>取消
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                {% if role %}更新角色{% else %}创建角色{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- 帮助信息 -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-question-circle me-2"></i>填写说明</h6>
                </div>
                <div class="card-body">
                    <h6>角色名称</h6>
                    <p class="small text-muted">角色的显示名称，如"销售经理"、"客服专员"等</p>
                    
                    <h6>角色代码</h6>
                    <p class="small text-muted">角色的唯一标识符，建议使用英文和下划线，如"sales_manager"</p>
                    
                    <h6>角色描述</h6>
                    <p class="small text-muted">详细描述该角色的职责和权限范围</p>
                    
                    <h6>权限分配</h6>
                    <p class="small text-muted">选择该角色应该拥有的系统权限，可以随时修改</p>
                </div>
            </div>

            {% if role %}
            <!-- 角色统计信息 -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>角色统计</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary">{{ role.permission_count or 0 }}</h4>
                            <small class="text-muted">当前权限数</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info">{{ role.user_count or 0 }}</h4>
                            <small class="text-muted">使用用户数</small>
                        </div>
                    </div>
                    <hr>
                    <p class="small text-muted mb-0">
                        <strong>创建时间：</strong>{{ role.created_at.strftime('%Y-%m-%d %H:%M') if role.created_at else '未知' }}
                    </p>
                    {% if role.updated_at %}
                    <p class="small text-muted mb-0">
                        <strong>更新时间：</strong>{{ role.updated_at.strftime('%Y-%m-%d %H:%M') }}
                    </p>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function selectAllPermissions() {
    const checkboxes = document.querySelectorAll('input[name="permissions"]');
    checkboxes.forEach(checkbox => checkbox.checked = true);
}

function clearAllPermissions() {
    const checkboxes = document.querySelectorAll('input[name="permissions"]');
    checkboxes.forEach(checkbox => checkbox.checked = false);
}
</script>
{% endblock %}
