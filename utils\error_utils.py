from flask import render_template, flash  # 导入必要的Flask组件

def handle_bad_request(e, app):  # 处理400错误
    app.logger.error(f'400错误: {str(e)}')
    return render_template('errors/400.html'), 400

def handle_forbidden(e, app):  # 处理403错误
    app.logger.error(f'403错误: {str(e)}')
    return render_template('errors/403.html'), 403

def handle_not_found(e, app):  # 处理404错误
    app.logger.error(f'404错误: {str(e)}')
    return render_template('errors/404.html'), 404

def handle_server_error(e, app):  # 处理500错误
    app.logger.error(f'500错误: {str(e)}')
    return render_template('errors/500.html'), 500

def handle_error(error, app):  # 处理通用错误
    app.logger.error(f'系统错误: {str(error)}')
    flash(f'系统错误: {str(error)}', 'danger')
    return render_template('errors/500.html'), 500 