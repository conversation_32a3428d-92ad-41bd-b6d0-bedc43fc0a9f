#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证测试数据是否正确创建
"""

import os
import sys

def verify_test_data():
    """验证测试数据"""
    try:
        # 确保在正确的目录
        if not os.path.isfile('app.py'):
            print("❌ 错误: 请在项目根目录下运行此脚本！")
            return False

        # 导入应用和模型
        from app import create_app
        from config import Config
        from models import db, User, Company, Lead
        
        app = create_app(Config)
        
        with app.app_context():
            print("🔍 验证测试数据")
            print("="*60)
            
            # 1. 检查总线索数
            total_leads = Lead.query.count()
            test_leads = Lead.query.filter(Lead.name.like('测试客户%')).count()
            
            print(f"📊 线索统计:")
            print(f"   总线索数: {total_leads}")
            print(f"   测试线索数: {test_leads}")
            
            # 2. 检查测试线索的各阶段数据
            if test_leads > 0:
                print(f"\n📈 测试线索各阶段统计:")
                
                base_query = Lead.query.filter(Lead.name.like('测试客户%'))
                
                stages = {
                    'called': base_query.filter(Lead.is_called == True).count(),
                    'connected': base_query.filter(Lead.is_connected == True).count(),
                    'valid_call': base_query.filter(Lead.is_valid_call == True).count(),
                    'wechat_added': base_query.filter(Lead.is_wechat_added == True).count(),
                    'intentional': base_query.filter(Lead.is_intentional == True).count(),
                    'visited': base_query.filter(Lead.is_visited == True).count(),
                    'compliant': base_query.filter(Lead.is_compliant == True).count(),
                    'deal_done': base_query.filter(Lead.is_deal_done == True).count(),
                    'car_selected': base_query.filter(Lead.is_car_selected == True).count()
                }
                
                for stage, count in stages.items():
                    percentage = (count / test_leads * 100) if test_leads > 0 else 0
                    print(f"   {stage}: {count} ({percentage:.1f}%)")
            
            # 3. 检查当前用户信息
            print(f"\n👤 当前登录用户信息:")
            admin_user = User.query.filter_by(username='admin').first()
            if admin_user:
                print(f"   用户名: {admin_user.username}")
                print(f"   姓名: {admin_user.name}")
                print(f"   角色: {admin_user.role.name if admin_user.role else '无角色'}")
                print(f"   公司ID: {admin_user.company_id}")
                print(f"   部门ID: {admin_user.department_id}")
                
                # 检查该用户能看到的线索
                user_leads = Lead.query.filter(Lead.owner_id == admin_user.id).count()
                print(f"   拥有的线索数: {user_leads}")
            
            # 4. 检查所有线索的归属
            print(f"\n🏢 线索归属统计:")
            companies = Company.query.all()
            for company in companies:
                company_leads = Lead.query.filter(Lead.company_id == company.id).count()
                print(f"   {company.name}: {company_leads} 条线索")
            
            # 5. 检查线索的owner_id分布
            print(f"\n👥 线索负责人分布:")
            users = User.query.all()
            for user in users:
                user_leads = Lead.query.filter(Lead.owner_id == user.id).count()
                if user_leads > 0:
                    print(f"   {user.name} ({user.username}): {user_leads} 条线索")
            
            # 6. 模拟首页数据查询
            print(f"\n🏠 模拟首页数据查询:")
            
            # 模拟get_funnel_stats函数的查询逻辑
            from utils.homepage_utils import get_funnel_stats
            
            try:
                funnel_stats = get_funnel_stats()
                print(f"   首页漏斗统计结果:")
                for key, value in funnel_stats.items():
                    print(f"     {key}: {value}")
            except Exception as e:
                print(f"   ❌ 首页数据查询失败: {e}")
                import traceback
                traceback.print_exc()
            
            return True
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_data_ownership():
    """修复数据归属问题"""
    try:
        from app import create_app
        from config import Config
        from models import db, User, Lead
        
        app = create_app(Config)
        
        with app.app_context():
            print("\n🔧 修复数据归属问题")
            print("="*50)
            
            # 获取admin用户
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                print("❌ 未找到admin用户")
                return False
            
            # 将所有测试线索的owner_id设置为admin用户
            test_leads = Lead.query.filter(Lead.name.like('测试客户%')).all()
            
            updated_count = 0
            for lead in test_leads:
                if lead.owner_id != admin_user.id:
                    lead.owner_id = admin_user.id
                    updated_count += 1
            
            if updated_count > 0:
                db.session.commit()
                print(f"✅ 已更新 {updated_count} 条线索的负责人为admin")
            else:
                print("✅ 所有测试线索的负责人已经是admin")
            
            return True
            
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

if __name__ == '__main__':
    # 验证数据
    verify_test_data()
    
    # 询问是否修复数据归属
    print("\n" + "="*60)
    choice = input("是否修复测试数据的归属问题？(y/N): ").lower().strip()
    if choice in ['y', 'yes']:
        fix_data_ownership()
        print("\n🎯 请刷新首页查看数据")
