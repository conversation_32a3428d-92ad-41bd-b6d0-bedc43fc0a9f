#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复重复线索数据的脚本
"""

import os
import sys
from datetime import datetime

def fix_duplicate_leads():
    """修复重复的线索数据"""
    try:
        # 确保在正确的目录
        if not os.path.isfile('app.py'):
            print("❌ 错误: 请在项目根目录下运行此脚本！")
            return False

        # 导入应用和模型
        from app import create_app
        from config import Config
        from models import db, Lead
        
        app = create_app(Config)
        
        with app.app_context():
            print("🔧 修复重复线索数据")
            print("="*60)
            
            # 1. 查找重复的电话号码
            print("🔍 查找重复的电话号码...")
            duplicate_phones = db.session.execute("""
                SELECT phone, COUNT(*) as count 
                FROM lead 
                WHERE phone IS NOT NULL AND phone != '' 
                GROUP BY phone 
                HAVING COUNT(*) > 1 
                ORDER BY count DESC
            """).fetchall()
            
            if not duplicate_phones:
                print("✅ 没有发现重复的电话号码")
                return True
            
            print(f"发现 {len(duplicate_phones)} 个重复的电话号码")
            
            # 2. 询问用户是否继续
            print("\n⚠️  警告: 此操作将删除重复的线索记录，只保留最早创建的记录")
            response = input("是否继续? (y/N): ").strip().lower()
            
            if response != 'y':
                print("操作已取消")
                return False
            
            # 3. 处理重复数据
            total_removed = 0
            
            for phone, count in duplicate_phones:
                print(f"\n处理电话号码: {phone} ({count} 条记录)")
                
                # 获取所有相同电话号码的线索，按创建时间排序
                duplicate_leads = Lead.query.filter_by(phone=phone).order_by(Lead.created_at.asc()).all()
                
                if len(duplicate_leads) <= 1:
                    continue
                
                # 保留最早创建的记录
                keep_lead = duplicate_leads[0]
                remove_leads = duplicate_leads[1:]
                
                print(f"  保留: {keep_lead.name} (ID: {keep_lead.id}, 创建时间: {keep_lead.created_at})")
                
                # 删除重复的记录
                for lead in remove_leads:
                    print(f"  删除: {lead.name} (ID: {lead.id}, 创建时间: {lead.created_at})")
                    db.session.delete(lead)
                    total_removed += 1
            
            # 4. 提交更改
            if total_removed > 0:
                print(f"\n准备删除 {total_removed} 条重复记录...")
                response = input("确认提交更改? (y/N): ").strip().lower()
                
                if response == 'y':
                    db.session.commit()
                    print(f"✅ 成功删除 {total_removed} 条重复记录")
                else:
                    db.session.rollback()
                    print("操作已取消，未做任何更改")
            else:
                print("没有需要删除的记录")
            
            return True
            
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def backup_database():
    """备份数据库（仅适用于SQLite）"""
    try:
        from app import create_app
        from config import Config
        
        app = create_app(Config)
        
        # 检查是否使用SQLite
        if 'sqlite' in app.config['SQLALCHEMY_DATABASE_URI']:
            import shutil
            db_path = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')
            backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            shutil.copy2(db_path, backup_path)
            print(f"✅ 数据库已备份到: {backup_path}")
            return True
        else:
            print("⚠️  当前使用的不是SQLite数据库，请手动备份")
            return False
            
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False

if __name__ == '__main__':
    print("🔧 线索重复数据修复工具")
    print("="*60)
    
    # 询问是否先备份
    response = input("是否先备份数据库? (推荐) (Y/n): ").strip().lower()
    if response != 'n':
        backup_database()
        print()
    
    fix_duplicate_leads()
