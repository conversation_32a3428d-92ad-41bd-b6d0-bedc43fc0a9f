from flask import (Blueprint, render_template, request, flash, redirect, url_for,
                   current_app, abort, jsonify)
from flask_login import login_required, current_user
from models import db, Setting # Assuming Setting model is in models/__init__ or models.setting
from forms.admin_forms import EmailSettingsForm # Assuming form is in forms.admin_forms
from models.setting import encrypt_value, decrypt_value # Assuming crypto utils are in models.setting
import time
import logging

admin_settings_bp = Blueprint('admin_settings', __name__, 
                              template_folder='../templates/admin', # Point to admin templates
                              url_prefix='/admin/settings')

MAIL_SETTING_KEYS = [
    'MAIL_SERVER', 'MAIL_PORT', 'MAIL_USERNAME', 'MAIL_PASSWORD', 
    'MAIL_USE_TLS', 'MAIL_USE_SSL', 'MAIL_DEFAULT_SENDER'
]

@admin_settings_bp.route('/email', methods=['GET', 'POST'])
@login_required
def configure_email():
    # --- Permission Check ---
    if not current_user.role or current_user.role.code != 'super_admin':
        abort(403) # Forbidden

    form = EmailSettingsForm()

    if form.validate_on_submit():
        # --- Process POST Request --- 
        try:
            settings_to_save = {
                'MAIL_SERVER': form.mail_server.data,
                'MAIL_PORT': form.mail_port.data,
                'MAIL_USERNAME': form.mail_username.data,
                'MAIL_USE_TLS': form.mail_use_tls.data,
                'MAIL_USE_SSL': form.mail_use_ssl.data,
                'MAIL_DEFAULT_SENDER': form.mail_default_sender.data,
            }
            
            # Handle password separately (encrypt if provided)
            new_password = form.mail_password.data
            if new_password:
                Setting.set('MAIL_PASSWORD', new_password, encrypt=True)
                current_app.config['MAIL_PASSWORD'] = new_password # Update runtime config immediately
            else:
                # If password field is empty, don't update the existing MAIL_PASSWORD setting
                # We might want to reload the decrypted password for the runtime config if it exists
                 existing_password = Setting.get('MAIL_PASSWORD', decrypt=True)
                 if existing_password:
                      current_app.config['MAIL_PASSWORD'] = existing_password

            # Save other settings
            for key, value in settings_to_save.items():
                Setting.set(key, value, encrypt=False) # No encryption for these
                # Update runtime config immediately
                # Convert boolean strings if necessary for app.config
                if isinstance(value, bool):
                    current_app.config[key] = value
                elif key == 'MAIL_PORT': # Ensure port is integer
                     current_app.config[key] = int(value)
                else:
                    current_app.config[key] = str(value) 

            db.session.commit()
            flash('邮件配置已成功保存。', 'success')
            return redirect(url_for('admin_settings.configure_email'))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"保存邮件配置失败: {e}", exc_info=True)
            flash('保存配置时出错，请稍后重试。', 'danger')

    else:
        # --- Process GET Request --- 
        # Load existing settings from DB to populate the form
        # Don't load/decrypt password into the form for security
        settings = {key: Setting.get(key) for key in MAIL_SETTING_KEYS if key != 'MAIL_PASSWORD'}
        
        form.mail_server.data = settings.get('MAIL_SERVER', '')
        # 修复可能导致None值被转换为int的问题
        mail_port = settings.get('MAIL_PORT')
        form.mail_port.data = int(mail_port) if mail_port is not None else 587  # 默认端口
        form.mail_username.data = settings.get('MAIL_USERNAME', '')
        form.mail_use_tls.data = str(settings.get('MAIL_USE_TLS', 'True')).lower() in ['true', '1', 't']
        form.mail_use_ssl.data = str(settings.get('MAIL_USE_SSL', 'False')).lower() in ['true', '1', 't']
        form.mail_default_sender.data = settings.get('MAIL_DEFAULT_SENDER', '')
        # Password field remains empty unless user types a new one

    return render_template('email_config.html', form=form)


@admin_settings_bp.route('/test_email_send', methods=['POST'])
@login_required
def test_email_send():
    """发送测试邮件的API端点"""
    if not current_user.role or current_user.role.code != 'super_admin':
        return jsonify({'success': False, 'message': '权限不足'}), 403

    result = {
        'success': False,
        'message': '',
        'details': []
    }

    try:
        # 安全地获取请求数据
        request_data = {}
        if request.is_json and request.json:
            request_data = request.json
        elif request.form:
            request_data = request.form

        # 获取收件人邮箱
        recipient_email = request_data.get('recipient_email')
        if not recipient_email:
            return jsonify({'success': False, 'message': '请提供收件人邮箱地址'}), 400

        # 获取邮件配置（优先使用表单提交的配置，否则使用系统配置）
        mail_config = {
            'MAIL_SERVER': request_data.get('mail_server') or current_app.config.get('MAIL_SERVER'),
            'MAIL_PORT': int(request_data.get('mail_port') or current_app.config.get('MAIL_PORT', 587)),
            'MAIL_USERNAME': request_data.get('mail_username') or current_app.config.get('MAIL_USERNAME'),
            'MAIL_PASSWORD': request_data.get('mail_password') or current_app.config.get('MAIL_PASSWORD'),
            'MAIL_USE_TLS': request_data.get('mail_use_tls') or current_app.config.get('MAIL_USE_TLS', False),
            'MAIL_USE_SSL': request_data.get('mail_use_ssl') or current_app.config.get('MAIL_USE_SSL', False),
            'MAIL_DEFAULT_SENDER': request_data.get('mail_default_sender') or current_app.config.get('MAIL_DEFAULT_SENDER')
        }

        # 处理布尔值
        if isinstance(mail_config['MAIL_USE_TLS'], str):
            mail_config['MAIL_USE_TLS'] = mail_config['MAIL_USE_TLS'].lower() in ['true', '1', 'on']
        if isinstance(mail_config['MAIL_USE_SSL'], str):
            mail_config['MAIL_USE_SSL'] = mail_config['MAIL_USE_SSL'].lower() in ['true', '1', 'on']

        result['details'].append(f"使用配置: 服务器={mail_config['MAIL_SERVER']}, 端口={mail_config['MAIL_PORT']}")
        result['details'].append(f"用户={mail_config['MAIL_USERNAME']}, SSL={mail_config['MAIL_USE_SSL']}, TLS={mail_config['MAIL_USE_TLS']}")

        # 验证必要配置
        if not all([mail_config['MAIL_SERVER'], mail_config['MAIL_USERNAME'], mail_config['MAIL_PASSWORD']]):
            return jsonify({
                'success': False,
                'message': '邮件配置不完整，请检查服务器、用户名和密码设置',
                'details': result['details']
            }), 400

        # 创建测试邮件
        subject = "CRM系统测试邮件"
        sender = mail_config['MAIL_DEFAULT_SENDER'] or mail_config['MAIL_USERNAME']
        html_body = f"""
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #007bff;">CRM系统邮件测试</h2>
            <p>如果您收到此邮件，说明CRM系统邮件发送功能工作正常。</p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h4>测试信息：</h4>
                <ul>
                    <li><strong>发送时间：</strong> {time.strftime('%Y-%m-%d %H:%M:%S')}</li>
                    <li><strong>发件人：</strong> {sender}</li>
                    <li><strong>收件人：</strong> {recipient_email}</li>
                    <li><strong>SMTP服务器：</strong> {mail_config['MAIL_SERVER']}:{mail_config['MAIL_PORT']}</li>
                </ul>
            </div>
            <hr style="margin: 20px 0;">
            <p style="color: #6c757d; font-size: 14px;">
                <em>此邮件由CRM系统自动发送，请勿回复。</em>
            </p>
        </div>
        """

        result['details'].append(f"创建测试邮件: 发件人={sender}, 收件人={recipient_email}")

        # 使用SMTP直接发送，而不依赖Flask-Mail扩展
        result['details'].append("开始发送邮件...")

        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart

        # 创建SMTP连接
        if mail_config['MAIL_USE_SSL']:
            server = smtplib.SMTP_SSL(mail_config['MAIL_SERVER'], mail_config['MAIL_PORT'])
        else:
            server = smtplib.SMTP(mail_config['MAIL_SERVER'], mail_config['MAIL_PORT'])
            if mail_config['MAIL_USE_TLS']:
                server.starttls()

        result['details'].append(f"连接到SMTP服务器: {mail_config['MAIL_SERVER']}:{mail_config['MAIL_PORT']}")

        # 登录
        server.login(mail_config['MAIL_USERNAME'], mail_config['MAIL_PASSWORD'])
        result['details'].append("SMTP登录成功")

        # 创建邮件
        email_msg = MIMEMultipart('alternative')
        email_msg['Subject'] = subject
        email_msg['From'] = sender
        email_msg['To'] = recipient_email

        # 添加HTML内容
        html_part = MIMEText(html_body, 'html', 'utf-8')
        email_msg.attach(html_part)

        # 发送邮件
        server.send_message(email_msg)
        server.quit()

        result['success'] = True
        result['message'] = '测试邮件发送成功'
        result['details'].append(f"邮件已成功发送到 {recipient_email}")
        result['details'].append("请检查收件箱（可能在垃圾邮件文件夹中）")

    except Exception as e:
        result['success'] = False
        result['message'] = f'发送失败: {str(e)}'
        result['details'].append(f"错误详情: {str(e)}")
        current_app.logger.error(f"测试邮件发送失败: {e}", exc_info=True)

    return jsonify(result)