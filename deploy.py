#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""CRM系统一键部署脚本"""

import os
import sys
import subprocess
import shutil
import time
import random
import string

class CRMDeployer:
    def __init__(self):
        self.app_dir = "/www/wwwroot/crm.jeyue.net"
        self.current_dir = os.getcwd()
        self.backup_dir = "/tmp/crm_backup_{}".format(int(time.time()))
        self.python_cmd = "python3"  # 默认值，会在check_prerequisites中更新
        
    def log(self, message, level="INFO"):
        """日志输出"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print("[{}] [{}] {}".format(timestamp, level, message))
    
    def run_command(self, command, check=True, shell=True):
        """执行命令"""
        self.log("执行命令: {}".format(command))
        try:
            # Python 3.6兼容性：使用PIPE而不是capture_output
            result = subprocess.run(command, shell=shell, check=check,
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                  universal_newlines=True)
            if result.stdout:
                self.log("输出: {}".format(result.stdout.strip()))
            return result
        except subprocess.CalledProcessError as e:
            self.log("命令执行失败: {}".format(e), "ERROR")
            if hasattr(e, 'stderr') and e.stderr:
                self.log("错误: {}".format(e.stderr.strip()), "ERROR")
            raise
    
    def find_python3(self):
        """查找可用的Python3解释器"""
        self.log("开始查找Python解释器...")

        # 首先检查当前运行的Python版本
        current_python = sys.executable
        self.log("当前运行的Python: {}".format(current_python))

        # 检查当前Python版本
        if sys.version_info.major >= 3 and sys.version_info.minor >= 6:
            self.log("✅ 使用当前Python: {} - Python {}.{}.{}".format(
                current_python, sys.version_info.major, sys.version_info.minor, sys.version_info.micro))
            self.python_cmd = current_python
            return current_python

        # 如果当前Python版本不够，尝试查找其他版本
        self.log("当前Python版本过低，查找其他版本...")

        # 简单的Python命令检查
        python_commands = ['python3.8', 'python3.7', 'python3.6', 'python3']

        for cmd in python_commands:
            try:
                # 使用简单的os.system检查
                exit_code = os.system("which {} > /dev/null 2>&1".format(cmd))
                if exit_code == 0:
                    # 检查版本
                    import tempfile
                    with tempfile.NamedTemporaryFile(mode='w+', delete=False) as f:
                        version_cmd = "{} -c \"import sys; print('Python', sys.version_info.major, sys.version_info.minor, sys.version_info.micro)\"".format(cmd)
                        exit_code = os.system("{} > {} 2>&1".format(version_cmd, f.name))

                        if exit_code == 0:
                            f.seek(0)
                            version_output = open(f.name).read().strip()
                            self.log("找到Python: {} - {}".format(cmd, version_output))

                            if "Python 3" in version_output:
                                parts = version_output.split()
                                if len(parts) >= 3:
                                    major, minor = int(parts[1]), int(parts[2])
                                    if major >= 3 and minor >= 6:
                                        self.log("✅ 选择Python: {}".format(cmd))
                                        self.python_cmd = cmd
                                        os.unlink(f.name)
                                        return cmd

                        os.unlink(f.name)
            except Exception as e:
                self.log("检查{}失败: {}".format(cmd, e))
                continue

        raise Exception("未找到可用的Python版本。请确保安装了Python 3.6或更高版本。")

    def check_prerequisites(self):
        """检查部署前提条件"""
        self.log("检查部署前提条件...")

        # 查找并设置Python命令
        python_cmd = self.find_python3()
        self.log("✅ 使用Python: {}".format(python_cmd))
        
        # 检查必要的系统命令
        commands = ['mysql', 'nginx', 'systemctl']
        for cmd in commands:
            try:
                self.run_command("which {}".format(cmd), check=True)
                self.log("✅ {} 已安装".format(cmd))
            except:
                self.log("⚠️  {} 未找到，可能需要手动安装".format(cmd), "WARN")
        
        # 检查必要文件
        required_files = ['app.py', 'wsgi.py', 'config.py', 'requirements.txt', 'uwsgi.ini']
        for file in required_files:
            if not os.path.exists(file):
                raise Exception("缺少必要文件: {}".format(file))
        self.log("✅ 必要文件检查通过")
    
    def backup_existing(self):
        """备份现有部署"""
        if os.path.exists(self.app_dir):
            self.log("备份现有部署到: {}".format(self.backup_dir))
            shutil.copytree(self.app_dir, self.backup_dir)
            self.log("✅ 备份完成")
    
    def get_web_user(self):
        """获取Web服务器用户"""
        # 检查常见的Web服务器用户
        web_users = ['nginx', 'apache', 'www-data', 'httpd']

        for user in web_users:
            exit_code = os.system("id {} > /dev/null 2>&1".format(user))
            if exit_code == 0:
                self.log("找到Web用户: {}".format(user))
                return user

        # 如果nginx不存在，尝试创建
        self.log("未找到Web用户，尝试创建nginx用户...")
        try:
            self.run_command("useradd -r -s /sbin/nologin nginx", check=False)
            # 再次检查
            exit_code = os.system("id nginx > /dev/null 2>&1")
            if exit_code == 0:
                self.log("✅ 成功创建nginx用户")
                return "nginx"
        except:
            pass

        # 如果都没找到，使用root
        self.log("使用root用户")
        return "root"

    def create_directories(self):
        """创建必要目录"""
        self.log("创建应用目录...")
        os.makedirs(self.app_dir, exist_ok=True)
        os.makedirs("{}/logs".format(self.app_dir), exist_ok=True)
        os.makedirs("{}/uploads".format(self.app_dir), exist_ok=True)

        # 获取Web用户
        web_user = self.get_web_user()

        # 移除.user.ini的不可变属性（如果存在）
        user_ini_path = "{}/.user.ini".format(self.app_dir)
        if os.path.exists(user_ini_path):
            try:
                self.run_command("chattr -i {}".format(user_ini_path), check=False)
                self.log("移除.user.ini的不可变属性")
            except:
                pass

        # 设置权限（跳过可能有问题的文件）
        try:
            self.run_command("chown -R {}:{} {}".format(web_user, web_user, self.app_dir))
        except:
            self.log("⚠️  部分文件权限设置失败，继续部署...", "WARN")
            # 至少确保关键目录有正确权限
            self.run_command("chown -R {}:{} {}/logs".format(web_user, web_user, self.app_dir))
            self.run_command("chown -R {}:{} {}/uploads".format(web_user, web_user, self.app_dir))

        self.run_command("chmod -R 755 {}".format(self.app_dir))
        self.run_command("chmod -R 777 {}/logs".format(self.app_dir))
        self.run_command("chmod -R 777 {}/uploads".format(self.app_dir))
        self.log("✅ 目录创建完成")
    
    def copy_files(self):
        """复制应用文件"""
        self.log("复制应用文件...")

        # 要跳过的文件和目录
        skip_items = ['__pycache__', 'logs', 'venv', '.git', '.gitignore', 'instance']

        # 复制所有文件到目标目录
        for item in os.listdir(self.current_dir):
            if item.startswith('.') or item in skip_items:
                self.log("跳过: {}".format(item))
                continue

            src = os.path.join(self.current_dir, item)
            dst = os.path.join(self.app_dir, item)

            try:
                if os.path.isdir(src):
                    if os.path.exists(dst):
                        shutil.rmtree(dst)
                    shutil.copytree(src, dst)
                    self.log("复制目录: {}".format(item))
                else:
                    shutil.copy2(src, dst)
                    self.log("复制文件: {}".format(item))
            except Exception as e:
                self.log("复制失败 {}: {}".format(item, e), "ERROR")
                raise

        self.log("✅ 文件复制完成")
    
    def setup_virtual_environment(self):
        """设置虚拟环境"""
        self.log("设置Python虚拟环境...")

        venv_path = "{}/venv".format(self.app_dir)
        if os.path.exists(venv_path):
            shutil.rmtree(venv_path)

        self.run_command("cd {} && {} -m venv venv".format(self.app_dir, self.python_cmd))
        self.run_command("cd {} && source venv/bin/activate && pip install --upgrade pip".format(self.app_dir))
        self.run_command("cd {} && source venv/bin/activate && pip install -r requirements.txt".format(self.app_dir))
        self.log("✅ 虚拟环境设置完成")
    
    def configure_environment(self):
        """配置环境变量"""
        self.log("配置环境变量...")

        env_file = "{}/.env".format(self.app_dir)
        
        # 生成新的SECRET_KEY
        chars = string.ascii_letters + string.digits + '-_'
        secret_key = ''.join(random.choice(chars) for _ in range(32))
        
        # 读取并修改.env文件
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换配置
        content = content.replace('change-this-to-a-random-secure-string', secret_key)
        content = content.replace('redis://redis:6379/0', 'redis://localhost:6379/0')
        content = content.replace('/app/uploads', '{}/uploads'.format(self.app_dir))
        content = content.replace('SESSION_COOKIE_SECURE=True', 'SESSION_COOKIE_SECURE=False')
        content = content.replace('REMEMBER_COOKIE_SECURE=True', 'REMEMBER_COOKIE_SECURE=False')
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        self.log("✅ 环境配置完成")
    
    def setup_database(self):
        """设置数据库"""
        self.log("设置数据库...")
        
        # 运行数据库迁移
        self.run_command("cd {} && source venv/bin/activate && export FLASK_APP=app.py && flask db upgrade".format(self.app_dir))
        
        # 创建管理员用户
        create_user_script = """
from app import app
with app.app_context():
    from models import db, User, Role, Company, Department
    from werkzeug.security import generate_password_hash

    # 创建超级管理员角色
    admin_role = Role.query.filter_by(name='超级管理员').first()
    if not admin_role:
        admin_role = Role(
            name='超级管理员',
            code='super_admin',
            description='系统超级管理员',
            is_system=True
        )
        db.session.add(admin_role)
        db.session.commit()

    # 创建平台公司
    platform_company = Company.query.filter_by(name='平台管理').first()
    if not platform_company:
        platform_company = Company(
            name='平台管理',
            code='platform',
            type='headquarters'
        )
        db.session.add(platform_company)
        db.session.flush()

    # 创建管理部门
    admin_dept = Department.query.filter_by(name='系统管理部', company_id=platform_company.id).first()
    if not admin_dept:
        admin_dept = Department(
            name='系统管理部',
            code='admin_dept',
            description='系统管理部门',
            company_id=platform_company.id
        )
        db.session.add(admin_dept)
        db.session.flush()

    # 创建admin用户
    admin_user = User.query.filter_by(username='admin').first()
    if not admin_user:
        admin_user = User(
            username='admin',
            password_hash=generate_password_hash('admin123'),
            email='<EMAIL>',
            name='系统管理员',
            role_id=admin_role.id,
            company_id=platform_company.id,
            department_id=admin_dept.id,
            status='active'
        )
        db.session.add(admin_user)
        db.session.commit()
        print('管理员用户创建成功')
"""
        
        with open("{}/create_admin.py".format(self.app_dir), 'w', encoding='utf-8') as f:
            f.write(create_user_script)

        self.run_command("cd {} && source venv/bin/activate && {} create_admin.py".format(self.app_dir, self.python_cmd))
        os.remove("{}/create_admin.py".format(self.app_dir))
        
        self.log("✅ 数据库设置完成")
    
    def configure_nginx(self):
        """配置Nginx"""
        self.log("配置Nginx...")
        
        nginx_config = """
server {
    listen 80;
    server_name crm.jeyue.net;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias """ + self.app_dir + """/static;
        expires 30d;
    }

    location /uploads {
        alias """ + self.app_dir + """/uploads;
    }
}
"""
        
        with open('/etc/nginx/sites-available/crm.jeyue.net', 'w') as f:
            f.write(nginx_config)
        
        # 启用站点
        if not os.path.exists('/etc/nginx/sites-enabled/crm.jeyue.net'):
            os.symlink('/etc/nginx/sites-available/crm.jeyue.net', 
                      '/etc/nginx/sites-enabled/crm.jeyue.net')
        
        # 测试并重载Nginx
        self.run_command("nginx -t")
        self.run_command("systemctl reload nginx")
        
        self.log("✅ Nginx配置完成")
    
    def setup_systemd_service(self):
        """设置systemd服务"""
        self.log("设置systemd服务...")

        # 获取Web用户
        web_user = self.get_web_user()

        service_config = """[Unit]
Description=CRM uWSGI instance
After=network.target

[Service]
User={}
Group={}
WorkingDirectory={}
Environment="PATH={}/venv/bin"
ExecStart={}/venv/bin/uwsgi --ini uwsgi.ini
Restart=always

[Install]
WantedBy=multi-user.target
""".format(web_user, web_user, self.app_dir, self.app_dir, self.app_dir)
        
        with open('/etc/systemd/system/crm-uwsgi.service', 'w') as f:
            f.write(service_config)
        
        self.run_command("systemctl daemon-reload")
        self.run_command("systemctl enable crm-uwsgi")
        self.run_command("systemctl start crm-uwsgi")
        
        self.log("✅ systemd服务设置完成")
    
    def deploy(self):
        """执行部署"""
        try:
            self.log("开始CRM系统部署...")
            
            self.check_prerequisites()
            self.backup_existing()
            self.create_directories()
            self.copy_files()
            self.setup_virtual_environment()
            self.configure_environment()
            self.setup_database()
            self.configure_nginx()
            self.setup_systemd_service()
            
            self.log("🎉 CRM系统部署完成！")
            self.log("默认管理员账号: admin / admin123")
            self.log("访问地址: http://crm.jeyue.net")
            
        except Exception as e:
            self.log("部署失败: {}".format(e), "ERROR")
            if os.path.exists(self.backup_dir):
                self.log("可以从备份恢复: {}".format(self.backup_dir))
            sys.exit(1)

if __name__ == "__main__":
    if os.geteuid() != 0:
        print("请使用root权限运行此脚本: sudo python deploy.py")
        sys.exit(1)
    
    deployer = CRMDeployer()
    deployer.deploy()
