{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ title }}</h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="employeeForm" data-skip-validation="true">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <div class="row">
                            <!-- 基本信息 -->
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">姓名 <span class="text-danger">*</span></label>
                                    <input type="text" name="name" class="form-control" placeholder="请输入员工姓名" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">用户名 <span class="text-danger">*</span></label>
                                    <input type="text" name="username" class="form-control" placeholder="请输入用户名" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">邮箱 <span class="text-danger">*</span></label>
                                    <input type="email" name="email" class="form-control" placeholder="请输入邮箱地址" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">手机号码</label>
                                    <input type="text" name="phone" class="form-control" placeholder="请输入手机号码">
                                </div>
                            </div>
                            
                            <!-- 组织关系 -->
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label class="form-label">所属公司 <span class="text-danger">*</span></label>
                                    <select name="company_id" id="companySelect" class="form-control" required>
                                        <option value="0">请选择公司</option>
                                        {% for company in companies %}
                                        <option value="{{ company.id }}" 
                                                {% if selected_company_id == company.id %}selected{% endif %}>
                                            {{ company.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label class="form-label">所属部门</label>
                                    <select name="department_id" id="departmentSelect" class="form-control">
                                        <option value="0">请选择部门</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label class="form-label">角色 <span class="text-danger">*</span></label>
                                    <select name="role_id" class="form-control" required>
                                        <option value="0">请选择角色</option>
                                        {% for role in roles %}
                                        <option value="{{ role.id }}">{{ role.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <!-- 密码设置 -->
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label class="form-label">初始密码</label>
                                    <input type="password" name="password" class="form-control" placeholder="留空则使用默认密码 123456">
                                    <small class="form-text text-muted">默认密码为 123456，员工首次登录后需要修改密码</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">创建员工</button>
                            <a href="{{ url_for('organization_unified.employees') }}" class="btn btn-secondary">取消</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    console.log('页面加载完成');
    
    const $companySelect = $('#companySelect');
    const $departmentSelect = $('#departmentSelect');
    
    // 公司选择变化时更新部门
    $companySelect.change(function() {
        const companyId = $(this).val();
        console.log('公司选择变化:', companyId);
        updateDepartments(companyId);
    });
    
    // 更新部门列表
    function updateDepartments(companyId) {
        console.log('更新部门列表, 公司ID:', companyId);
        
        // 清空部门选项
        $departmentSelect.html('<option value="0">请选择部门</option>');
        
        if (companyId && companyId !== '0') {
            const apiUrl = `/organization_api/companies/${companyId}/departments`;
            console.log('请求API:', apiUrl);
            
            $.ajax({
                url: apiUrl,
                method: 'GET',
                success: function(data) {
                    console.log('部门数据:', data);
                    if (data && Array.isArray(data)) {
                        if (data.length === 0) {
                            $departmentSelect.append('<option value="0">该公司暂无部门</option>');
                        } else {
                            data.forEach(function(dept) {
                                $departmentSelect.append(`<option value="${dept.id}">${dept.name}</option>`);
                            });
                            
                            // 如果有预选的部门ID，设置选中
                            const selectedDeptId = {{ selected_department_id or 0 }};
                            if (selectedDeptId > 0) {
                                $departmentSelect.val(selectedDeptId);
                            }
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('获取部门失败:', status, error);
                    $departmentSelect.append('<option value="0">加载部门失败</option>');
                }
            });
        }
    }
    
    // 页面加载时初始化部门列表
    const initialCompanyId = $companySelect.val();
    console.log('初始公司ID:', initialCompanyId);
    if (initialCompanyId && initialCompanyId !== '0') {
        updateDepartments(initialCompanyId);
    }
});
</script>
{% endblock %}
