{% extends 'base.html' %}

{% block title %}部门管理 - CRM系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{{ company.name }} - 部门管理</h1>
    <div>
        <a href="{{ url_for('organization.add_department', company_id=company.id) }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> 添加部门
        </a>
    </div>
</div>

<!-- 部门列表 -->
<div class="card shadow">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>部门名称</th>
                        <th>部门代码</th>
                        <th>部门类型</th>
                        <th>上级部门</th>
                        <th>用户数量</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for department in departments %}
                    <tr>
                        <td>{{ department.name }}</td>
                        <td>{{ department.code }}</td>
                        <td>
                            {% if department.type == 'department' %}
                            <span class="badge bg-primary">部门</span>
                            {% elif department.type == 'group' %}
                            <span class="badge bg-success">小组</span>
                            {% else %}
                            <span class="badge bg-secondary">{{ department.type }}</span>
                            {% endif %}
                        </td>
                        <td>{{ department.parent.name if department.parent else '-' }}</td>
                        <td>{{ department.users|length }}</td>
                        <td>{{ department.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            <a href="{{ url_for('organization.department_users', company_id=company.id, department_id=department.id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-people"></i> 用户管理
                            </a>
                            <a href="{{ url_for('organization.edit_department', company_id=company.id, department_id=department.id) }}" class="btn btn-sm btn-outline-warning">
                                <i class="bi bi-pencil"></i> 编辑
                            </a>
                            <button class="btn btn-sm btn-outline-danger delete-btn"
                                    data-bs-toggle="modal"
                                    data-bs-target="#deleteDepartmentModal"
                                    data-department-name="{{ department.name }}"
                                    data-delete-url="{{ url_for('organization.delete_department', company_id=company.id, department_id=department.id) }}">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 删除部门模态框 -->
<div class="modal fade" id="deleteDepartmentModal" tabindex="-1" aria-labelledby="deleteDepartmentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteDepartmentModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="deleteDepartmentForm" method="POST" action="">
                {{ form.csrf_token }}
                <div class="modal-body">
                    <p>确定要删除部门 <strong id="departmentNameToDelete"></strong> 吗？</p>
                    <p>此操作不可逆，部门下的所有用户和子部门将被转移到默认部门。</p>
                    <p>请输入部门名称 <strong id="departmentNameToConfirm" class="text-danger"></strong> 以确认：</p>
                    <div class="mb-3">
                        <input type="text" class="form-control" name="confirm_name" required autocomplete="off">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function () {
    var deleteModal = document.getElementById('deleteDepartmentModal');
    deleteModal.addEventListener('show.bs.modal', function (event) {
        var button = event.relatedTarget;
        var departmentName = button.getAttribute('data-department-name');
        var deleteUrl = button.getAttribute('data-delete-url');
        
        var modalTitle = deleteModal.querySelector('.modal-title');
        var departmentNameToDelete = deleteModal.querySelector('#departmentNameToDelete');
        var departmentNameToConfirm = deleteModal.querySelector('#departmentNameToConfirm');
        var form = deleteModal.querySelector('#deleteDepartmentForm');
        
        modalTitle.textContent = '确认删除: ' + departmentName;
        departmentNameToDelete.textContent = departmentName;
        departmentNameToConfirm.textContent = departmentName;
        form.action = deleteUrl;
    });
});
</script>
{% endblock %}