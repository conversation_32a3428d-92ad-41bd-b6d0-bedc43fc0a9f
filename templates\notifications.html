{% extends 'base.html' %}

{% block title %}通知中心 - CRM系统{% endblock %}

{% block content %}
<!-- 页面标题区域 -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">
                <i class="fas fa-bell me-2"></i>通知中心
            </h1>
            <p class="page-subtitle">查看系统通知和消息提醒</p>
        </div>
        <div class="text-end">
            <div class="btn-group">
                <button type="button" class="btn btn-outline-light" onclick="markAllAsRead()">
                    <i class="fas fa-check-double me-1"></i>全部标记已读
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">

    {% if notifications %}
        <div class="list-group">
            {% for notification in notifications %}
            <a href="{{ notification.related_url or '#' }}" class="list-group-item list-group-item-action {% if not notification.is_read %}list-group-item-primary{% endif %}" aria-current="true">
                <div class="d-flex w-100 justify-content-between">
                    <p class="mb-1">{{ notification.message }}</p>
                    <small>{{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                </div>
                {% if not notification.is_read %}
                <small class="text-primary">未读</small>
                {% else %}
                <small class="text-muted">已读</small>
                {% endif %}
            </a>
            {% endfor %}
        </div>

        <!-- 分页 (如果需要) -->
        {# {% include '_pagination.html' with context %} #}

    {% else %}
        <div class="alert alert-info">暂无通知。</div>
    {% endif %}
</div>

<script>
function markAllAsRead() {
    // 这里可以添加AJAX请求来标记所有通知为已读
    alert('功能开发中...');
}
</script>
{% endblock %}