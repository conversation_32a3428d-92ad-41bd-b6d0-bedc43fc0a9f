{% extends 'base.html' %}

{% block title %}角色管理 - CRM系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题区域 -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">
                    <i class="fas fa-user-shield me-2"></i>角色管理
                </h1>
                <p class="page-subtitle">管理系统角色和权限分配，控制不同角色的功能访问权限</p>
            </div>
            <div class="text-end">
                <div class="btn-group-theme">
                    <a href="{{ url_for('rbac.role_permissions') }}" class="btn btn-outline-warning me-2">
                        <i class="fas fa-cogs me-1"></i>权限分配
                    </a>
                    <a href="{{ url_for('rbac.add_role') }}" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>添加角色
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-primary">{{ roles|length }}</h3>
                    <p class="mb-0">总角色数</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    {% set system_roles = roles|selectattr('is_system')|list %}
                    <h3 class="text-success">{{ system_roles|length }}</h3>
                    <p class="mb-0">系统角色</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    {% set total_users = roles|sum(attribute='user_count') %}
                    <h3 class="text-info">{{ total_users }}</h3>
                    <p class="mb-0">总用户数</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    {% set max_permissions = roles|map(attribute='permission_count')|max %}
                    <h3 class="text-warning">{{ max_permissions or 0 }}</h3>
                    <p class="mb-0">最多权限</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 角色列表 -->
    <div class="card shadow">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-list me-2"></i>角色列表</h5>
        </div>
        <div class="card-body">
            {% if roles %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th width="8%">序号</th>
                            <th width="15%">角色名称</th>
                            <th width="15%">角色代码</th>
                            <th width="25%">描述</th>
                            <th width="10%">权限数</th>
                            <th width="10%">用户数</th>
                            <th width="10%">类型</th>
                            <th width="7%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for role in roles %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>
                                <strong>{{ role.name }}</strong>
                                {% if role.is_system %}
                                <span class="badge bg-info ms-2">系统</span>
                                {% endif %}
                            </td>
                            <td><code class="text-primary">{{ role.code }}</code></td>
                            <td>{{ role.description or '无描述' }}</td>
                            <td><span class="badge bg-primary">{{ role.permission_count }}</span></td>
                            <td><span class="badge bg-info">{{ role.user_count }}</span></td>
                            <td>
                                {% if role.is_system %}
                                <span class="badge bg-warning">系统内置</span>
                                {% else %}
                                <span class="badge bg-success">自定义</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="d-flex justify-content-between">
                                    <a href="{{ url_for('rbac.edit_role', role_id=role.id) }}"
                                       class="btn btn-sm btn-outline-primary"
                                       title="编辑角色">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% if not role.is_system %}
                                    <form action="{{ url_for('rbac.delete_role', role_id=role.id) }}"
                                          method="post"
                                          class="d-inline"
                                          data-skip-validation="true"
                                          onsubmit="return confirm('确定要删除角色「{{ role.name }}」吗？')">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="删除角色">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                    {% else %}
                                    <span></span>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-user-shield fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无角色数据</h5>
                <p class="text-muted">系统中还没有创建任何角色，请先添加角色。</p>
                <a href="{{ url_for('rbac.add_role') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>添加第一个角色
                </a>
            </div>
            {% endif %}
        </div>
    </div>


</div>
{% endblock %}
