{% extends "base.html" %}

{% block title %}组织管理{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jstree/3.3.12/themes/default/style.min.css">
<style>
    .organization-container {
        height: calc(100vh - 200px);
        min-height: 600px;
    }
    
    .org-tree-panel {
        background: #f8f9fa;
        border-right: 1px solid #dee2e6;
        height: 100%;
        overflow-y: auto;
    }
    
    .org-detail-panel {
        height: 100%;
        overflow-y: auto;
    }
    
    /* 移除自定义stat-card样式，使用Bootstrap卡片 */
    
    .org-tree {
        padding: 15px;
    }
    
    .tree-search {
        margin-bottom: 15px;
    }
    
    .quick-actions {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #dee2e6;
    }
    
    .detail-tabs {
        border-bottom: 1px solid #dee2e6;
        margin-bottom: 20px;
    }
    
    .detail-tabs .nav-link {
        border: none;
        border-bottom: 3px solid transparent;
        color: #6c757d;
        font-weight: 500;
    }
    
    .detail-tabs .nav-link.active {
        color: #495057;
        border-bottom-color: #007bff;
        background: none;
    }
    
    .tab-content {
        padding: 20px 0;
    }
    
    .info-card {
        background: #fff;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .info-card h5 {
        color: #495057;
        margin-bottom: 15px;
        font-weight: 600;
    }
    
    .form-group-inline {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .form-group-inline label {
        width: 100px;
        margin-bottom: 0;
        margin-right: 15px;
        font-weight: 500;
    }
    
    .form-group-inline .form-control {
        flex: 1;
    }
    
    .edit-mode {
        background-color: #fff3cd;
        border-color: #ffeaa7;
    }
    
    .manager-badge {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        font-size: 0.75em;
        padding: 2px 6px;
        border-radius: 10px;
        margin-left: 5px;
    }
    
    .node-actions {
        margin-left: 10px;
        opacity: 0;
        transition: opacity 0.2s;
    }
    
    .jstree-node:hover .node-actions {
        opacity: 1;
    }
    
    .btn-sm-icon {
        padding: 2px 6px;
        font-size: 0.75em;
    }
    
    @media (max-width: 768px) {
        .organization-container {
            height: auto;
        }
        
        .org-tree-panel {
            border-right: none;
            border-bottom: 1px solid #dee2e6;
            height: auto;
            max-height: 300px;
        }
        
        .org-detail-panel {
            height: auto;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题和权限显示 -->
    <div class="row mb-3">
        <div class="col-md-8">
            <h2><i class="fas fa-sitemap"></i> 组织管理</h2>
        </div>
        <div class="col-md-4 text-right">
            <span class="badge badge-info">当前权限: {{ current_user.role.name }}</span>
            <span class="badge badge-secondary ml-2">管理范围: {{ scope.scope_name }}</span>
        </div>
    </div>
    
    <!-- 统计概览卡片 - 与线索管理样式完全一致 -->
    <div class="row mb-4">
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-building text-primary" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-primary mt-2 mb-1">公司</h6>
                    <h4 class="mb-1">{{ scope.companies|length }}</h4>
                    <small class="text-muted">总数量</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-diagram-3 text-success" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-success mt-2 mb-1">部门</h6>
                    <h4 class="mb-1">{{ scope.departments|length }}</h4>
                    <small class="text-muted">总数量</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-people text-info" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-info mt-2 mb-1">员工</h6>
                    <h4 class="mb-1">{{ scope.users|length }}</h4>
                    <small class="text-muted">总数量</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-shield-check text-warning" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-warning mt-2 mb-1">管理员</h6>
                    <h4 class="mb-1">{{ scope.managers_count }}</h4>
                    <small class="text-muted">权限用户</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-circle-fill text-success" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-success mt-2 mb-1">在线</h6>
                    <h4 class="mb-1">{{ scope.active_users_count }}</h4>
                    <small class="text-muted">活跃用户</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-graph-up text-secondary" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-secondary mt-2 mb-1">参与排名</h6>
                    <h4 class="mb-1">{{ scope.performance_users_count }}</h4>
                    <small class="text-muted">考核用户</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="row organization-container">
        <!-- 左侧组织架构树 -->
        <div class="col-md-4 org-tree-panel">
            <div class="org-tree">
                <!-- 搜索框 -->
                <div class="tree-search">
                    <div class="input-group">
                        <input type="text" class="form-control" id="tree-search" placeholder="搜索组织...">
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 组织架构树 -->
                <div id="organization-tree"></div>
                
                <!-- 快速操作按钮 -->
                <div class="quick-actions">
                    <h6>快速操作</h6>
                    {% if current_user.has_permission('company_manage') %}
                    <button class="btn btn-primary btn-sm btn-block mb-2" id="add-company-btn">
                        <i class="fas fa-plus"></i> 添加公司
                    </button>
                    {% endif %}
                    {% if current_user.has_permission('department_manage') %}
                    <button class="btn btn-success btn-sm btn-block mb-2" id="add-department-btn">
                        <i class="fas fa-plus"></i> 添加部门
                    </button>
                    {% endif %}
                    {% if current_user.has_permission('user_manage') %}
                    <button class="btn btn-info btn-sm btn-block mb-2" id="add-employee-btn">
                        <i class="fas fa-plus"></i> 添加员工
                    </button>
                    {% endif %}
                    <button class="btn btn-secondary btn-sm btn-block" id="refresh-tree-btn">
                        <i class="fas fa-sync"></i> 刷新
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 右侧详情面板 -->
        <div class="col-md-8 org-detail-panel">
            <div class="p-3">
                <!-- 详情面板标题 -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 id="detail-title">请选择左侧组织节点</h4>
                    <div id="detail-actions"></div>
                </div>
                
                <!-- 标签导航 -->
                <ul class="nav nav-tabs detail-tabs" id="detail-tabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="overview-tab" data-toggle="tab" href="#overview" role="tab">
                            <i class="fas fa-chart-pie"></i> 概览
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="info-tab" data-toggle="tab" href="#info" role="tab">
                            <i class="fas fa-info-circle"></i> 基本信息
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="departments-tab" data-toggle="tab" href="#departments" role="tab">
                            <i class="fas fa-building"></i> 部门管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="employees-tab" data-toggle="tab" href="#employees" role="tab">
                            <i class="fas fa-users"></i> 员工管理
                        </a>
                    </li>
                </ul>
                
                <!-- 标签内容 -->
                <div class="tab-content" id="detail-tab-content">
                    <!-- 概览标签 -->
                    <div class="tab-pane fade show active" id="overview" role="tabpanel">
                        <div id="overview-content">
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-mouse-pointer fa-3x mb-3"></i>
                                <p>请在左侧选择一个组织节点查看详细信息</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 基本信息标签 -->
                    <div class="tab-pane fade" id="info" role="tabpanel">
                        <div id="info-content"></div>
                    </div>
                    
                    <!-- 部门管理标签 -->
                    <div class="tab-pane fade" id="departments" role="tabpanel">
                        <div id="departments-content"></div>
                    </div>
                    
                    <!-- 员工管理标签 -->
                    <div class="tab-pane fade" id="employees" role="tabpanel">
                        <div id="employees-content"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模态框容器 -->
<div id="modal-container"></div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/jstree/3.3.12/jstree.min.js"></script>
<script src="{{ url_for('static', filename='js/organization-unified.js') }}"></script>
{% endblock %}
