{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <h2>更新线索签约状态</h2>
    <div class="card">
        <div class="card-body">
            <h5 class="card-title">{{ lead.name }} - {{ lead.company_name }}</h5>
            <form method="POST" data-skip-validation="true">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                
                <div class="mb-3">
                    <label for="deal_status" class="form-label">签约状态</label>
                    <select class="form-select" id="deal_status" name="deal_status" required>
                        <option value="pending" {% if lead.deal_status == 'pending' %}selected{% endif %}>待定</option>
                        <option value="won" {% if lead.deal_status == 'won' %}selected{% endif %}>签约</option>
                        <option value="lost" {% if lead.deal_status == 'lost' %}selected{% endif %}>未签约</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="deal_time" class="form-label">签约时间</label>
                    <input type="date" class="form-control" id="deal_time" name="deal_time">
                </div>
                
                <div class="mb-3">
                    <label for="deal_amount" class="form-label">签约金额</label>
                    <input type="number" step="0.01" class="form-control" id="deal_amount" name="deal_amount" placeholder="请输入签约金额">
                </div>
                
                <div class="mb-3">
                    <label for="feedback" class="form-label">反馈内容</label>
                    <textarea class="form-control" id="feedback" name="feedback" rows="3" placeholder="请输入详细的反馈内容"></textarea>
                </div>
                
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">提交</button>
                    <a href="{{ url_for('lead.detail', lead_id=lead.id) }}" class="btn btn-secondary">返回</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}