{% extends 'base.html' %}

{% block title %}个人资料 - CRM系统{% endblock %}

{% block content %}
<!-- 页面标题区域 -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">
                <i class="fas fa-user-circle me-2"></i>个人资料
            </h1>
            <p class="page-subtitle">管理您的个人信息和账户设置</p>
        </div>
        <div class="text-end">
            <div class="btn-group">
                <a href="{{ url_for('index') }}" class="btn btn-outline-light">
                    <i class="fas fa-arrow-left me-1"></i>返回首页
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- 显示消息 -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card shadow">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-edit me-2"></i>编辑个人资料
            </h5>
        </div>
        <div class="card-body">
            <form method="post" action="{{ url_for('profile') }}" data-skip-validation="true">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-user me-1"></i>用户名 <span class="text-danger">*</span>
                            </label>
                            <input type="text"
                                   class="form-control"
                                   id="username"
                                   name="username"
                                   value="{{ current_user.username }}"
                                   required
                                   maxlength="50">
                            <div class="form-text">用户名用于登录系统</div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-1"></i>邮箱地址 <span class="text-danger">*</span>
                            </label>
                            <input type="email"
                                   class="form-control"
                                   id="email"
                                   name="email"
                                   value="{{ current_user.email }}"
                                   required
                                   maxlength="100">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone me-1"></i>手机号码
                            </label>
                            <input type="tel"
                                   class="form-control"
                                   id="phone"
                                   name="phone"
                                   value="{{ current_user.phone or '' }}"
                                   placeholder="请输入11位手机号码"
                                   maxlength="11">
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="role" class="form-label">
                                <i class="fas fa-user-tag me-1"></i>角色
                            </label>
                            <input type="text"
                                   class="form-control"
                                   id="role"
                                   value="{{ current_user.role.name }}"
                                   disabled
                                   readonly>
                            <div class="form-text">角色由管理员分配，无法自行修改</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-1"></i>新密码
                            </label>
                            <input type="password"
                                   class="form-control"
                                   id="password"
                                   name="password"
                                   placeholder="留空则保持当前密码不变"
                                   minlength="6"
                                   maxlength="50">
                            <div class="form-text">密码长度6-50位，留空则不修改密码</div>
                        </div>
                    </div>
                </div>

                <hr>

                <div class="d-flex justify-content-between">
                    <div>
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            最后更新时间：{{ current_user.updated_at.strftime('%Y-%m-%d %H:%M:%S') if current_user.updated_at else '未知' }}
                        </small>
                    </div>
                    <div>
                        <button type="reset" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-undo me-1"></i>重置
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-1"></i>保存修改
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// 简单的页面增强，完全不干扰表单提交
document.addEventListener('DOMContentLoaded', function() {
    console.log('个人资料页面已加载');

    // 自动隐藏成功消息（3秒后）
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert-success');
        alerts.forEach(function(alert) {
            try {
                alert.style.display = 'none';
            } catch (e) {
                // 忽略错误
            }
        });
    }, 3000);
});
</script>

{% endblock %}