{% extends 'base.html' %}

{% block title %}{% if form.obj %}编辑用户{% else %}添加用户{% endif %} - CRM系统{% endblock %}

{% block content %}
<div class="container">
    <!-- 页面标题区域 -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">
                    <i class="fas fa-user-edit me-2"></i>{% if form.obj %}编辑用户{% else %}添加用户{% endif %}
                </h1>
                <p class="page-subtitle">{% if form.obj %}修改用户基本信息和权限设置{% else %}创建新用户并分配相应的角色和部门{% endif %}</p>
            </div>
            <div class="text-end">
                <div class="btn-group-theme">
                    <a href="{{ url_for('users') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回用户管理
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">用户信息</h5>
                </div>
                <div class="card-body">
                    <form method="post" id="userForm" data-skip-validation="true">
                        {{ form.csrf_token }}
                        <div class="mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control" + (" readonly" if form.obj else "")) }}
                            {% if form.username.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.username.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control") }}
                            {% if form.email.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.email.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.phone.label(class="form-label") }}
                            {{ form.phone(class="form-control", placeholder="请输入11位手机号码") }}
                            {% if form.phone.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.phone.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.password.label(class="form-label") }}
                            {{ form.password(class="form-control") }}
                            <div class="form-text">
                                密码长度为6-50个字符{% if action == 'edit' %}，留空则不修改密码{% endif %}
                            </div>
                            {% if form.password.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.password.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.company_id.label(class="form-label") }}
                            {{ form.company_id(class="form-select", onchange="loadDepartments(this.value)") }}
                            {% if form.company_id.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.company_id.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            {{ form.department_id.label(class="form-label") }}
                            {{ form.department_id(class="form-select", **{'data-selected-id': form.department_id.data if form.department_id.data else ''}) }}
                            {% if form.department_id.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.department_id.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.role_id.label(class="form-label") }}
                            {{ form.role_id(class="form-select") }}
                            {% if form.role_id.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.role_id.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="d-grid">
                            {{ form.submit(class="btn btn-theme-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
/* 获取CSRF令牌 */
const csrfToken = document.querySelector('input[name="csrf_token"]').value;
const selectedDepartmentId = document.getElementById('department_id').dataset.selectedId || null;

document.addEventListener('DOMContentLoaded', function() {
    const companySelect = document.getElementById('company_id');
    if (companySelect.value) {
        loadDepartments(companySelect.value);
    }
});

function loadDepartments(companyId) {
    if (!companyId) {
        const departmentSelect = document.getElementById('department_id');
        departmentSelect.innerHTML = '<option value="">请选择部门</option>';
        return;
    }
    
    fetch(`/organization_api/companies/${companyId}/departments`, {
        headers: {
            'X-CSRFToken': csrfToken,
            'Content-Type': 'application/json'
        },
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('加载部门失败');
        }
        return response.json();
    })
    .then(data => {
        const departmentSelect = document.getElementById('department_id');
        departmentSelect.innerHTML = '<option value="">请选择部门</option>';
        
        data.forEach(department => {
            const option = document.createElement('option');
            option.value = department.id;
            option.textContent = department.name;
            if (department.id === selectedDepartmentId) {
                option.selected = true;
            }
            departmentSelect.appendChild(option);
        });
    })
    .catch(error => {
        console.error('加载部门失败:', error);
        alert('加载部门失败，请刷新页面重试');
    });
}

/* 表单提交前验证 */
document.getElementById('userForm').addEventListener('submit', function(e) {
    const company = document.getElementById('company_id').value;
    const department = document.getElementById('department_id').value;
    const role = document.getElementById('role_id').value;
    
    if (!company || !department || !role) {
        e.preventDefault();
        alert('请填写所有必填字段');
        return false;
    }
});
</script>
{% endblock %}