from datetime import datetime, timedelta
from flask import Blueprint, render_template, redirect, url_for, request, flash, send_file, jsonify, current_app
from flask_login import login_required, current_user
from models import db, User, Lead, Activity, LeadPool, LeadStatusHistory, LeadTransferHistory, Department, Company, LeadImportRequest, Notification, LeasePlan, Setting
from sqlalchemy import or_, and_, not_
from utils.timezone_compat import get_shanghai_now
import logging
import pandas as pd
import io
import csv
import os
import json
import uuid
from werkzeug.utils import secure_filename
from logging.handlers import RotatingFileHandler
from utils.department_utils import get_child_departments
import json
import uuid
# 步骤 16: 导入所需的辅助函数
from utils.user_utils import get_company_admins
# <<< STEP 2: Add imports
from flask_mail import Message
# +++ 添加 smtplib 和 email 相关导入
import smtplib
import ssl
from email.message import EmailMessage
import traceback # <--- 添加导入

# 导入 mail 实例 (通常在 app.py 中定义并初始化)
# from app import mail # 避免循环导入，使用 current_app
from flask import current_app, render_template # render_template 用于邮件模板

# 导入优化的权限控制和查询优化
from utils.lead_permissions import (
    LeadPermissionManager,
    has_permission_to_edit,
    has_permission_to_delete,
    has_permission_to_assign,
    has_permission_to_release
)
from utils.lead_model_optimizations import lead_optimizer

app_logger = logging.getLogger(__name__)

# 创建蓝图
bp = Blueprint('leads', __name__)

# 配置日志
handler = RotatingFileHandler('logs/leads.log', maxBytes=10000, backupCount=3, delay=True)
handler.setLevel(logging.INFO)
formatter = logging.Formatter(
    '[%(asctime)s] %(levelname)s in %(module)s: %(message)s'
)
handler.setFormatter(formatter)
app_logger.addHandler(handler)
app_logger.setLevel(logging.INFO)

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'csv', 'xlsx'}

# 城市到省份的映射缓存
_city_to_province_map = None

def get_city_to_province_map():
    """获取所有城市到省份的映射字典"""
    global _city_to_province_map
    
    # 如果已经加载过映射，则直接返回缓存
    if _city_to_province_map is not None:
        return _city_to_province_map
    
    # 初始化映射字典
    _city_to_province_map = {}
    
    try:
        # 读取省份和城市数据
        json_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', 'data', 'china_regions.json')
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 构建城市到省份的映射
        for province_data in data['provinces']:
            province_name = province_data['name']
            for city in province_data['cities']:
                _city_to_province_map[city] = province_name
        
        app_logger.info(f'成功加载城市-省份映射，共 {len(_city_to_province_map)} 个城市')
    except Exception as e:
        app_logger.error(f'加载城市-省份映射失败: {str(e)}', exc_info=True)
        # 初始化一个空映射
        _city_to_province_map = {}
    
    return _city_to_province_map

def normalize_city_name(city_name):
    """标准化城市名称，自动补全"市"字"""
    if not city_name:
        return None

    city_name = city_name.strip()

    # 直辖市列表
    direct_municipalities = ["北京市", "上海市", "天津市", "重庆市"]
    direct_municipalities_short = ["北京", "上海", "天津", "重庆"]

    # 检查是否是直辖市（处理有无"市"字的情况）
    if city_name in direct_municipalities:
        return city_name
    if city_name in direct_municipalities_short:
        return city_name + "市"

    # 获取城市到省份的映射
    city_to_province = get_city_to_province_map()

    # 精确匹配
    if city_name in city_to_province:
        return city_name

    # 如果城市名不以"市"结尾，尝试添加"市"字
    if not city_name.endswith(('市', '区', '县', '盟', '州')):
        city_with_suffix = city_name + "市"
        if city_with_suffix in city_to_province:
            return city_with_suffix

    # 如果城市名以"市"结尾，尝试去掉"市"字
    if city_name.endswith('市'):
        city_without_suffix = city_name[:-1]
        if city_without_suffix in city_to_province:
            return city_without_suffix

    # 模糊匹配
    for mapped_city in city_to_province.keys():
        # 检查是否是部分匹配
        if (city_name in mapped_city or mapped_city in city_name) and abs(len(city_name) - len(mapped_city)) <= 1:
            return mapped_city

    # 如果都没有匹配，返回原始名称
    return city_name

def find_province_by_city(city_name):
    """根据城市名查找对应的省份名"""
    if not city_name:
        return None

    # 先标准化城市名称
    normalized_city = normalize_city_name(city_name)
    if not normalized_city:
        return None

    # 直辖市列表
    direct_municipalities = ["北京市", "上海市", "天津市", "重庆市"]

    # 检查是否是直辖市
    if normalized_city in direct_municipalities:
        return normalized_city

    # 获取城市到省份的映射
    city_to_province = get_city_to_province_map()

    # 精确匹配
    if normalized_city in city_to_province:
        province = city_to_province[normalized_city]
        # 如果匹配到的是直辖市下的区，直接返回直辖市名称
        if province in direct_municipalities:
            return province
        return province

    # 处理"XX区"，可能是直辖市的区
    if normalized_city.endswith('区'):
        # 检查是否是直辖市的区
        for municipality in direct_municipalities:
            # 获取直辖市下的所有区
            municipality_districts = [city for city, province in city_to_province.items()
                                     if province == municipality and city.endswith('区')]

            # 检查是否匹配直辖市的区
            for district in municipality_districts:
                if normalized_city == district or normalized_city in district or district in normalized_city:
                    return municipality

    # 前缀匹配
    for mapped_city, province in city_to_province.items():
        if normalized_city.startswith(mapped_city) or mapped_city.startswith(normalized_city):
            # 如果匹配到的是直辖市下的区，直接返回直辖市名称
            if province in direct_municipalities:
                return province
            return province

    # 没有找到匹配的省份
    return None

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def _process_xlsx_file(file_content):
    """处理xlsx文件，转换为CSV格式的StringIO对象"""
    try:
        # 使用pandas读取xlsx文件
        df = pd.read_excel(io.BytesIO(file_content), engine='openpyxl')

        # 转换为CSV格式的字符串
        csv_string = df.to_csv(index=False, encoding='utf-8')

        # 返回StringIO对象
        return io.StringIO(csv_string, newline=None)
    except Exception as e:
        app_logger.error(f'处理xlsx文件失败: {str(e)}', exc_info=True)
        return None

def mask_phone_number(phone):
    """将电话号码中间部分用星号代替，保留前3位和后3位"""
    if not phone or len(phone) < 7: return phone
    return phone[:3] + '*' * (len(phone) - 6) + phone[-3:]

def create_lead(name, company_name, email, phone, owner_id, company_id, pool_id=None, notes=None, **kwargs):
    """创建新线索"""
    try:
        # 获取状态字段的默认值
        default_status = {
            'is_called': False,
            'is_connected': False,
            'is_valid_call': False,
            'is_wechat_added': False,
            'is_intentional': False,
            'is_compliant': False,
            'is_visited': False,
            'is_car_selected': False,
            'is_deal_done': False,
            'deal_status': 'pending'
        }
        
        # 用传入的参数更新默认值
        for key, value in kwargs.items():
            if key in default_status:
                default_status[key] = value
        
        # 创建线索对象
        lead = Lead(
            name=name,
            company_name=company_name,
            email=email,
            phone=phone,
            owner_id=owner_id,
            company_id=company_id,
            original_company_id=company_id,
            pool_id=pool_id,
            is_in_public_sea=False,
            public_sea_time=None,
            is_self_created=True,
            is_called=default_status['is_called'],
            is_connected=default_status['is_connected'],
            is_valid_call=default_status['is_valid_call'],
            is_wechat_added=default_status['is_wechat_added'],
            is_intentional=default_status['is_intentional'],
            is_compliant=default_status['is_compliant'],
            is_visited=default_status['is_visited'],
            is_car_selected=default_status['is_car_selected'],
            is_deal_done=default_status['is_deal_done'],
            deal_status=default_status['deal_status']
        )
        
        db.session.add(lead)
        db.session.flush()
        
        # 添加活动记录
        if notes:
            activity = Activity(
                lead_id=lead.id,
                user_id=owner_id,
                description=f'创建了新线索 {name}，备注：{notes}'
            )
            db.session.add(activity)
        
        db.session.commit()
        return lead
    except Exception as e:
        db.session.rollback()
        app_logger.error(f'创建线索失败: {str(e)}', exc_info=True)
        raise

def update_lead(lead_id, current_user, **kwargs):
    """更新线索信息"""
    try:
        lead = Lead.query.get_or_404(lead_id)
        
        # 权限检查应该在调用此函数之前由路由处理，但可以加一道保险
        if not has_permission_to_edit(lead, current_user): # 使用我们刚修改的权限函数
            app_logger.warning(f"更新线索 {lead.id} 失败：用户 {current_user.id} 没有编辑权限 (在update_lead内再次检查)")
            raise PermissionError('您没有权限编辑此线索')

        # 针对 B 员工修改其认领的异地线索的字段级权限控制 (任务 6.1.1)
        is_editing_cross_lead_by_assignee = False
        if lead.owner_id == current_user.id and \
           lead.current_processing_company_id == current_user.company_id and \
           lead.company_id != current_user.company_id:
            is_editing_cross_lead_by_assignee = True
            
            allowed_fields_for_cross_lead = {
                'is_called',             # 是否已联系
                'is_connected',          # 是否接通
                'is_valid_call',         # 是否有效沟通
                'is_wechat_added',       # 是否添加微信
                'is_intentional',        # 是否意向客户
                'is_visited',            # 是否到面
                'is_compliant',          # 是否合规
                'is_car_selected',       # 是否提车
                'is_deal_done',          # 是否签约/签约
                'notes'                  # 备注
            }
            
            unauthorized_fields_attempted = []
            # owner_id 将单独检查，因为它有特殊的转移逻辑
            # 我们需要检查 kwargs 中除了 owner_id 之外，是否有其他不允许的字段
            for key in kwargs.keys():
                if key not in allowed_fields_for_cross_lead and key != 'owner_id':
                    unauthorized_fields_attempted.append(key)
            
            if unauthorized_fields_attempted:
                error_message = f"您无权修改异地线索的以下字段: {', '.join(unauthorized_fields_attempted)}。请只更新允许的状态和备注。"
                app_logger.warning(f"更新线索 {lead.id} 失败：B公司员工 {current_user.id} 尝试修改异地线索的受限字段: {unauthorized_fields_attempted}")
                raise PermissionError(error_message)

            # 单独处理 owner_id 字段的权限，如果它在kwargs中被提供了
            if 'owner_id' in kwargs and kwargs['owner_id'] is not None:
                # 检查是否尝试修改负责人，并且新负责人是否为本公司同事
                if kwargs['owner_id'] != lead.owner_id: # 只有在负责人确实发生变化时才校验
                    new_owner_check = User.query.get(kwargs['owner_id'])
                    if not new_owner_check or new_owner_check.company_id != current_user.company_id:
                        app_logger.warning(f"更新线索 {lead.id} 失败：B公司员工 {current_user.id} 尝试将异地线索负责人修改为非本公司人员 {kwargs['owner_id']}")
                        raise PermissionError('异地线索只能在处理公司内部转移负责人。')
            # 如果代码执行到这里，说明所有尝试修改的字段（除了可能变化的owner_id）都在允许范围内
            # 或者只修改了owner_id且通过了上述检查 (虽然owner_id通常不由普通编辑表单修改)

        # 阻止A公司员工修改已推送的异地线索的任何字段 (任务 6.1.2)
        # 这个逻辑实际上由 has_permission_to_edit 控制，这里是双重保险或更细致的错误
        if not is_editing_cross_lead_by_assignee: # 如果不是B员工在编辑异地单
            if lead.current_processing_company_id and lead.current_processing_company_id != current_user.company_id and lead.company_id == current_user.company_id:
                app_logger.warning(f"更新线索 {lead.id} 失败：A公司员工 {current_user.id} 尝试修改已推送的线索。")
                raise PermissionError('线索已被推送到其他公司处理，您不能再修改。请先撤回。')
            if lead.pool_id and not is_editing_cross_lead_by_assignee: # 避免B员工编辑自己池子里的线索被这条拦住
                 lead_pool_check = LeadPool.query.get(lead.pool_id)
                 if lead_pool_check and lead_pool_check.pool_type == 'CROSS_LOCATION_PENDING' and lead_pool_check.company_id != current_user.company_id and lead.company_id == current_user.company_id:
                      app_logger.warning(f"更新线索 {lead.id} 失败：A公司员工 {current_user.id} 尝试修改在其他公司异地池中的线索。")
                      raise PermissionError('线索已在其他公司异地池中，您不能再修改。请先撤回。')

        # 记录原始状态
        old_states = {
            'is_called': lead.is_called,
            'is_connected': lead.is_connected,
            'is_valid_call': lead.is_valid_call,
            'is_wechat_added': lead.is_wechat_added,
            'is_intentional': lead.is_intentional,
            'is_compliant': lead.is_compliant,
            'is_visited': lead.is_visited,
            'is_car_selected': lead.is_car_selected,
            'is_deal_done': lead.is_deal_done
        }
        
        # 状态字段的顺序列表，从前到后（与线索详情页面和首页显示顺序一致）
        status_fields_order = [
            'is_called',
            'is_connected',
            'is_valid_call',
            'is_wechat_added',
            'is_intentional',
            'is_visited',
            'is_compliant',
            'is_deal_done',
            'is_car_selected'
        ]
        
        # 处理状态级联更新（如果后面的状态是True，则前面的状态也必须是True）
        for field in status_fields_order:
            if field in kwargs and kwargs[field] == True:
                # 如果当前字段被设置为True，则确保之前所有字段也为True
                for prev_field in status_fields_order:
                    if prev_field == field:
                        break
                    kwargs[prev_field] = True
        
        # 更新基本信息
        for key, value in kwargs.items():
            if hasattr(lead, key):
                setattr(lead, key, value)
        
        # --- 处理租赁计划 --- 
        is_deal_done_final = getattr(lead, 'is_deal_done', False)

        if is_deal_done_final:
            term_months_str = request.form.get('lease_term_months')
            free_rent_days_str = request.form.get('lease_free_rent_days')
            custom_notes = request.form.get('lease_custom_notes')

            # 只有当表单中确实提交了租赁计划相关字段时才进行处理
            # (避免在非is_deal_done的表单提交中意外创建空的LeasePlan)
            # 或者，如果is_deal_done为True，即使表单字段为空，也应该尝试创建或更新LeasePlan为空状态
            # 当前逻辑：如果签约了，就尝试获取或创建LeasePlan记录并更新
            lease_plan = LeasePlan.query.filter_by(lead_id=lead.id).first()
            if not lease_plan:
                lease_plan = LeasePlan(lead_id=lead.id)
                db.session.add(lease_plan)
            
            lease_plan.term_months = int(term_months_str) if term_months_str and term_months_str.isdigit() else None
            lease_plan.free_rent_days = int(free_rent_days_str) if free_rent_days_str and free_rent_days_str.isdigit() else 0
            lease_plan.custom_notes = custom_notes
        else:
            # 如果线索未签约，删除关联的租赁计划 (如果存在)
            existing_lease_plan = LeasePlan.query.filter_by(lead_id=lead.id).first()
            if existing_lease_plan:
                db.session.delete(existing_lease_plan)
        # --- 租赁计划处理结束 --- 
        
        # 检查状态变更并添加历史记录
        status_changes = []
        for field, old_value in old_states.items():
            new_value = getattr(lead, field)
            if old_value != new_value:
                status_history = LeadStatusHistory(
                    lead_id=lead.id,
                    status_field=field,
                    old_value=old_value,
                    new_value=new_value,
                    created_by=current_user.id
                )
                db.session.add(status_history)
                status_changes.append(field)
        
        # 记录活动
        if status_changes:
            status_field_to_chinese_map = {
                'is_called': '已拨电话',
                'is_connected': '接通',
                'is_valid_call': '有效通话',
                'is_wechat_added': '添加微信',
                'is_intentional': '意向客户',
                'is_compliant': '信息合规',
                'is_visited': '确认到面',
                'is_car_selected': '完成提车',
                'is_deal_done': '完成签约'
            }
            translated_changes = [status_field_to_chinese_map.get(field, field.replace('is_', '')) for field in status_changes]
            changes_desc = '、'.join(translated_changes)
            activity = Activity(
                lead_id=lead.id,
                user_id=current_user.id,
                description=f'更新了线索状态：{changes_desc}'
            )
        else:
            # 如果没有状态变更，但租赁计划信息被修改了，也应该记录活动
            # (这个判断可以更精确，例如比较旧的租赁计划值和新的值)
            if is_deal_done_final and (request.form.get('lease_term_months') or request.form.get('lease_free_rent_days') or request.form.get('lease_custom_notes')):
                 activity_desc = '更新了线索基本信息和/或租赁计划详情'
            else:
                 activity_desc = '更新了线索基本信息'
            activity = Activity(lead_id=lead.id, user_id=current_user.id, description=activity_desc)
        db.session.add(activity)
        
        db.session.commit()
        return lead
    except PermissionError: # Re-raise permission errors to be caught by Flask handler
        raise
    except Exception as e:
        db.session.rollback()
        app_logger.error(f'更新线索失败: {str(e)}', exc_info=True)
        raise

def delete_lead(lead_id, current_user):
    """删除线索"""
    try:
        lead = Lead.query.get_or_404(lead_id)
        
        # 检查权限
        if not has_permission_to_delete(lead, current_user):
            raise PermissionError('您没有权限删除此线索')
        
        # 删除相关的活动记录
        Activity.query.filter_by(lead_id=lead_id).delete()
        
        # 删除相关的状态历史记录
        LeadStatusHistory.query.filter_by(lead_id=lead_id).delete()
        
        # 删除相关的转移历史记录
        LeadTransferHistory.query.filter_by(lead_id=lead_id).delete()
        
        # 删除线索
        db.session.delete(lead)
        db.session.commit()
        return True
    except Exception as e:
        db.session.rollback()
        app_logger.error(f'删除线索失败: {str(e)}', exc_info=True)
        raise

def assign_lead(lead_id, new_owner_id, current_user, notes=None, assignment_type='same_company', target_company_id=None):
    """分配线索"""
    try:
        lead = Lead.query.get_or_404(lead_id)
        
        # 检查权限
        if not has_permission_to_assign(lead, current_user):
            raise PermissionError('您没有权限分配此线索')
        
        # 记录原负责人和公司信息
        old_owner = lead.owner.username if lead.owner else '无'
        old_company = lead.owner.company.name if lead.owner and lead.owner.company else '无'
        
        # 更新负责人
        if new_owner_id:
            new_owner = User.query.get(int(new_owner_id))
            if not new_owner:
                raise ValueError('选择的用户不存在')
            
            # 检查新负责人是否在允许分配的范围内
            if not is_valid_assignee(new_owner, current_user, assignment_type, target_company_id):
                raise PermissionError('无权分配给该用户')
            
            lead.owner_id = new_owner.id
            lead.company_id = new_owner.company_id
            new_owner_name = new_owner.username
            new_company = new_owner.company.name if new_owner.company else '无'
        else:
            lead.owner_id = None
            new_owner_name = '无'
            new_company = '无'
        
        # 添加活动记录
        activity = Activity(
            lead_id=lead.id,
            user_id=current_user.id,
            description=f'将线索从 {old_owner}（{old_company}）分配给 {new_owner_name}（{new_company}）' + 
                      (f'，备注：{notes}' if notes else '')
        )
        db.session.add(activity)
        
        db.session.commit()
        return lead
    except Exception as e:
        db.session.rollback()
        app_logger.error(f'分配线索失败: {str(e)}', exc_info=True)
        raise

def release_lead_to_pool(lead_id, current_user):
    """释放线索到相应的池 (公海 或 异地待认领池)"""
    try:
        lead = Lead.query.get_or_404(lead_id)
        
        # 记录开始释放的调试信息
        app_logger.info(f'开始释放线索 - 线索ID:{lead_id}, 当前用户:{current_user.username}')
        app_logger.info(f'线索当前状态 - owner_id:{lead.owner_id}, is_in_public_sea:{lead.is_in_public_sea}, pool_id:{lead.pool_id}, company_id:{lead.company_id}, processing_company:{lead.current_processing_company_id}')
        
        # 检查权限 (使用已有的 release 权限函数)
        if not has_permission_to_release(lead, current_user):
            app_logger.warning(f'用户{current_user.username}没有权限释放线索{lead_id}')
            raise PermissionError('您没有权限释放此线索')
            
        # --- 检查是否为B员工释放其认领的异地线索 --- 
        is_releasing_cross_lead = False
        target_pool = None
        transfer_type = 'release' # 默认是释放到公海

        if lead.owner_id == current_user.id and \
           lead.current_processing_company_id == current_user.company_id and \
           lead.company_id != current_user.company_id:
            is_releasing_cross_lead = True
            app_logger.info(f"检测到用户 {current_user.username} 正在释放其认领的异地线索 {lead.id}")
            # 目标应该是本公司 (B公司) 的 CROSS_LOCATION_PENDING 池
            target_pool = LeadPool.query.filter_by(
                company_id=current_user.company_id,
                pool_type='CROSS_LOCATION_PENDING'
            ).first()
            if not target_pool:
                app_logger.error(f"释放异地线索 {lead.id} 失败：未找到公司 {current_user.company.name} 的异地到店线索池")
                raise ValueError(f'未找到您公司的异地到店线索池，无法释放')
            transfer_type = 'RELEASE_TO_CROSS_PENDING_POOL_BY_ASSIGNEE' # 使用新的转移类型
            app_logger.info(f"异地线索 {lead.id} 将被释放回异地到店池: {target_pool.name} (ID: {target_pool.id})")
        
        # --- 如果不是释放异地线索，则按原逻辑查找公海池 --- 
        if not is_releasing_cross_lead:
            app_logger.info(f"线索 {lead.id} 为普通线索，将释放到公海池")
            # 目标是本公司 (A公司或B公司自己的线索) 的公海池
            target_pool = LeadPool.query.filter_by(company_id=current_user.company_id, is_public_sea=True).first()
            if not target_pool:
                # 检查线索是否属于当前用户公司，避免跨公司释放到不存在的公海
                if lead.company_id != current_user.company_id:
                     app_logger.error(f"释放线索 {lead.id} 失败：线索公司 {lead.company_id} 与用户公司 {current_user.company_id} 不符，且未找到用户公司的公海池")
                     raise ValueError('无法释放不属于本公司的线索到公海')
                app_logger.error(f'未找到公司 {current_user.company_id} 的公海池')
                raise ValueError('公海池不存在，请联系管理员配置')
            app_logger.info(f"找到公海池 - ID:{target_pool.id}, 名称:{target_pool.name}")
            transfer_type = 'release' # 确认转移类型
        
        # 记录转移历史
        history_notes = '释放到异地到店池' if is_releasing_cross_lead else '释放到公海'
        transfer_history = LeadTransferHistory(
            lead_id=lead.id,
            from_user_id=lead.owner_id,
            to_pool_id=target_pool.id, # 指向目标池 (公海或异地池)
            transfer_type=transfer_type,
            created_by=current_user.id,
            notes=history_notes,
            # 如果是释放异地线索，处理公司不变 (仍是B)
            target_company_id_for_processing=lead.current_processing_company_id if is_releasing_cross_lead else None 
        )
        db.session.add(transfer_history)
        
        # 添加活动记录
        activity_desc = '将线索释放回异地到店池' if is_releasing_cross_lead else '将线索释放到公海'
        activity = Activity(
            lead_id=lead.id,
            user_id=current_user.id,
            description=activity_desc
        )
        db.session.add(activity)
        
        # 更新线索状态
        lead.last_owner_id = lead.owner_id  # 记录上一个负责人
        lead.owner_id = None  # 清除负责人
        lead.is_in_public_sea = (not is_releasing_cross_lead) # 只有释放到公海才标记 is_in_public_sea
        lead.public_sea_time = get_shanghai_now() if (not is_releasing_cross_lead) else None # 记录进入公海时间
        lead.pool_id = target_pool.id  # 设置所属目标池ID
        # lead.company_id 保持不变 (A公司或B公司自己的)
        # lead.current_processing_company_id 在释放异地单时保持B公司，在释放普通单到公海时应该清空或设为None？
        # 如果是普通单释放到公海，意味着处理权不再固定，设为None可能更合适
        if not is_releasing_cross_lead:
             lead.current_processing_company_id = None
        
        # 清除保护期相关字段 (适用于两种释放情况)
        lead.protection_start_time = None
        lead.protection_end_time = None
        
        # 记录更新后的状态
        app_logger.info(f'释放线索后状态 - ID:{lead.id}, owner_id:{lead.owner_id}, pool_id:{lead.pool_id}, is_public_sea:{lead.is_in_public_sea}, processing_company:{lead.current_processing_company_id}')
        
        db.session.commit()
        app_logger.info(f'线索 {lead.id} 已成功释放到池 {target_pool.name}')
        return lead
    except Exception as e:
        db.session.rollback()
        app_logger.error(f'释放线索失败: {str(e)}', exc_info=True)
        raise

def has_permission_to_view_and_recall(lead, current_user):
    """检查是否有权限查看和撤回线索（用于已推送的异地线索）"""
    if not lead or not current_user or not hasattr(current_user, 'role') or not current_user.role:
        return False

    # 1. 超级管理员有所有权限
    if current_user.role.code == 'super_admin':
        return True

    # 2. 线索的原始公司员工可以查看和撤回自己推送的线索
    if lead.company_id == current_user.company_id:
        return True

    return False

def has_permission_to_push(lead, current_user):
    """检查是否有权限推送线索到异地"""
    if not lead or not current_user or not hasattr(current_user, 'role') or not current_user.role:
        return False

    # 1. 超级管理员有所有权限
    if current_user.role.code == 'super_admin':
        return True

    # 2. 线索必须属于用户所在公司
    if lead.company_id != current_user.company_id:
        return False

    # 3. 公司管理员可以推送任何本公司线索
    if current_user.role.code == 'company_admin':
        return True

    # 4. 线索负责人可以推送自己的线索
    if lead.owner_id == current_user.id:
        return True

    # 5. 部门管理员可以推送本部门的线索
    if (current_user.role.code == 'department_admin' and
        lead.owner_id and
        hasattr(current_user, 'department_id')):
        lead_owner = User.query.get(lead.owner_id)
        if lead_owner and lead_owner.department_id == current_user.department_id:
            return True

    return False

# 注意：has_permission_to_edit 函数已从 utils.lead_permissions 导入，不在此处重复定义

def has_permission_to_delete(lead, current_user):
    """检查是否有权限删除线索"""
    if not lead or not current_user or not hasattr(current_user, 'role') or not current_user.role:
        return False # Basic safety check

    # 1. 超级管理员有所有权限
    if current_user.role.code == 'super_admin':
        return True

    # 2. B公司员工不能删除其认领的异地线索
    if lead.owner_id == current_user.id and \
       lead.current_processing_company_id == current_user.company_id and \
       lead.company_id != current_user.company_id:
        app_logger.debug(f"权限拒绝 (删除): 用户 {current_user.id} (公司 {current_user.company_id}) 尝试删除其认领的异地线索 {lead.id} (原属公司 {lead.company_id})")
        return False

    # 3. A公司员工不能删除已推送至其他公司处理的线索
    if lead.current_processing_company_id and lead.current_processing_company_id != current_user.company_id:
        if lead.company_id == current_user.company_id: # 确认是A公司员工
             app_logger.debug(f"权限拒绝 (删除): 用户 {current_user.id} ({current_user.company.name}) 尝试删除已推送至公司 {lead.current_processing_company_id} 的线索 {lead.id}")
             return False
    if lead.pool_id:
        lead_pool = LeadPool.query.get(lead.pool_id)
        if lead_pool and lead_pool.pool_type == 'CROSS_LOCATION_PENDING' and lead_pool.company_id != current_user.company_id:
            if lead.company_id == current_user.company_id: # 确认是A公司员工
                app_logger.debug(f"权限拒绝 (删除): 用户 {current_user.id} ({current_user.company.name}) 尝试删除在公司 {lead_pool.company_id} 异地池中的线索 {lead.id}")
                return False

    # 4. 现有权限逻辑 (适用于本公司内的线索)
    # 只允许对本公司的线索进行删除操作，且符合角色权限
    if lead.company_id == current_user.company_id: 
        if current_user.role.code == 'company_admin':
            lead_owner = User.query.get(lead.owner_id) if lead.owner_id else None
            can_delete = lead_owner is None or lead_owner.company_id == current_user.company_id
            if can_delete: app_logger.debug(f"权限允许 (删除): 公司管理员 {current_user.id} 删除公司线索 {lead.id}")
            return can_delete
        elif current_user.role.code == 'department_admin':
            lead_owner = User.query.get(lead.owner_id) if lead.owner_id else None
            if lead_owner is None and lead.company_id == current_user.company_id:
                app_logger.debug(f"权限允许 (删除): 部门管理员 {current_user.id} 删除未分配公司线索 {lead.id}")
                return True
            if lead_owner and lead_owner.company_id == current_user.company_id:
                department_ids = get_child_departments(current_user.department_id)
                can_delete = lead_owner.department_id in department_ids
                if can_delete: app_logger.debug(f"权限允许 (删除): 部门管理员 {current_user.id} 删除部门线索 {lead.id}")
                return can_delete
            app_logger.debug(f"权限拒绝 (删除): 部门管理员 {current_user.id} 对线索 {lead.id} (负责人: {lead_owner.id if lead_owner else 'N/A'})")
            return False
        else: # 普通用户通常不允许直接删除线索，除非是自己的且未分配或有特殊配置
              # 按照现有逻辑，普通用户不能删除，除非是自己的
            can_delete = lead.owner_id == current_user.id
            if can_delete: app_logger.debug(f"权限允许 (删除): 用户 {current_user.id} 删除自己的线索 {lead.id} (需谨慎，通常不建议)")
            return can_delete # 注意：这里允许普通用户删除自己的线索，可能需要根据业务调整

    app_logger.debug(f"权限拒绝 (删除): 用户 {current_user.id} 对线索 {lead.id} (公司 {lead.company_id} vs 用户公司 {current_user.company_id}) - 未匹配任何规则")
    return False

def has_permission_to_assign(lead, current_user):
    """检查是否有权限分配线索"""
    if current_user.role.code == 'super_admin':
        return True
    elif current_user.role.code == 'company_admin':
        lead_owner = User.query.get(lead.owner_id) if lead.owner_id else None
        return lead_owner is None or lead_owner.company_id == current_user.company_id
    elif current_user.role.code == 'department_admin':
        lead_owner = User.query.get(lead.owner_id) if lead.owner_id else None
        if lead_owner is None:
            return True
            
        # 获取部门管理员管理的所有子部门ID
        department_ids = get_child_departments(current_user.department_id)
        return lead_owner.department_id in department_ids
    else:
        return lead.owner_id == current_user.id

def has_permission_to_release(lead, current_user):
    """检查是否有权限释放线索"""
    if current_user.role.code in ['super_admin', 'company_admin', 'department_admin']:
        return True
    return lead.owner_id == current_user.id

def is_valid_assignee(new_owner, current_user, assignment_type='same_company', target_company_id=None):
    """检查新负责人是否在允许分配的范围内"""
    from utils.department_utils import get_child_departments

    if current_user.role.code == 'super_admin':
        return True
    elif current_user.role.code == 'company_admin':
        # 如果是跨公司分配，检查目标用户是否属于指定的目标公司
        if assignment_type == 'cross_company' and target_company_id:
            return new_owner.company_id == int(target_company_id)
        # 否则只能分配给同公司用户
        return new_owner.company_id == current_user.company_id
    elif current_user.role.code == 'department_admin':
        # 部门管理员不支持跨公司分配，只能在本部门内分配
        if assignment_type == 'cross_company':
            return False
        # 获取管理员的所有子部门ID
        department_ids = get_child_departments(current_user.department_id)
        # 检查目标用户是否在管理员的部门或子部门中
        return new_owner.department_id in department_ids
    return False

def batch_assign_leads(lead_ids, user_id, current_user):
    """批量分配线索给指定用户"""
    try:
        from utils.department_utils import get_child_departments
        
        user = User.query.get(user_id)
        if not user:
            raise ValueError('找不到目标用户')
            
        # 检查权限
        if not is_valid_assignee(user, current_user):
            raise PermissionError('您没有权限将线索分配给此用户')
            
        success_count = 0
        for lead_id in lead_ids:
            try:
                lead_id = int(lead_id) if lead_id else None
                lead = Lead.query.get(lead_id)
                
                # 根据用户角色检查分配权限
                has_permission = False
                
                if current_user.role.code == 'super_admin':
                    has_permission = True
                elif current_user.role.code == 'company_admin':
                    has_permission = lead.company_id == current_user.company_id
                elif current_user.role.code == 'department_admin':
                    # 获取管理员的所有子部门ID
                    department_ids = get_child_departments(current_user.department_id)
                    # 检查线索所有者是否在管理范围内
                    lead_owner = User.query.get(lead.owner_id) if lead.owner_id else None
                    has_permission = lead_owner is None or (lead_owner.department_id in department_ids)
                else:
                    has_permission = lead.owner_id == current_user.id
                
                if lead and has_permission:
                    # 记录转移历史
                    transfer_history = LeadTransferHistory(
                        lead_id=lead.id,
                        from_pool_id=lead.pool_id,
                        to_pool_id=None,
                        from_user_id=lead.owner_id,
                        to_user_id=user_id,
                        transfer_type='assign',
                        created_by=current_user.id,
                        notes='批量分配'
                    )
                    db.session.add(transfer_history)
                    
                    old_owner = lead.owner_id
                    
                    # 更新线索状态
                    lead.owner_id = user_id
                    lead.company_id = user.company_id
                    lead.pool_id = None
                    lead.is_in_public_sea = False
                    
                    # 添加活动记录
                    activity = Activity(
                        lead_id=lead.id,
                        user_id=current_user.id,
                        description=f'将线索从{User.query.get(old_owner).username if old_owner else "未分配"}分配给{user.username}'
                    )
                    db.session.add(activity)
                    success_count += 1
                else:
                    app_logger.warning(f'用户{current_user.username}没有权限分配线索{lead_id}')
            except Exception as e:
                app_logger.error(f'分配线索 {lead_id} 失败: {str(e)}')
                continue
        
        db.session.commit()
        return success_count
    except Exception as e:
        db.session.rollback()
        app_logger.error(f'批量分配线索失败: {str(e)}', exc_info=True)
        raise

def batch_release_to_pool(lead_ids, current_user):
    """批量释放线索到公海"""
    try:
        # 获取公海池
        public_sea = LeadPool.query.filter_by(company_id=current_user.company_id, is_public_sea=True).first()
        if not public_sea:
            app_logger.error(f'未找到公司{current_user.company_id}的公海池')
            raise ValueError('公海池不存在，请联系管理员配置公海池')
            
        app_logger.info(f'找到公海池 - ID:{public_sea.id}, 名称:{public_sea.name}')
        
        success_count = 0
        for lead_id in lead_ids:
            try:
                lead = Lead.query.get(int(lead_id))
                # 检查权限
                if lead and (current_user.role.code in ['super_admin', 'company_admin', 'department_admin'] or lead.owner_id == current_user.id):
                    # 记录原始状态
                    app_logger.info(f'批量释放线索前状态 - ID:{lead.id}, owner_id:{lead.owner_id}, is_in_public_sea:{lead.is_in_public_sea}, pool_id:{lead.pool_id}, company_id:{lead.company_id}')

                    # 检查是否为异地线索
                    is_cross_location_lead = (
                        lead.owner_id == current_user.id and
                        lead.current_processing_company_id == current_user.company_id and
                        lead.company_id != current_user.company_id
                    )

                    if is_cross_location_lead:
                        # 异地线索释放到异地池
                        app_logger.info(f'检测到异地线索 {lead.id}，将释放到异地池而非公海')

                        # 获取异地池
                        cross_location_pool = LeadPool.query.filter_by(
                            company_id=current_user.company_id,
                            pool_type='CROSS_LOCATION_PENDING'
                        ).first()

                        if not cross_location_pool:
                            app_logger.error(f"释放异地线索 {lead.id} 失败：未找到公司 {current_user.company_id} 的异地池")
                            continue

                        # 记录转移历史
                        transfer_history = LeadTransferHistory(
                            lead_id=lead.id,
                            from_user_id=lead.owner_id,
                            to_pool_id=cross_location_pool.id,
                            transfer_type='RELEASE_TO_CROSS_PENDING_POOL_BY_ASSIGNEE',
                            created_by=current_user.id,
                            notes='批量释放异地线索到异地池',
                            target_company_id_for_processing=lead.current_processing_company_id
                        )
                        db.session.add(transfer_history)

                        # 添加活动记录
                        activity = Activity(
                            lead_id=lead.id,
                            user_id=current_user.id,
                            description='将异地线索释放回异地池'
                        )
                        db.session.add(activity)

                        # 更新线索状态
                        lead.last_owner_id = lead.owner_id
                        lead.owner_id = None
                        lead.is_in_public_sea = False  # 异地线索不进入公海
                        lead.public_sea_time = None
                        lead.pool_id = cross_location_pool.id
                        # current_processing_company_id 保持不变

                    else:
                        # 普通线索释放到公海
                        # 记录转移历史
                        transfer_history = LeadTransferHistory(
                            lead_id=lead.id,
                            from_user_id=lead.owner_id,
                            to_user_id=None,
                            transfer_type='release',
                            created_by=current_user.id,
                            notes='批量释放到公海'
                        )
                        db.session.add(transfer_history)

                        # 添加活动记录
                        activity = Activity(
                            lead_id=lead.id,
                            user_id=current_user.id,
                            description='将线索释放到公海'
                        )
                        db.session.add(activity)

                        # 更新线索状态
                        lead.last_owner_id = lead.owner_id  # 记录上一个负责人
                        lead.owner_id = None  # 清除负责人
                        lead.is_in_public_sea = True  # 标记为在公海中
                        lead.public_sea_time = get_shanghai_now()  # 设置进入公海时间
                        lead.pool_id = public_sea.id  # 设置所属公海池
                        lead.company_id = current_user.company_id  # 确保线索属于当前公司
                        lead.current_processing_company_id = None  # 清除处理公司

                        # 清除保护期相关字段
                        lead.protection_start_time = None
                        lead.protection_end_time = None

                    # 记录更新后的状态
                    app_logger.info(f'批量释放线索后状态 - ID:{lead.id}, owner_id:{lead.owner_id}, is_in_public_sea:{lead.is_in_public_sea}, pool_id:{lead.pool_id}, company_id:{lead.company_id}')

                    success_count += 1
                else:
                    app_logger.warning(f'用户{current_user.username}没有权限释放线索{lead_id}')
            except Exception as e:
                app_logger.error(f'释放线索 {lead_id} 失败: {str(e)}')
                continue
            
        db.session.commit()
        app_logger.info(f'批量释放完成，成功释放 {success_count} 条线索到公海')
        return success_count
    except Exception as e:
        db.session.rollback()
        app_logger.error(f'批量释放线索失败: {str(e)}', exc_info=True)
        raise

def export_leads(current_user, export_format='csv', filters=None):
    """导出线索数据 - 支持多种格式"""
    try:
        # 检查权限
        if not current_user.has_permission('export_leads'):
            raise PermissionError('您没有权限导出线索')

        # 使用优化的查询构建器
        query = lead_optimizer.get_optimized_leads_query(current_user, filters)

        # 获取符合条件的线索
        leads = query.all()
        app_logger.info(f'导出线索: 找到{len(leads)}条线索')

        if export_format.lower() == 'excel':
            return _export_to_excel(leads)
        elif export_format.lower() == 'json':
            return _export_to_json(leads)
        else:
            return _export_to_csv(leads)

    except Exception as e:
        app_logger.error(f'导出线索失败: {str(e)}', exc_info=True)
        raise

def _export_to_csv(leads):
    """导出为CSV格式"""
    output = io.StringIO()
    writer = csv.writer(output)

    # 写入表头
    headers = [
        '姓名', '省份', '城市', '电话', '已拨电话', '已接通', '有效通话',
        '已加微信', '意向客户', '已到面', '信息合规', '已签约', '已提车',
        '负责人', '所属公司', '创建时间', '最后更新', '备注'
    ]
    writer.writerow(headers)

    # 写入数据
    for lead in leads:
        writer.writerow([
            lead.name,
            lead.company_name,
            lead.email,
            lead.phone,
            '是' if lead.is_called else '否',
            '是' if lead.is_connected else '否',
            '是' if lead.is_valid_call else '否',
            '是' if lead.is_wechat_added else '否',
            '是' if lead.is_intentional else '否',
            '是' if lead.is_visited else '否',
            '是' if lead.is_compliant else '否',
            '是' if lead.is_deal_done else '否',
            '是' if lead.is_car_selected else '否',
            lead.owner.username if lead.owner else '未分配',
            lead.company.name if lead.company else '未知公司',
            lead.created_at.strftime('%Y-%m-%d %H:%M:%S') if lead.created_at else '',
            lead.updated_at.strftime('%Y-%m-%d %H:%M:%S') if lead.updated_at else '',
            lead.notes or ''
        ])

    output.seek(0)
    return output

def _export_to_excel(leads):
    """导出为Excel格式"""
    try:
        import pandas as pd
        from io import BytesIO

        # 准备数据
        data = []
        for lead in leads:
            data.append({
                '姓名': lead.name,
                '省份': lead.company_name,
                '城市': lead.email,
                '电话': lead.phone,
                '已拨电话': '是' if lead.is_called else '否',
                '已接通': '是' if lead.is_connected else '否',
                '有效通话': '是' if lead.is_valid_call else '否',
                '已加微信': '是' if lead.is_wechat_added else '否',
                '意向客户': '是' if lead.is_intentional else '否',
                '已到面': '是' if lead.is_visited else '否',
                '信息合规': '是' if lead.is_compliant else '否',
                '已签约': '是' if lead.is_deal_done else '否',
                '已提车': '是' if lead.is_car_selected else '否',
                '负责人': lead.owner.username if lead.owner else '未分配',
                '所属公司': lead.company.name if lead.company else '未知公司',
                '创建时间': lead.created_at.strftime('%Y-%m-%d %H:%M:%S') if lead.created_at else '',
                '最后更新': lead.updated_at.strftime('%Y-%m-%d %H:%M:%S') if lead.updated_at else '',
                '备注': lead.notes or ''
            })

        # 创建DataFrame
        df = pd.DataFrame(data)

        # 导出到Excel
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='线索数据', index=False)

            # 设置列宽
            worksheet = writer.sheets['线索数据']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        output.seek(0)
        return output

    except ImportError:
        app_logger.warning('pandas或openpyxl未安装，回退到CSV格式')
        return _export_to_csv(leads)

def _export_to_json(leads):
    """导出为JSON格式"""
    data = []
    for lead in leads:
        data.append({
            'id': lead.id,
            'name': lead.name,
            'company_name': lead.company_name,
            'email': lead.email,
            'phone': mask_phone_number(lead.phone) if lead.phone else '',  # 脱敏处理
            'stages': {
                'is_called': lead.is_called,
                'is_connected': lead.is_connected,
                'is_valid_call': lead.is_valid_call,
                'is_wechat_added': lead.is_wechat_added,
                'is_intentional': lead.is_intentional,
                'is_visited': lead.is_visited,
                'is_compliant': lead.is_compliant,
                'is_deal_done': lead.is_deal_done,
                'is_car_selected': lead.is_car_selected
            },
            'owner': {
                'id': lead.owner.id if lead.owner else None,
                'username': lead.owner.username if lead.owner else None
            },
            'company': {
                'id': lead.company.id if lead.company else None,
                'name': lead.company.name if lead.company else None
            },
            'created_at': lead.created_at.isoformat() if lead.created_at else None,
            'updated_at': lead.updated_at.isoformat() if lead.updated_at else None,
            'notes': lead.notes
        })

    output = io.StringIO()
    import json
    json.dump(data, output, ensure_ascii=False, indent=2)
    output.seek(0)
    return output

@bp.route('/import-leads', methods=['POST'])
@login_required
def import_leads():
    # 权限检查
    if not current_user.has_permission('lead_import'):
        flash('您没有权限执行此操作', 'danger')
        return redirect(url_for('leads.leads_unified'))

    # 修正：文件检查应该在权限检查之后，并且对两种导入目标都适用
    if 'file' not in request.files:
        flash('请选择要导入的文件', 'danger')
        return redirect(url_for('leads.leads_unified'))

    file = request.files['file']
    if file.filename == '':
        flash('未选择文件', 'danger')
        return redirect(url_for('leads.leads_unified'))

    if not allowed_file(file.filename):
        flash('不支持的文件类型，请上传CSV或XLSX文件', 'danger')
        return redirect(url_for('leads.leads_unified'))
        
    import_target = request.form.get('import_target', 'my_company')

    try:
        if import_target == 'my_company':
            # --- 导入到本公司逻辑 --- 
            # 读取文件内容并解码
            file_content = file.read()
            if not file_content:
                flash('文件为空', 'danger')
                return redirect(url_for('leads.leads_unified'))

            file_stream = _process_file_content(file_content, file.filename)
            if file_stream is None:
                flash('无法处理文件，请确保文件格式正确', 'danger')
                return redirect(url_for('leads.leads_unified'))

            # 检查CSV格式
            if not _validate_csv_format(file_stream):
                flash('文件格式错误或为空', 'danger')
                return redirect(url_for('leads.leads_unified'))

            # 调用核心导入逻辑
            success_count, error_count, error_messages = _process_lead_import_data(file_stream, current_user.company_id, current_user.id)

            if error_count > 0:
                flash(f'导入完成，成功导入 {success_count} 条线索，失败 {error_count} 条。错误信息：{", ".join(error_messages)}', 'warning')
            else:
                flash(f'成功导入 {success_count} 条线索到您的公司', 'success')

        elif import_target == 'other_company':
            # --- 请求导入到其他公司逻辑 --- 
            target_company_id = request.form.get('target_company_id', '').strip()
            target_company_name = request.form.get('target_company_name', '').strip()
            notes = request.form.get('notes')

            if not target_company_id:
                flash('请选择目标公司', 'danger')
                return redirect(url_for('leads.leads_unified'))

            target_company = Company.query.get(target_company_id)

            if not target_company:
                flash(f'未找到ID为 "{target_company_id}" 的公司，请重新选择。', 'danger')
                return redirect(url_for('leads.leads_unified'))

            if target_company.id == current_user.company_id:
                flash('不能选择您自己所在的公司作为目标。', 'danger')
                return redirect(url_for('leads.leads_unified'))

            try:
                temp_dir = os.path.join(current_app.instance_path, 'temp_imports')
                os.makedirs(temp_dir, exist_ok=True)
                filename = secure_filename(f"{uuid.uuid4()}_{file.filename}")
                file_path = os.path.join(temp_dir, filename)
                file.seek(0)
                file.save(file_path)
            except Exception as e:
                app_logger.error(f"保存临时导入文件失败: {str(e)}", exc_info=True)
                flash('处理文件时出错，请稍后重试', 'danger')
                return redirect(url_for('leads.leads_unified'))

            import_request = LeadImportRequest(
                requester_user_id=current_user.id,
                requester_company_id=current_user.company_id,
                target_company_id=target_company_id, 
                file_path=file_path,
                original_filename=file.filename,
                status='pending',
                notes=notes
            )
            db.session.add(import_request)
            db.session.flush() # 获取 import_request.id (虽然下面没直接用，但有时需要)

            target_admins = get_company_admins(target_company_id)
            if not target_admins:
                 app_logger.warning(f"无法找到目标公司 {target_company_id} 的管理员发送通知")

            notification_message = f"收到来自 {current_user.company.name} ({current_user.username}) 的线索导入请求，文件名: {file.filename}"
            related_url = url_for('leads.import_requests', _external=True) # 使用绝对URL

            # --- 添加邮件通知给目标管理员 --- 
            email_subject = f"[CRM] 新的线索导入请求: {import_request.original_filename}"
            
            # 从 app.config 获取邮件配置 (应该在应用启动时已加载)
            smtp_server = current_app.config.get('MAIL_SERVER')
            smtp_port = current_app.config.get('MAIL_PORT')
            mail_username = current_app.config.get('MAIL_USERNAME')
            mail_password = current_app.config.get('MAIL_PASSWORD')
            use_ssl = current_app.config.get('MAIL_USE_SSL')
            use_tls = current_app.config.get('MAIL_USE_TLS')
            sender_email = current_app.config.get('MAIL_DEFAULT_SENDER') or mail_username
            
            if not all([smtp_server, smtp_port, mail_username, mail_password, sender_email]):
                app_logger.error("邮件配置不完整，无法发送导入请求通知邮件")
            else:
                for admin_user in target_admins:
                    if admin_user.email:
                        try:
                            app_logger.info(f"准备发送导入请求通知邮件给管理员 {admin_user.username} ({admin_user.email})")
                            html_body = render_template(
                                'emails/import_request_notification.html', 
                                admin_user=admin_user, 
                                request_obj=import_request,
                                approval_url=related_url # 传入审批链接
                            )
                            
                            email_msg = EmailMessage()
                            email_msg['Subject'] = email_subject
                            email_msg['From'] = sender_email
                            email_msg['To'] = admin_user.email
                            email_msg.set_content("请在支持HTML的邮件客户端中查看此邮件。")
                            email_msg.add_alternative(html_body, subtype='html')
                            
                            context = ssl.create_default_context()
                            server = None
                            try:
                                if use_ssl:
                                    server = smtplib.SMTP_SSL(smtp_server, smtp_port, context=context, timeout=15)
                                else:
                                    server = smtplib.SMTP(smtp_server, smtp_port, timeout=15)
                                    if use_tls:
                                        server.starttls(context=context)
                                server.login(mail_username, mail_password)
                                server.send_message(email_msg)
                                app_logger.info(f"成功发送导入请求通知邮件给 {admin_user.email}")
                            finally:
                                if server:
                                    server.quit()
                        except Exception as mail_e:
                            app_logger.error(f"发送导入请求通知邮件给 {admin_user.email} 失败: {str(mail_e)}", exc_info=True)
                    else:
                         app_logger.warning(f"目标管理员 {admin_user.username} 没有配置邮箱地址，无法发送邮件通知")
            # --- 邮件通知结束 --- 

            # 添加系统通知 (现在放在邮件发送之后)
            for admin_user in target_admins:
                notification = Notification(
                    user_id=admin_user.id,
                    message=notification_message,
                    related_url=related_url # 使用绝对 URL
                )
                db.session.add(notification)

            db.session.commit() # 提交请求记录和通知
            flash(f'已向 {target_company.name} 发送线索导入请求', 'success')

        else:
            flash('无效的导入目标', 'danger')

    except Exception as e:
        db.session.rollback()
        app_logger.error(f'导入处理失败：{str(e)}', exc_info=True)
        flash(f'导入处理失败：{str(e)}', 'danger')

    return redirect(url_for('leads.leads_unified'))

# 辅助函数：处理文件内容（支持CSV和XLSX）
def _process_file_content(file_content, filename):
    """处理文件内容，支持CSV和XLSX格式"""
    file_ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''

    if file_ext == 'xlsx':
        # 处理xlsx文件
        return _process_xlsx_file(file_content)
    else:
        # 处理CSV文件（原有逻辑）
        return _decode_file_content(file_content)

# 辅助函数：解码文件内容（仅用于CSV）
def _decode_file_content(file_content):
    encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312', 'gb18030']
    for encoding in encodings:
        try:
            # 尝试解码并返回 StringIO 对象
            return io.StringIO(file_content.decode(encoding), newline=None)
        except UnicodeDecodeError:
            # 如果解码失败，继续尝试下一种编码
            continue
    # 如果所有编码都失败，返回 None
    return None

# 辅助函数：验证CSV格式
def _validate_csv_format(file_stream):
    try:
        # 使用 peek 读取少量数据来检查格式，避免消耗整个流
        peek_data = file_stream.read(1024) # 读取最多1KB
        if not peek_data:
            return False # 文件为空
        file_stream.seek(0) # 重置指针
        
        # 尝试解析头部
        sniffer = csv.Sniffer()
        dialect = sniffer.sniff(peek_data)
        # 检查是否有有效的header
        reader = csv.DictReader(io.StringIO(peek_data, newline=None), dialect=dialect)
        if not reader.fieldnames:
             app_logger.warning("CSV文件缺少有效的表头")
             return False
             
        file_stream.seek(0) # 重置指针
        return True
    except csv.Error as csv_e:
        app_logger.warning(f"CSV格式错误: {csv_e}")
        file_stream.seek(0) # 重置指针
        return False
    except Exception as e:
        # 捕获其他可能的异常
        app_logger.error(f"验证CSV格式时发生未知错误: {e}", exc_info=True)
        file_stream.seek(0) # 重置指针
        return False

# 新增：核心处理导入数据的函数
def _process_lead_import_data(file_stream, company_id, owner_id):
    """处理上传的CSV文件流，创建线索"""
    success_count = 0
    error_count = 0
    error_messages = []
    duplicate_count = 0  # 新增：重复数据计数
    required_columns = ['姓名', '电话'] # 定义必须存在的列
    optional_columns = ['省份', '城市', '来源', '备注']
    column_map = {
        '姓名': 'name',
        '电话': 'phone',
        '省份': 'province', # 临时存储
        '城市': 'city', # 临时存储
        '来源': 'source', # 注意：Lead 模型目前没有 source 字段，此列数据可能被忽略
        '备注': 'notes'
    }

    try:
        reader = csv.DictReader(file_stream)
        
        # 检查必需的列是否存在
        header = reader.fieldnames
        if not header:
             error_messages.append("CSV文件为空或缺少表头")
             return success_count, error_count + 1, error_messages
             
        missing_required = [col for col in required_columns if col not in header]
        if missing_required:
            error_messages.append(f"CSV文件缺少必需列: {', '.join(missing_required)}")
            return success_count, error_count + 1, error_messages
        
        # 获取公司线索池 (确保目标公司有线索池)
        target_company = Company.query.get(company_id)
        if not target_company:
            error_messages.append(f"目标公司 ID {company_id} 不存在")
            return 0, 1, error_messages
            
        normal_pool = LeadPool.query.filter_by(company_id=company_id, is_public_sea=False).first()
        if not normal_pool:
            app_logger.info(f'目标公司 {target_company.name} (ID: {company_id}) 没有普通线索池，创建一个')
            normal_pool = LeadPool(
                name=f"{target_company.name}线索池",
                description="普通线索池",
                company_id=company_id,
                is_public_sea=False
            )
            db.session.add(normal_pool)
            db.session.flush() 
            app_logger.info(f'为公司 {company_id} 创建了新的线索池 ID: {normal_pool.id}')
            
        pool_id = normal_pool.id

        # 新增：预先获取现有电话号码，用于重复检查
        existing_phones = set()
        existing_leads = Lead.query.with_entities(Lead.phone).filter(Lead.phone.isnot(None)).all()
        for lead in existing_leads:
            if lead.phone:
                existing_phones.add(lead.phone.strip())
        app_logger.info(f'预加载了 {len(existing_phones)} 个现有电话号码用于重复检查')

        for i, row in enumerate(reader):
            row_num = i + 2 # 行号从2开始（包含表头）
            lead_data = {}
            try:
                # 提取数据并映射到模型字段
                for csv_col, model_attr in column_map.items():
                    if csv_col in row:
                        lead_data[model_attr] = row[csv_col].strip() if row[csv_col] else None
                
                # 检查必需字段
                if not lead_data.get('name') or not lead_data.get('phone'):
                    raise ValueError("姓名和电话不能为空")

                # 新增：检查电话号码是否重复
                phone_to_check = lead_data['phone'].strip()
                if phone_to_check in existing_phones:
                    duplicate_count += 1
                    app_logger.info(f"Row {row_num}: 跳过重复电话号码 {phone_to_check}")
                    continue  # 跳过重复数据，不计入错误

                # 将当前电话号码添加到已存在集合中，防止同批次内重复
                existing_phones.add(phone_to_check)

                # 处理省份和城市
                province = lead_data.pop('province', None)
                city = lead_data.pop('city', None)

                # 标准化城市名称（自动补全"市"字等）
                if city:
                    normalized_city = normalize_city_name(city)
                    app_logger.info(f"Row {row_num}: City '{city}' normalized to '{normalized_city}'")
                    city = normalized_city

                # 优先使用省份列，如果省份为空但城市不为空，则尝试通过城市查找省份
                company_name_for_lead = province # 默认使用省份列作为 Lead 的 company_name
                if not company_name_for_lead and city:
                    found_province = find_province_by_city(city)
                    if found_province:
                        company_name_for_lead = found_province
                        app_logger.info(f"Row {row_num}: City '{city}' found province '{found_province}'")
                    else:
                        app_logger.warning(f"Row {row_num}: City '{city}' could not find province, using city as company_name.")
                        company_name_for_lead = city # 如果找不到省份，则使用城市名
                elif not company_name_for_lead and not city:
                     company_name_for_lead = "未知区域" # 省份和城市都为空

                # 使用城市列作为 Lead 的 email (按现有逻辑)
                email_for_lead = city if city else "未知城市"
                
                # 提取备注
                notes = lead_data.pop('notes', None)
                
                # 准备最终传递给 create_lead 的数据
                final_lead_data = {
                     'name': lead_data['name'],
                     'company_name': company_name_for_lead,
                     'email': email_for_lead,
                     'phone': lead_data['phone'],
                     'owner_id': owner_id,
                     'company_id': company_id,
                     'current_processing_company_id': company_id, # 修正：添加当前处理公司
                     'pool_id': pool_id,
                     'notes': notes,
                     'is_self_created': False # 标记为导入
                }

                # 创建线索 (使用已有的 create_lead 函数)
                created_lead = create_lead(**final_lead_data)
                success_count += 1

            except ValueError as ve:
                error_count += 1
                error_messages.append(f"第 {row_num} 行错误: {str(ve)} - 数据: {row}")
            except KeyError as ke:
                error_count += 1
                error_messages.append(f"第 {row_num} 行错误: 缺少列 {str(ke)} - 数据: {row}")
            except Exception as e:
                error_count += 1
                error_messages.append(f"第 {row_num} 行处理失败: {str(e)} - 数据: {row}")
                db.session.rollback() # 回滚当前行的事务更改
        
        # 提交所有成功创建的线索
        db.session.commit()
        
    except csv.Error as csv_e:
        error_count += 1
        error_messages.append(f"读取CSV文件时出错: {str(csv_e)}")
        db.session.rollback()
    except Exception as global_e:
        error_count += 1
        error_messages.append(f"处理导入时发生未知错误: {str(global_e)}")
        db.session.rollback()
    finally:
         if file_stream:
              file_stream.seek(0) # 确保文件指针重置，即使出错

    return success_count, error_count, error_messages

# 统一线索管理界面 (原 unified-management 改为 leads)
@bp.route('/leads')
@login_required
def leads_unified():
    """统一线索管理界面"""
    try:
        # 获取统计数据
        stats = get_leads_statistics(current_user)
        return render_template('leads_unified.html', stats=stats)
    except Exception as e:
        app_logger.error(f'加载统一线索管理界面失败: {str(e)}', exc_info=True)
        flash('加载失败，请稍后重试', 'danger')
        return redirect(url_for('index'))

def get_leads_statistics(user):
    """获取线索统计数据（使用缓存优化）"""
    try:
        # 使用优化的统计数据获取
        return lead_optimizer.get_cached_statistics(user)
    except Exception as e:
        app_logger.error(f'获取统计数据失败，使用备用方法: {str(e)}')
        # 备用方法
        return _get_basic_statistics(user)

def _get_basic_statistics(user):
    """基础统计数据获取（备用方法）"""
    stats = {}

    # 我的线索数量
    my_leads_query = Lead.query.filter(Lead.owner_id == user.id)
    stats['my_leads_count'] = my_leads_query.count()
    stats['my_pending_count'] = my_leads_query.filter(Lead.is_deal_done == False).count()

    # 公海线索数量
    if user.role.code == 'super_admin':
        stats['public_sea_count'] = Lead.query.filter(Lead.is_in_public_sea == True).count()
    else:
        # 只显示本公司的公海线索
        stats['public_sea_count'] = Lead.query.filter(
            Lead.is_in_public_sea == True,
            Lead.company_id == user.company_id
        ).count()

    # 异地到店待认领数量
    cross_location_pool = LeadPool.query.filter_by(
        company_id=user.company_id,
        pool_type='CROSS_LOCATION_PENDING'
    ).first()
    if cross_location_pool:
        stats['cross_location_pending_count'] = Lead.query.filter(
            Lead.pool_id == cross_location_pool.id,
            Lead.owner_id == None
        ).count()
    else:
        stats['cross_location_pending_count'] = 0

    # 本月签约数量
    from datetime import datetime, timedelta
    month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    if user.role.code == 'super_admin':
        monthly_deals = Lead.query.filter(
            Lead.is_deal_done == True,
            Lead.updated_at >= month_start
        ).count()
        total_leads_this_month = Lead.query.filter(Lead.created_at >= month_start).count()
    else:
        monthly_deals = Lead.query.filter(
            Lead.is_deal_done == True,
            Lead.updated_at >= month_start,
            Lead.company_id == user.company_id
        ).count()
        total_leads_this_month = Lead.query.filter(
            Lead.created_at >= month_start,
            Lead.company_id == user.company_id
        ).count()

    stats['monthly_deals_count'] = monthly_deals
    stats['conversion_rate'] = round((monthly_deals / total_leads_this_month * 100) if total_leads_this_month > 0 else 0, 1)

    # 本月提车数量
    if user.role.code == 'super_admin':
        monthly_deliveries = Lead.query.filter(
            Lead.is_car_selected == True,
            Lead.updated_at >= month_start
        ).count()
    else:
        monthly_deliveries = Lead.query.filter(
            Lead.is_car_selected == True,
            Lead.updated_at >= month_start,
            Lead.company_id == user.company_id
        ).count()

    stats['monthly_deliveries_count'] = monthly_deliveries

    return stats

# API路由用于统一界面的数据加载
@bp.route('/api/leads/my-leads')
@login_required
def api_my_leads():
    """API: 获取我的线索数据"""
    try:
        stage_filter = request.args.get('stage', '')
        search = request.args.get('search', '')
        page = request.args.get('page', 1, type=int)
        per_page = 20

        query = Lead.query.filter(Lead.owner_id == current_user.id)

        # 应用筛选条件
        if stage_filter:
            query = apply_stage_filter(query, stage_filter)

        if search:
            query = query.filter(or_(
                Lead.name.ilike(f'%{search}%'),
                Lead.company_name.ilike(f'%{search}%'),
                Lead.phone.ilike(f'%{search}%')
            ))

        pagination = query.order_by(Lead.updated_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        leads_data = []
        for lead in pagination.items:
            leads_data.append({
                'id': lead.id,
                'name': lead.name,
                'company_name': lead.company_name,
                'email': lead.email,
                'phone': mask_phone_number(lead.phone) if lead.phone else '',  # 脱敏处理
                'current_stage': lead.current_stage,
                'last_activity': lead.updated_at.strftime('%Y-%m-%d %H:%M') if lead.updated_at else None
            })

        return jsonify({
            'success': True,
            'leads': leads_data,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'total': pagination.total,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }
        })
    except Exception as e:
        app_logger.error(f'获取我的线索API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': '获取数据失败'})

@bp.route('/api/leads/team-leads')
@login_required
def api_team_leads():
    """API: 获取团队线索数据"""
    try:
        # 权限检查
        if current_user.role.code not in ['super_admin', 'company_admin', 'department_admin']:
            return jsonify({'success': False, 'message': '权限不足'})

        owner_filter = request.args.get('owner_id', '')
        stage_filter = request.args.get('stage', '')
        page = request.args.get('page', 1, type=int)
        per_page = 20

        # 根据角色构建基础查询
        if current_user.role.code == 'super_admin':
            query = Lead.query
        elif current_user.role.code == 'company_admin':
            query = Lead.query.filter(Lead.company_id == current_user.company_id)
        else:  # department_admin
            department_ids = get_child_departments(current_user.department_id)
            query = Lead.query.filter(
                Lead.company_id == current_user.company_id,
                Lead.owner_id.in_(db.session.query(User.id).filter(User.department_id.in_(department_ids)))
            )

        # 应用筛选条件
        if owner_filter:
            query = query.filter(Lead.owner_id == owner_filter)

        if stage_filter:
            query = apply_stage_filter(query, stage_filter)

        pagination = query.order_by(Lead.updated_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        leads_data = []
        for lead in pagination.items:
            # 优先显示真实姓名，如果没有则显示用户名
            if lead.owner:
                owner_name = lead.owner.name or lead.owner.username
            else:
                owner_name = '未分配'

            leads_data.append({
                'id': lead.id,
                'name': lead.name,
                'company_name': lead.company_name,
                'email': lead.email,
                'phone': mask_phone_number(lead.phone) if lead.phone else '',  # 脱敏处理
                'current_stage': lead.current_stage,
                'owner_name': owner_name,
                'last_activity': lead.updated_at.strftime('%Y-%m-%d %H:%M') if lead.updated_at else None
            })

        return jsonify({
            'success': True,
            'leads': leads_data,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'total': pagination.total,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }
        })
    except Exception as e:
        app_logger.error(f'获取团队线索API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': '获取数据失败'})

@bp.route('/api/leads/public-sea')
@login_required
def api_public_sea_leads():
    """API: 获取公海线索数据"""
    try:
        search = request.args.get('search', '')
        page = request.args.get('page', 1, type=int)
        per_page = 20

        # 构建查询
        if current_user.role.code == 'super_admin':
            query = Lead.query.filter(Lead.is_in_public_sea == True)
        else:
            query = Lead.query.filter(
                Lead.is_in_public_sea == True,
                Lead.company_id == current_user.company_id
            )

        if search:
            query = query.filter(or_(
                Lead.name.ilike(f'%{search}%'),
                Lead.company_name.ilike(f'%{search}%'),
                Lead.phone.ilike(f'%{search}%')
            ))

        pagination = query.order_by(Lead.public_sea_time.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        leads_data = []
        for lead in pagination.items:
            leads_data.append({
                'id': lead.id,
                'name': lead.name,
                'company_name': lead.company_name,
                'email': lead.email,
                'phone': mask_phone_number(lead.phone) if lead.phone else '',  # 脱敏处理
                'current_stage': lead.current_stage,
                'last_activity': lead.public_sea_time.strftime('%Y-%m-%d %H:%M') if lead.public_sea_time else None
            })

        return jsonify({
            'success': True,
            'leads': leads_data,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'total': pagination.total,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }
        })
    except Exception as e:
        app_logger.error(f'获取公海线索API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': '获取数据失败'})

def apply_stage_filter(query, stage_filter):
    """应用阶段筛选"""
    stage_mapping = {
        'new': Lead.is_called == False,
        'called': and_(Lead.is_called == True, Lead.is_connected == False),
        'connected': and_(Lead.is_connected == True, Lead.is_valid_call == False),
        'valid_call': and_(Lead.is_valid_call == True, Lead.is_wechat_added == False),
        'wechat_added': and_(Lead.is_wechat_added == True, Lead.is_intentional == False),
        'intentional': and_(Lead.is_intentional == True, Lead.is_visited == False),
        'visited': and_(Lead.is_visited == True, Lead.is_compliant == False),
        'compliant': and_(Lead.is_compliant == True, Lead.is_deal_done == False),
        'deal_done': and_(Lead.is_deal_done == True, Lead.is_car_selected == False),
        'car_selected': Lead.is_car_selected == True
    }

    if stage_filter in stage_mapping:
        query = query.filter(stage_mapping[stage_filter])

    return query

@bp.route('/api/leads/cross-location')
@login_required
def api_cross_location_leads():
    """API: 获取异地到店线索数据"""
    try:
        source_company_filter = request.args.get('source_company', '')
        page = request.args.get('page', 1, type=int)
        per_page = 20

        # 获取当前公司的异地到店池
        cross_location_pool = LeadPool.query.filter_by(
            company_id=current_user.company_id,
            pool_type='CROSS_LOCATION_PENDING'
        ).first()

        if not cross_location_pool:
            return jsonify({
                'success': True,
                'leads': [],
                'pagination': {'page': 1, 'pages': 0, 'total': 0, 'has_prev': False, 'has_next': False}
            })

        # 构建查询 - 待认领的异地线索
        query = Lead.query.filter(
            Lead.pool_id == cross_location_pool.id,
            Lead.owner_id == None,
            Lead.current_processing_company_id == current_user.company_id
        )

        # 添加调试信息
        total_in_pool = Lead.query.filter(Lead.pool_id == cross_location_pool.id).count()
        app_logger.info(f'异地池 {cross_location_pool.name} 中总共有 {total_in_pool} 个线索')
        app_logger.info(f'其中符合认领条件的线索数量: {query.count()}')

        # 来源公司筛选
        if source_company_filter:
            query = query.filter(Lead.company_id == source_company_filter)

        # 分页
        pagination = query.order_by(Lead.last_cross_location_push_time.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        leads_data = []
        for lead in pagination.items:
            leads_data.append({
                'id': lead.id,
                'name': lead.name,
                'phone': lead.phone,
                'company_name': lead.company_name,
                'email': lead.email,
                'source_company': lead.company.name if lead.company else '未知',
                'push_time': lead.last_cross_location_push_time.strftime('%Y-%m-%d %H:%M') if lead.last_cross_location_push_time else None
            })

        return jsonify({
            'success': True,
            'leads': leads_data,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'total': pagination.total,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }
        })

    except Exception as e:
        app_logger.error(f'获取异地到店线索API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': '获取数据失败'})

@bp.route('/api/leads/my-pushed-cross-location')
@login_required
def api_my_pushed_cross_location_leads():
    """API: 获取我推送的异地线索数据"""
    try:
        app_logger.info(f'用户 {current_user.username} (ID: {current_user.id}) 请求查看推送的异地线索')

        page = request.args.get('page', 1, type=int)
        per_page = 20
        status_filter = request.args.get('status', '')  # pending, claimed, completed
        target_company_filter = request.args.get('target_company', '')

        # 查询我推送的异地线索
        # 通过转移历史记录找到我推送的线索
        pushed_lead_ids_query = db.session.query(LeadTransferHistory.lead_id).filter(
            LeadTransferHistory.created_by == current_user.id,
            LeadTransferHistory.transfer_type == 'PUSH_TO_CROSS_PENDING'
        ).distinct()

        # 调试信息：查看推送历史记录数量
        push_history_count = LeadTransferHistory.query.filter(
            LeadTransferHistory.created_by == current_user.id,
            LeadTransferHistory.transfer_type == 'PUSH_TO_CROSS_PENDING'
        ).count()
        app_logger.info(f'用户 {current_user.username} 的推送历史记录数量: {push_history_count}')

        # 构建主查询 - 只查询用户有查看权限的线索
        query = Lead.query.filter(
            Lead.id.in_(pushed_lead_ids_query),
            Lead.company_id == current_user.company_id  # 确保是本公司的线索
        )

        # 状态筛选
        if status_filter == 'pending':
            # 待认领：在异地池中且无负责人
            query = query.filter(
                Lead.pool_id.isnot(None),
                Lead.owner_id.is_(None),
                Lead.current_processing_company_id.isnot(None)
            )
        elif status_filter == 'claimed':
            # 已认领：有负责人且处理公司不是本公司
            query = query.filter(
                Lead.owner_id.isnot(None),
                Lead.current_processing_company_id.isnot(None),
                Lead.current_processing_company_id != current_user.company_id
            )
        elif status_filter == 'recalled':
            # 已撤回：处理公司为空或等于本公司
            query = query.filter(
                db.or_(
                    Lead.current_processing_company_id.is_(None),
                    Lead.current_processing_company_id == current_user.company_id
                )
            )

        # 目标公司筛选
        if target_company_filter:
            query = query.filter(Lead.current_processing_company_id == target_company_filter)

        # 分页
        pagination = query.order_by(Lead.last_cross_location_push_time.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        leads_data = []
        for lead in pagination.items:
            # 获取最新的推送历史
            latest_push = LeadTransferHistory.query.filter_by(
                lead_id=lead.id,
                transfer_type='PUSH_TO_CROSS_PENDING',
                created_by=current_user.id
            ).order_by(LeadTransferHistory.created_at.desc()).first()

            # 获取目标公司信息
            target_company = None
            if lead.current_processing_company_id:
                target_company = Company.query.get(lead.current_processing_company_id)

            # 获取当前负责人信息
            current_owner = None
            if lead.owner_id:
                current_owner = User.query.get(lead.owner_id)

            # 判断线索状态
            if lead.current_processing_company_id is None or lead.current_processing_company_id == current_user.company_id:
                status = 'recalled'
                status_text = '已撤回'
            elif lead.owner_id is None:
                status = 'pending'
                status_text = '待认领'
            else:
                status = 'claimed'
                status_text = '已认领'

            leads_data.append({
                'id': lead.id,
                'name': lead.name,
                'phone': lead.phone,
                'company_name': lead.company_name,  # 省份
                'email': lead.email,  # 城市
                'status': status,
                'status_text': status_text,
                'target_company': target_company.name if target_company else '未知',
                'target_company_id': target_company.id if target_company else None,
                'current_owner': current_owner.username if current_owner else None,
                'push_time': latest_push.created_at.strftime('%Y-%m-%d %H:%M') if latest_push else None,
                'last_cross_location_push_time': lead.last_cross_location_push_time.strftime('%Y-%m-%d %H:%M') if lead.last_cross_location_push_time else None
            })

        app_logger.info(f'用户 {current_user.username} 的推送异地线索查询结果: 总数={pagination.total}, 当前页={len(leads_data)}条')

        return jsonify({
            'success': True,
            'leads': leads_data,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'total': pagination.total,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }
        })

    except Exception as e:
        app_logger.error(f'获取我推送的异地线索失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': str(e)})

@bp.route('/leads/<int:lead_id>/re-push-to-cross-location', methods=['POST'])
@login_required
def re_push_lead_to_cross_location_route(lead_id):
    """路由：重新推送异地线索到其他公司"""
    target_company_id = request.form.get('target_company_id', type=int)

    if not target_company_id:
        return jsonify({'success': False, 'message': '请选择要推送的目标公司'})

    try:
        lead = Lead.query.get_or_404(lead_id)

        # 权限检查：只能重新推送自己公司的线索
        if lead.company_id != current_user.company_id:
            return jsonify({'success': False, 'message': '您只能重新推送本公司的线索'})

        # 检查用户权限：管理员或原始推送者
        if current_user.role.code in ['super_admin', 'company_admin']:
            # 管理员有权限
            pass
        else:
            # 检查是否是原始推送者
            push_history = LeadTransferHistory.query.filter_by(
                lead_id=lead_id,
                created_by=current_user.id,
                transfer_type='PUSH_TO_CROSS_PENDING'
            ).first()

            if not push_history:
                return jsonify({'success': False, 'message': '您只能重新推送自己推送的线索'})

        # 检查线索状态：只能重新推送已撤回的或待认领的线索
        if lead.current_processing_company_id and lead.current_processing_company_id != current_user.company_id and lead.owner_id:
            return jsonify({'success': False, 'message': '该线索已被其他公司员工认领，无法重新推送'})

        # 如果线索当前在异地池中，先撤回
        if lead.current_processing_company_id and lead.current_processing_company_id != current_user.company_id:
            try:
                recall_lead_from_cross_location(lead_id, current_user)
                app_logger.info(f'重新推送前先撤回线索 {lead_id}')
            except Exception as e:
                app_logger.warning(f'撤回线索 {lead_id} 失败，继续推送: {str(e)}')

        # 执行重新推送
        push_lead_to_cross_location_pool(lead_id, target_company_id, current_user)

        return jsonify({'success': True, 'message': '线索已成功重新推送到目标公司'})

    except (ValueError, PermissionError) as e:
        return jsonify({'success': False, 'message': str(e)})
    except Exception as e:
        app_logger.error(f'重新推送异地线索失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': '重新推送过程中发生内部错误，请联系管理员'})

        if source_company_filter:
            query = query.filter(Lead.company_id == source_company_filter)

        pagination = query.order_by(Lead.last_cross_location_push_time.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        leads_data = []
        for lead in pagination.items:
            source_company_name = lead.company.name if lead.company else '未知公司'
            leads_data.append({
                'id': lead.id,
                'name': lead.name,
                'company_name': lead.company_name,
                'email': lead.email,
                'phone': mask_phone_number(lead.phone) if lead.phone else '',  # 脱敏处理
                'current_stage': lead.current_stage,
                'source_company': source_company_name,
                'last_activity': lead.last_cross_location_push_time.strftime('%Y-%m-%d %H:%M') if lead.last_cross_location_push_time else None
            })

        return jsonify({
            'success': True,
            'leads': leads_data,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'total': pagination.total,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }
        })
    except Exception as e:
        app_logger.error(f'获取异地到店线索API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': '获取数据失败'})

@bp.route('/api/leads/my-cross-location')
@login_required
def api_my_cross_location_leads():
    """API: 获取我的异地线索数据"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20

        # 查询当前用户认领的异地线索
        query = Lead.query.filter(
            Lead.owner_id == current_user.id,
            Lead.company_id != current_user.company_id,  # 线索的原始公司不是当前用户的公司
            Lead.current_processing_company_id == current_user.company_id  # 线索当前由用户所在公司处理
        )

        pagination = query.order_by(Lead.updated_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        leads_data = []
        for lead in pagination.items:
            source_company_name = lead.company.name if lead.company else '未知公司'
            leads_data.append({
                'id': lead.id,
                'name': lead.name,
                'company_name': lead.company_name,
                'email': lead.email,
                'phone': mask_phone_number(lead.phone) if lead.phone else '',  # 脱敏处理
                'current_stage': lead.current_stage,
                'source_company': source_company_name,
                'last_activity': lead.updated_at.strftime('%Y-%m-%d %H:%M') if lead.updated_at else None
            })

        return jsonify({
            'success': True,
            'leads': leads_data,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'total': pagination.total,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            }
        })
    except Exception as e:
        app_logger.error(f'获取我的异地线索API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': '获取数据失败'})

@bp.route('/api/leads/<int:lead_id>/claim', methods=['POST'])
@login_required
def api_claim_lead(lead_id):
    """API: 认领公海线索"""
    try:
        lead = Lead.query.get_or_404(lead_id)

        # 检查是否为公海线索
        if not lead.is_in_public_sea:
            return jsonify({'success': False, 'message': '该线索不在公海中'})

        # 检查权限
        if current_user.role.code != 'super_admin' and lead.company_id != current_user.company_id:
            return jsonify({'success': False, 'message': '您只能认领本公司的公海线索'})

        # 认领线索
        lead.owner_id = current_user.id
        lead.company_id = current_user.company_id
        lead.is_in_public_sea = False
        lead.public_sea_time = None

        # 设置保护期
        lead.protection_end_time = get_shanghai_now() + timedelta(days=50)

        # 添加活动记录
        activity = Activity(
            lead_id=lead.id,
            user_id=current_user.id,
            description=f'从公海认领线索'
        )
        db.session.add(activity)

        db.session.commit()

        return jsonify({'success': True, 'message': '认领成功'})
    except Exception as e:
        db.session.rollback()
        app_logger.error(f'认领线索API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': '认领失败，请重试'})

@bp.route('/api/leads/<int:lead_id>/claim-cross-location', methods=['POST'])
@login_required
def api_claim_cross_location_lead(lead_id):
    """API: 认领异地到店线索"""
    try:
        app_logger.info(f'用户 {current_user.username} (ID: {current_user.id}, 公司: {current_user.company_id}) 尝试认领异地线索 {lead_id}')
        result = claim_lead_from_cross_location_pool(lead_id, current_user)
        app_logger.info(f'异地线索 {lead_id} 认领成功')
        return jsonify({'success': True, 'message': '认领成功'})
    except Exception as e:
        app_logger.error(f'认领异地线索API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': str(e)})

# 新增完整的CRUD API接口
@bp.route('/api/leads', methods=['POST'])
@login_required
def api_create_lead():
    """API: 创建新线索"""
    try:
        data = request.get_json()

        # 验证必需字段
        required_fields = ['name', 'phone']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'{field}字段不能为空'})

        # 创建线索
        lead = create_lead(
            name=data['name'],
            company_name=data.get('company_name', ''),
            email=data.get('email', ''),
            phone=data['phone'],
            notes=data.get('notes', ''),
            owner_id=data.get('owner_id', current_user.id),
            company_id=current_user.company_id
        )

        return jsonify({
            'success': True,
            'message': '线索创建成功',
            'lead_id': lead.id
        })

    except Exception as e:
        app_logger.error(f'创建线索API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': str(e)})

@bp.route('/api/leads/<int:lead_id>', methods=['PUT'])
@login_required
def api_update_lead(lead_id):
    """API: 更新线索信息"""
    try:
        lead = Lead.query.get_or_404(lead_id)

        # 权限检查
        if not has_permission_to_edit(lead, current_user):
            return jsonify({'success': False, 'message': '权限不足'})

        data = request.get_json()

        # 获取可编辑字段
        editable_fields = LeadPermissionManager.get_editable_fields(current_user, lead)

        # 更新允许的字段
        update_data = {}
        for field, value in data.items():
            if field in editable_fields:
                update_data[field] = value

        if update_data:
            updated_lead = update_lead(lead_id, current_user, **update_data)
            return jsonify({
                'success': True,
                'message': '线索更新成功',
                'lead': {
                    'id': updated_lead.id,
                    'name': updated_lead.name,
                    'current_stage': updated_lead.current_stage
                }
            })
        else:
            return jsonify({'success': False, 'message': '没有可更新的字段'})

    except Exception as e:
        app_logger.error(f'更新线索API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': str(e)})

@bp.route('/api/leads/<int:lead_id>', methods=['DELETE'])
@login_required
def api_delete_lead(lead_id):
    """API: 删除线索"""
    try:
        lead = Lead.query.get_or_404(lead_id)

        # 权限检查
        if not has_permission_to_delete(lead, current_user):
            return jsonify({'success': False, 'message': '权限不足'})

        delete_lead(lead_id, current_user)
        return jsonify({'success': True, 'message': '线索删除成功'})

    except Exception as e:
        app_logger.error(f'删除线索API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': str(e)})

@bp.route('/api/leads/batch-delete', methods=['POST'])
@login_required
def api_batch_delete_leads():
    """API: 批量删除线索"""
    try:
        data = request.get_json()
        lead_ids = data.get('lead_ids', [])

        if not lead_ids:
            return jsonify({'success': False, 'message': '请选择要删除的线索'})

        success_count = batch_delete_leads(lead_ids, current_user)
        return jsonify({
            'success': True,
            'message': f'成功删除 {success_count} 条线索',
            'deleted_count': success_count
        })

    except Exception as e:
        app_logger.error(f'批量删除线索API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': str(e)})

@bp.route('/api/leads/batch-operations', methods=['POST'])
@login_required
def api_batch_operations():
    """API: 批量操作线索"""
    try:
        data = request.get_json()
        operation = data.get('operation')
        lead_ids = data.get('lead_ids', [])

        if not operation or not lead_ids:
            return jsonify({'success': False, 'message': '操作类型和线索ID不能为空'})

        if operation == 'assign':
            target_user_id = data.get('target_user_id')
            if not target_user_id:
                return jsonify({'success': False, 'message': '目标用户ID不能为空'})

            success_count = batch_assign_leads(lead_ids, target_user_id, current_user)
            return jsonify({
                'success': True,
                'message': f'成功分配 {success_count} 条线索'
            })

        elif operation == 'delete':
            success_count = batch_delete_leads(lead_ids, current_user)
            return jsonify({
                'success': True,
                'message': f'成功删除 {success_count} 条线索'
            })

        elif operation == 'release':
            success_count = batch_release_to_pool(lead_ids, current_user)
            return jsonify({
                'success': True,
                'message': f'成功释放 {success_count} 条线索到公海'
            })

        elif operation == 'claim':
            success_count = 0
            for lead_id in lead_ids:
                try:
                    lead = Lead.query.get(lead_id)
                    if lead and lead.is_in_public_sea:
                        # 认领公海线索
                        lead.owner_id = current_user.id
                        lead.company_id = current_user.company_id
                        lead.is_in_public_sea = False
                        lead.public_sea_time = None
                        lead.protection_end_time = get_shanghai_now() + timedelta(days=50)
                        success_count += 1
                except Exception as e:
                    app_logger.error(f'认领线索 {lead_id} 失败: {str(e)}')
                    continue

            db.session.commit()
            return jsonify({
                'success': True,
                'message': f'成功认领 {success_count} 条线索'
            })

        elif operation == 'claim_cross_location':
            success_count = 0
            failed_count = 0
            for lead_id in lead_ids:
                try:
                    claim_lead_from_cross_location_pool(lead_id, current_user)
                    success_count += 1
                    app_logger.info(f'批量认领异地线索 {lead_id} 成功')
                except Exception as e:
                    failed_count += 1
                    app_logger.error(f'批量认领异地线索 {lead_id} 失败: {str(e)}')
                    continue

            # 不需要手动提交，因为 claim_lead_from_cross_location_pool 内部已经处理了事务
            message = f'成功认领 {success_count} 条异地线索'
            if failed_count > 0:
                message += f'，{failed_count} 条认领失败'

            return jsonify({
                'success': True,
                'message': message
            })

        else:
            return jsonify({'success': False, 'message': '不支持的操作类型'})

    except Exception as e:
        db.session.rollback()
        app_logger.error(f'批量操作API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': str(e)})

@bp.route('/api/leads/export', methods=['POST'])
@login_required
def api_export_leads():
    """API: 导出线索"""
    try:
        data = request.get_json()
        export_format = data.get('format', 'csv')
        filters = data.get('filters', {})

        # 导出数据
        output = export_leads(current_user, export_format, filters)

        # 根据格式设置响应
        if export_format.lower() == 'excel':
            mimetype = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            filename = f'leads_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            return send_file(
                output,
                mimetype=mimetype,
                as_attachment=True,
                download_name=filename
            )
        elif export_format.lower() == 'json':
            return jsonify({
                'success': True,
                'data': output.getvalue(),
                'format': 'json'
            })
        else:
            return send_file(
                io.BytesIO(output.getvalue().encode('utf-8-sig')),
                mimetype='text/csv',
                as_attachment=True,
                download_name=f'leads_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
            )

    except Exception as e:
        app_logger.error(f'导出线索API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': str(e)})

@bp.route('/api/leads/import', methods=['POST'])
@login_required
def api_import_leads():
    """API: 导入线索"""
    try:
        # 检查权限
        if not current_user.has_permission('lead_import'):
            return jsonify({'success': False, 'message': '权限不足'})

        if 'file' not in request.files:
            return jsonify({'success': False, 'message': '请选择要导入的文件'})

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': '未选择文件'})

        if not allowed_file(file.filename):
            return jsonify({'success': False, 'message': '不支持的文件类型，请上传CSV或XLSX文件'})

        # 读取文件内容
        file_content = file.read()
        if not file_content:
            return jsonify({'success': False, 'message': '文件为空'})

        file_stream = _process_file_content(file_content, file.filename)
        if file_stream is None:
            return jsonify({'success': False, 'message': '无法处理文件，请确保文件格式正确'})

        if not _validate_csv_format(file_stream):
            return jsonify({'success': False, 'message': '文件格式错误'})

        # 处理导入
        success_count, error_count, error_messages = _process_lead_import_data(
            file_stream,
            current_user.company_id,
            current_user.id
        )

        return jsonify({
            'success': True,
            'message': f'导入完成，成功 {success_count} 条，失败 {error_count} 条',
            'success_count': success_count,
            'error_count': error_count,
            'error_messages': error_messages[:10]  # 只返回前10个错误信息
        })

    except Exception as e:
        app_logger.error(f'导入线索API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': str(e)})

@bp.route('/api/leads/<int:lead_id>', methods=['GET'])
@login_required
def api_get_lead(lead_id):
    """API: 获取单个线索详情"""
    try:
        lead = Lead.query.get_or_404(lead_id)
        app_logger.info(f'获取线索详情: lead_id={lead_id}, user_id={current_user.id}, user_role={current_user.role.code}')

        # 权限检查
        has_read_permission = LeadPermissionManager.has_permission(current_user, lead, 'READ')
        app_logger.info(f'读取权限检查结果: {has_read_permission}')

        if not has_read_permission:
            app_logger.warning(f'用户 {current_user.id} 没有读取线索 {lead_id} 的权限')
            return jsonify({'success': False, 'message': '权限不足'})

        lead_data = {
            'id': lead.id,
            'name': lead.name,
            'company_name': lead.company_name,
            'email': lead.email,
            'phone': lead.phone,
            'notes': lead.notes,
            'is_called': lead.is_called,
            'is_connected': lead.is_connected,
            'is_valid_call': lead.is_valid_call,
            'is_wechat_added': lead.is_wechat_added,
            'is_intentional': lead.is_intentional,
            'is_visited': lead.is_visited,
            'is_compliant': lead.is_compliant,
            'is_deal_done': lead.is_deal_done,
            'is_car_selected': lead.is_car_selected,
            'owner_id': lead.owner_id,
            'owner_name': lead.owner.username if lead.owner else None,
            'company_id': lead.company_id,
            'created_at': lead.created_at.isoformat() if lead.created_at else None,
            'updated_at': lead.updated_at.isoformat() if lead.updated_at else None
        }

        app_logger.info(f'成功返回线索详情: {lead_data}')
        return jsonify({'success': True, 'lead': lead_data})

    except Exception as e:
        app_logger.error(f'获取线索详情API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': str(e)})

@bp.route('/api/users/list', methods=['GET'])
@login_required
def api_get_users():
    """API: 获取用户列表"""
    try:
        # 根据用户角色限制可见用户
        if current_user.role.code == 'super_admin':
            users = User.query.filter_by(status='active').all()
        elif current_user.role.code == 'company_admin':
            users = User.query.filter_by(
                company_id=current_user.company_id,
                status='active'
            ).all()
        elif current_user.role.code == 'department_admin':
            users = User.query.filter_by(
                department_id=current_user.department_id,
                status='active'
            ).all()
        else:
            # 普通用户只能看到自己
            users = [current_user]

        users_data = []
        for user in users:
            users_data.append({
                'id': user.id,
                'username': user.username,
                'name': user.name,  # 添加真实姓名字段
                'email': user.email,
                'role': user.role.name if user.role else None,
                'department': user.department.name if user.department else None
            })

        return jsonify({'success': True, 'users': users_data})

    except Exception as e:
        app_logger.error(f'获取用户列表API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': str(e)})

@bp.route('/api/leads/batch-assign', methods=['POST'])
@login_required
def api_batch_assign_leads():
    """API: 批量分配线索"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        lead_ids = data.get('lead_ids', [])

        if not user_id or not lead_ids:
            return jsonify({'success': False, 'message': '参数不完整'})

        # 验证目标用户
        target_user = User.query.get(user_id)
        if not target_user:
            return jsonify({'success': False, 'message': '目标用户不存在'})

        # 执行批量分配
        success_count = batch_assign_leads(lead_ids, user_id, current_user)

        return jsonify({
            'success': True,
            'message': f'成功分配 {success_count} 条线索给 {target_user.username}'
        })

    except Exception as e:
        app_logger.error(f'批量分配线索API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': str(e)})

@bp.route('/api/companies/search', methods=['GET'])
@login_required
def api_search_companies():
    """API: 搜索公司"""
    try:
        query = request.args.get('q', '').strip()
        if len(query) < 2:
            return jsonify({'success': False, 'message': '搜索关键词至少2个字符'})

        # 构建搜索查询，排除自己的公司
        companies_query = Company.query.filter(
            and_(
                Company.id != current_user.company_id,  # 排除自己的公司
                or_(
                    Company.name.ilike(f'%{query}%'),
                    Company.code.ilike(f'%{query}%')
                )
            )
        ).limit(10)  # 限制返回数量

        companies = companies_query.all()

        companies_data = []
        for company in companies:
            companies_data.append({
                'id': company.id,
                'name': company.name,
                'code': company.code,
                'type': company.type
            })

        return jsonify({
            'success': True,
            'companies': companies_data,
            'total': len(companies_data)
        })

    except Exception as e:
        app_logger.error(f'搜索公司API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': str(e)})

@bp.route('/api/import-requests/incoming', methods=['GET'])
@login_required
def api_get_incoming_import_requests():
    """API: 获取收到的导入请求"""
    try:
        # 权限检查：只有管理员可以查看
        if not current_user.has_permission('manage_import_requests'):
            return jsonify({'success': False, 'message': '权限不足'}), 403

        status_filter = request.args.get('status', '').strip()

        # 构建查询
        query = LeadImportRequest.query.filter_by(target_company_id=current_user.company_id)

        if status_filter:
            query = query.filter_by(status=status_filter)

        requests_list = query.order_by(LeadImportRequest.requested_at.desc()).all()

        requests_data = []
        for req in requests_list:
            requests_data.append({
                'id': req.id,
                'requester_company': req.requester_company.name if req.requester_company else '未知',
                'requester_user': req.requester_user.username if req.requester_user else '未知',
                'original_filename': req.original_filename,
                'status': req.status,
                'notes': req.notes,
                'approver_notes': req.approver_notes,
                'requested_at': req.requested_at.strftime('%Y-%m-%d %H:%M') if req.requested_at else '',
                'processed_at': req.processed_at.strftime('%Y-%m-%d %H:%M') if req.processed_at else ''
            })

        return jsonify({
            'success': True,
            'requests': requests_data,
            'total': len(requests_data)
        })

    except Exception as e:
        app_logger.error(f'获取收到的导入请求失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': str(e)})

@bp.route('/api/import-requests/outgoing', methods=['GET'])
@login_required
def api_get_outgoing_import_requests():
    """API: 获取发出的导入请求"""
    try:
        status_filter = request.args.get('status', '').strip()

        # 构建查询
        query = LeadImportRequest.query.filter_by(requester_user_id=current_user.id)

        if status_filter:
            query = query.filter_by(status=status_filter)

        requests_list = query.order_by(LeadImportRequest.requested_at.desc()).all()

        requests_data = []
        for req in requests_list:
            requests_data.append({
                'id': req.id,
                'target_company': req.target_company.name if req.target_company else '未知',
                'original_filename': req.original_filename,
                'status': req.status,
                'notes': req.notes,
                'approver_notes': req.approver_notes,
                'requested_at': req.requested_at.strftime('%Y-%m-%d %H:%M') if req.requested_at else '',
                'processed_at': req.processed_at.strftime('%Y-%m-%d %H:%M') if req.processed_at else ''
            })

        return jsonify({
            'success': True,
            'requests': requests_data,
            'total': len(requests_data)
        })

    except Exception as e:
        app_logger.error(f'获取发出的导入请求失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': str(e)})

@bp.route('/api/import-requests/stats', methods=['GET'])
@login_required
def api_get_import_requests_stats():
    """API: 获取导入请求统计"""
    try:
        # 权限检查：只有管理员可以查看统计
        if not current_user.has_permission('manage_import_requests'):
            return jsonify({'success': False, 'message': '权限不足'}), 403

        # 统计收到的请求
        base_query = LeadImportRequest.query.filter_by(target_company_id=current_user.company_id)

        stats = {
            'pending_count': base_query.filter_by(status='pending').count(),
            'processing_count': base_query.filter_by(status='processing').count(),
            'completed_count': base_query.filter_by(status='completed').count(),
            'rejected_count': base_query.filter_by(status='rejected').count()
        }

        return jsonify({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        app_logger.error(f'获取导入请求统计失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': str(e)})

# 原来的 /leads 路由已移除，现在使用统一管理界面

@bp.route('/my_leads')
@login_required
def my_leads():
    """重定向到统一线索管理界面"""
    return redirect(url_for('leads.leads_unified'))

@bp.route('/new-leads')
@login_required
def new_leads():
    """重定向到统一线索管理界面"""
    return redirect(url_for('leads.leads_unified'))

@bp.route('/leads/add', methods=['GET', 'POST'])
@login_required
def add_lead():
    try:
        if request.method == 'POST':
            name = request.form.get('name')
            company_name = request.form.get('company_name')  # 获取省份/公司名称
            email = request.form.get('email')  # 获取城市/邮箱
            phone = request.form.get('phone')
            notes = request.form.get('notes')
            
            # 确保公司名称(省份)不为空
            if not company_name or company_name.strip() == '':
                company_name = "未知公司"
                
            # 确保城市(邮箱)不为空
            if not email or email.strip() == '':
                email = "未知城市"
            
            # 记录日志
            app_logger.info(f'开始添加线索: {name}, 省份: {company_name}, 城市: {email}')
            
            # 获取状态字段值
            status_fields = {
                'is_called': False,
                'is_connected': False,
                'is_valid_call': False,
                'is_wechat_added': False,
                'is_intentional': False,
                'is_compliant': False,
                'is_visited': False,
                'is_car_selected': False,
                'is_deal_done': False
            }
            
            # 处理状态字段，如果表单提交了，则使用表单值
            for field in status_fields.keys():
                field_value = request.form.get(field)
                if field_value == 'yes':  # 只有明确选择"是"时才设为True
                    status_fields[field] = True
            
            # 默认分配给当前用户，除非是管理员指定了其他人
            owner_id = current_user.id  # 默认为当前用户
            if current_user.role.code in ['super_admin', 'company_admin', 'department_admin']:
                form_owner_id = request.form.get('owner_id')
                if form_owner_id and form_owner_id.strip():  # 确保不是空字符串
                    owner_id = int(form_owner_id)
            
            app_logger.info(f'负责人ID: {owner_id}')
            
            # 获取当前公司用户的默认线索池
            company_id = current_user.company_id
            
            # 当owner_id不是None时，获取对应用户的公司ID
            company_obj = None # Initialize company_obj
            if owner_id:
                owner = User.query.get(owner_id)
                if owner:
                    company_id = owner.company_id
                    company_obj = Company.query.get(company_id) # Get the Company object
                    app_logger.info(f'设置公司ID为负责人的公司: {company_id}, 负责人: {owner.username}')
                else:
                    app_logger.warning(f'未找到ID为{owner_id}的用户')
            else: # Handle case where owner_id might be None initially if logic allows
                company_id = current_user.company_id
                company_obj = Company.query.get(company_id)
            
            # 确定线索池和是否在公海
            pool_id = None
            
            # 查找该公司的普通线索池
            normal_pool = LeadPool.query.filter_by(company_id=company_id, is_public_sea=False).first()
            if not normal_pool:
                app_logger.info(f'公司{company_id}没有普通线索池，创建一个')
                # 如果没有普通线索池，创建一个
                company_obj = Company.query.get(company_id)
                normal_pool = LeadPool(
                    name=f"{company_obj.name}线索池" if company_obj else "默认线索池",
                    description="普通线索池",
                    company_id=company_id,
                    is_public_sea=False
                )
                db.session.add(normal_pool)
                db.session.flush()
                app_logger.info(f'创建了新的线索池ID: {normal_pool.id}')
            else:
                app_logger.info(f'使用现有线索池ID: {normal_pool.id}')
            
            pool_id = normal_pool.id
            
            # Get company name safely
            actual_company_name = company_obj.name if company_obj else "未知公司"
            app_logger.info(f'线索创建参数 - 名称: {name}, 负责人: {owner_id}, 公司ID: {company_id}, 公司名: {actual_company_name}, 线索池: {pool_id}')
            
            # 创建线索
            lead = create_lead(
                name=name,
                company_name=company_name,  # 使用表单提交的省份作为company_name
                email=email,  # 使用表单提交的城市作为email
                phone=phone,
                owner_id=owner_id,
                company_id=company_id,
                pool_id=pool_id,
                notes=notes,
                **status_fields  # 使用处理后的状态字段
            )
            
            flash('线索添加成功', 'success')
            return redirect(url_for('leads.my_leads'))
    except Exception as e:
        app_logger.error(f'线索添加失败: {str(e)}', exc_info=True)
        flash('线索添加失败：' + str(e), 'danger')
        return redirect(url_for('leads.leads_unified'))
    
    # 获取所有用户（用于选择负责人）
    if current_user.role.code == 'super_admin':
        users = User.query.all()
    elif current_user.role.code == 'company_admin':
        users = User.query.filter_by(company_id=current_user.company_id).all()
    elif current_user.role.code == 'department_admin':
        users = User.query.filter_by(department_id=current_user.department_id).all()
    else:
        users = [current_user]
    
    return render_template('lead_form.html', users=users)

@bp.route('/leads/<int:lead_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_lead(lead_id):
    try:
        lead = Lead.query.get_or_404(lead_id)
        
        # 检查权限
        if not has_permission_to_edit(lead, current_user):
            flash('您没有权限编辑此线索', 'danger')
            return redirect(url_for('leads.leads_unified'))
        
        # 判断是否为B公司员工编辑其认领的异地线索 (此判断移到更早，POST和GET都需要)
        is_editing_cross_lead_by_b_employee = False
        if lead and current_user.id == lead.owner_id and \
           current_user.company_id == lead.current_processing_company_id and \
           current_user.company_id != lead.company_id:
            is_editing_cross_lead_by_b_employee = True

        if request.method == 'POST':
            lead_data = {}
            status_fields = [
                'is_called', 'is_connected', 'is_valid_call', 'is_wechat_added',
                'is_intentional', 'is_compliant', 'is_visited', 'is_car_selected', 'is_deal_done'
            ]

            if is_editing_cross_lead_by_b_employee:
                # B员工编辑异地线索：只允许修改状态和备注
                lead_data['notes'] = request.form.get('notes')
                for field in status_fields:
                    field_value = request.form.get(field)
                    lead_data[field] = True if field_value == 'yes' else False
            else:
                # 其他情况：允许修改基本信息和状态
                company_name_from_form = request.form.get('company_name')
                email_from_form = request.form.get('email')
                
                processed_company_name = company_name_from_form if company_name_from_form and company_name_from_form.strip() != '' else (lead.company_name if lead and lead.company_name else "未知公司")
                processed_email = email_from_form if email_from_form and email_from_form.strip() != '' else (lead.email if lead and lead.email else "未知城市")

                lead_data = {
                    'name': request.form.get('name'),
                    'company_name': processed_company_name,
                    'email': processed_email,
                    'phone': request.form.get('phone'),
                    'notes': request.form.get('notes')
                }
                for field in status_fields:
                    field_value = request.form.get(field)
                    lead_data[field] = True if field_value == 'yes' else False
            
            # 只有管理员和经理可以更改负责人 (此逻辑保持，但B员工不会提交owner_id)
            if not is_editing_cross_lead_by_b_employee and current_user.role.code in ['super_admin', 'company_admin', 'department_admin']:
                owner_id = request.form.get('owner_id')
                if owner_id:
                    new_owner = User.query.get(int(owner_id))
                    if new_owner and is_valid_assignee(new_owner, current_user):
                        lead_data['owner_id'] = new_owner.id
                        lead_data['company_id'] = new_owner.company_id # 线索的公司归属随负责人变更
            
            lead = update_lead(lead_id, current_user, **lead_data)
            
            # 获取目标公司ID（新增）
            target_company_id = request.form.get('target_company_id')
            
            # 如果提供了目标公司ID且线索意向客户，执行推送（新增）
            if target_company_id and lead.is_intentional:
                try:
                    push_lead_to_cross_location_pool(lead.id, int(target_company_id), current_user)
                    flash('线索已成功推送到目标公司', 'success')
                except (ValueError, PermissionError) as e:
                    flash(f'推送失败：{str(e)}', 'warning')
                except Exception as e:
                    app_logger.error(f'推送过程发生错误: {str(e)}', exc_info=True)
                    flash('推送过程发生错误，但线索已保存', 'warning')
            
            flash('线索更新成功', 'success')
            return redirect(url_for('leads.leads_unified'))
    except Exception as e:
        app_logger.error(f'线索更新失败: {str(e)}', exc_info=True)
        flash('线索更新失败：' + str(e), 'danger')
        return redirect(url_for('leads.leads_unified'))
    
    # 获取可选的用户列表
    if current_user.role.code == 'super_admin':
        users = User.query.all()
    elif current_user.role.code == 'company_admin':
        users = User.query.filter_by(company_id=current_user.company_id).all()
    elif current_user.role.code == 'department_admin':
        users = User.query.filter_by(department_id=current_user.department_id).all()
    else:
        users = [current_user]
    
    # is_editing_cross_lead_by_b_employee 的计算已经移到函数开头

    return render_template('lead_form.html', 
                           lead=lead, 
                           users=users, 
                           is_editing_cross_lead_by_b_employee=is_editing_cross_lead_by_b_employee)

@bp.route('/leads/<int:lead_id>')
@login_required
def view_lead(lead_id):
    lead = Lead.query.get_or_404(lead_id)
    
    # 检查权限 - 使用新的权限系统
    has_permission = LeadPermissionManager.has_permission(current_user, lead, 'READ')

    # 特殊情况：原公司管理员可以查看已推送的异地线索（用于撤回等操作）
    if not has_permission and has_permission_to_view_and_recall(lead, current_user):
        has_permission = True
        app_logger.info(f'用户 {current_user.username} 通过异地线索查看权限访问线索 {lead_id}')
    
    if not has_permission:
        flash('您没有权限查看此线索', 'danger')
        return redirect(url_for('leads.leads_unified'))
    
    # 获取线索的活动记录
    activities = Activity.query.filter_by(lead_id=lead_id).order_by(Activity.created_at.desc()).all()
    
    # 获取所有公司列表，用于推送到异地到店
    companies = Company.query.all()
    
    return render_template('lead_detail.html', lead=lead, activities=activities, companies=companies)

@bp.route('/leads/<int:lead_id>/activity', methods=['POST'])
@login_required
def add_activity(lead_id):
    lead = Lead.query.get_or_404(lead_id)
    
    # 检查权限
    has_permission = False
    
    if current_user.role.code == 'super_admin':
        has_permission = True
    elif current_user.role.code == 'company_admin':
        lead_owner = User.query.get(lead.owner_id) if lead.owner_id else None
        has_permission = lead_owner is None or lead_owner.company_id == current_user.company_id
    elif current_user.role.code == 'department_admin':
        lead_owner = User.query.get(lead.owner_id) if lead.owner_id else None
        has_permission = lead_owner is None or lead_owner.department_id == current_user.department_id
    else:
        has_permission = lead.owner_id == current_user.id
    
    if not has_permission:
        flash('您没有权限添加活动记录', 'danger')
        return redirect(url_for('leads.leads_unified'))
    
    try:
        description = request.form.get('description')
        if description:
            activity = Activity(
                lead_id=lead.id,
                user_id=current_user.id,
                description=description
            )
            db.session.add(activity)
            db.session.commit()
            flash('活动记录添加成功', 'success')
    except Exception as e:
        db.session.rollback()
        flash('活动记录添加失败：' + str(e), 'danger')
    
    return redirect(url_for('leads.view_lead', lead_id=lead.id))

@bp.route('/leads/<int:lead_id>/delete', methods=['POST'])
@login_required
def delete_lead_route(lead_id):
    try:
        delete_lead(lead_id, current_user)
        flash('线索已删除', 'success')
    except Exception as e:
        flash('删除线索失败：' + str(e), 'danger')
    
    return redirect(url_for('leads.leads_unified'))

@bp.route('/leads/<int:lead_id>/assign', methods=['GET', 'POST'])
@login_required
def assign_lead_route(lead_id):
    try:
        if request.method == 'POST':
            owner_id = request.form.get('owner_id')
            notes = request.form.get('notes')
            assignment_type = request.form.get('assignment_type', 'same_company')
            target_company_id = request.form.get('company_id')

            # 分配线索
            lead = assign_lead(lead_id, owner_id, current_user, notes, assignment_type, target_company_id)
            
            flash('线索分配成功', 'success')
            return redirect(url_for('leads.leads_unified'))
    except Exception as e:
        flash('线索分配失败：' + str(e), 'danger')
        return redirect(url_for('leads.leads_unified'))
    
    # 获取可选的用户列表
    if current_user.role.code == 'super_admin':
        users = User.query.all()
    elif current_user.role.code == 'company_admin':
        users = User.query.filter_by(company_id=current_user.company_id).all()
    elif current_user.role.code == 'department_admin':
        # 部门管理员应该只能分配给本部门或子部门的用户
        department_ids = get_child_departments(current_user.department_id)
        users = User.query.filter(User.department_id.in_(department_ids)).all()
    else:
        # 普通用户通常不能直接进入这个分配页面，除非是分配自己的线索给别人（如果业务允许）
        # 暂时假设普通用户不能访问此页面进行分配操作，或者只能看到自己
        users = [current_user] 
    
    lead = Lead.query.get_or_404(lead_id)
    companies = Company.query.all()  # 添加此行来查询所有公司
    return render_template('assign_lead.html', lead=lead, users=users, companies=companies) # 将companies传递给模板

@bp.route('/leads/<int:lead_id>/transfer', methods=['GET', 'POST'])
@login_required
def transfer_lead(lead_id):
    try:
        lead = Lead.query.get_or_404(lead_id)
        
        # 检查权限
        if current_user.role.code not in ['super_admin', 'company_admin', 'department_admin'] and lead.owner_id != current_user.id:
            flash('您没有权限转移此线索', 'danger')
            return redirect(url_for('leads.leads_unified'))
        
        if request.method == 'POST':
            new_owner_id = request.form.get('new_owner_id')
            if not new_owner_id:
                flash('请选择新的负责人', 'danger')
                return redirect(url_for('leads.transfer_lead', lead_id=lead_id))
            
            new_owner = User.query.get(int(new_owner_id))
            if not new_owner:
                flash('选择的用户不存在', 'danger')
                return redirect(url_for('leads.transfer_lead', lead_id=lead_id))
            
            # 验证新负责人是否在当前用户的管理范围内
            if current_user.role.code == 'super_admin':
                pass  # 超级管理员可以转移给任何人
            elif current_user.role.code == 'company_admin':
                if new_owner.company_id != current_user.company_id:
                    flash('您只能转移给本公司的用户', 'danger')
                    return redirect(url_for('leads.transfer_lead', lead_id=lead_id))
            elif current_user.role.code == 'department_admin':
                if new_owner.department_id != current_user.department_id:
                    flash('您只能转移给本部门的用户', 'danger')
                    return redirect(url_for('leads.transfer_lead', lead_id=lead_id))
            
            # 更新线索负责人
            old_owner = lead.owner.username if lead.owner else '未分配'
            lead.owner_id = new_owner.id
            
            # 添加活动记录
            activity = Activity(
                lead_id=lead.id,
                user_id=current_user.id,
                description=f'将线索从 {old_owner} 转移给 {new_owner.username}'
            )
            db.session.add(activity)
            
            db.session.commit()
            flash('线索转移成功', 'success')
            return redirect(url_for('leads.leads_unified'))
    except Exception as e:
        db.session.rollback()
        flash('线索转移失败：' + str(e), 'danger')
        return redirect(url_for('leads.leads_unified'))
    
    # 获取可选的用户列表
    if current_user.role.code == 'super_admin':
        users = User.query.filter(User.id != current_user.id).all()
    elif current_user.role.code == 'company_admin':
        users = User.query.filter(User.company_id == current_user.company_id, User.id != current_user.id).all()
    elif current_user.role.code == 'department_admin':
        users = User.query.filter(User.department_id == current_user.department_id, User.id != current_user.id).all()
    
    return render_template('transfer_lead.html', lead=lead, users=users)

@bp.route('/leads/<int:lead_id>/reclaim', methods=['POST'])
@login_required
def reclaim_lead(lead_id):
    lead = Lead.query.get_or_404(lead_id)
    
    # 检查权限：管理员可以回收任何线索，经理只能回收本公司的，销售只能回收自己创建的线索
    activities = Activity.query.filter_by(lead_id=lead.id).order_by(Activity.created_at).first()
    creator_id = activities.user_id if activities else None
    
    if current_user.role != 'admin':
        if current_user.role == 'manager' and lead.company_id != current_user.company_id:
            flash('您没有权限回收此线索', 'danger')
            return redirect(url_for('leads.leads_unified'))
        elif creator_id != current_user.id:
            flash('您没有权限回收此线索', 'danger')
            return redirect(url_for('leads.leads_unified'))
    
    # 记录原负责人和公司信息
    old_owner = lead.owner.username if lead.owner else '无'
    old_company = lead.owner.company.name if lead.owner and lead.owner.company else '无'
    
    # 回收线索（设置负责人为当前用户）
    lead.owner_id = current_user.id
    lead.company_id = current_user.company_id  # 更新线索所属公司
    
    # 添加活动记录
    activity = Activity(
        lead_id=lead.id,
        user_id=current_user.id,
        description=f'从 {old_owner}（{old_company}）回收了线索'
    )
    db.session.add(activity)
    
    db.session.commit()
    flash('线索回收成功', 'success')
    return redirect(url_for('leads.view_lead', lead_id=lead.id))

@bp.route('/export-leads')
@login_required
def export_leads_route():
    try:
        output = export_leads(current_user)
        return send_file(
            io.BytesIO(output.getvalue().encode('utf-8-sig')),
            mimetype='text/csv',
            as_attachment=True,
            download_name=f'leads_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
        )
    except Exception as e:
        app_logger.error(f'导出线索失败: {str(e)}', exc_info=True)
        flash(f'导出失败：{str(e)}', 'danger')
        return redirect(url_for('leads.leads_unified'))

@bp.route('/leads/release-to-pool', methods=['POST'])
@login_required
def release_leads_to_pool_route():
    lead_ids = request.form.getlist('lead_ids')
    
    if not lead_ids:
        flash('请选择要释放的线索', 'warning')
        return redirect(url_for('leads.leads_unified'))
    
    try:
        # 分别处理异地线索和普通线索
        cross_location_lead_ids = []
        regular_lead_ids = []
        
        for lead_id in lead_ids:
            try:
                lead = Lead.query.get(int(lead_id))
                # 检查是否是异地线索
                if lead and lead.owner_id == current_user.id and \
                   lead.current_processing_company_id == current_user.company_id and \
                   lead.company_id != current_user.company_id:
                    cross_location_lead_ids.append(lead_id)
                else:
                    regular_lead_ids.append(lead_id)
            except Exception as e:
                app_logger.error(f"检查线索 {lead_id} 类型时出错: {str(e)}")
                regular_lead_ids.append(lead_id)  # 出错时当做普通线索处理
        
        # 处理异地线索
        cross_location_success_count = 0
        for lead_id in cross_location_lead_ids:
            try:
                release_lead_to_pool(int(lead_id), current_user)
                cross_location_success_count += 1
            except Exception as e:
                app_logger.error(f"释放异地线索 {lead_id} 失败: {str(e)}", exc_info=True)
        
        # 处理普通线索
        regular_success_count = 0
        if regular_lead_ids:
            regular_success_count = batch_release_to_pool(regular_lead_ids, current_user)
        
        total_success_count = cross_location_success_count + regular_success_count
        
        # 根据成功释放的线索类型给出不同提示
        if cross_location_success_count > 0 and regular_success_count > 0:
            flash(f'成功释放 {regular_success_count} 条线索到公海和 {cross_location_success_count} 条异地线索到异地到店池', 'success')
        elif cross_location_success_count > 0:
            flash(f'成功释放 {cross_location_success_count} 条异地线索到异地到店池', 'success')
        else:
            flash(f'成功释放 {regular_success_count} 条线索到公海', 'success')
    except Exception as e:
        app_logger.error(f'批量释放线索失败: {str(e)}', exc_info=True)
        flash(f'释放失败：{str(e)}', 'danger')
    
    # 根据请求来源决定重定向目标
    referer = request.referrer
    if referer and 'my-cross-location-leads' in referer:
        return redirect(url_for('leads.my_cross_location_leads'))
    else:
        return redirect(url_for('leads.leads_unified'))

@bp.route('/leads/batch-assign', methods=['POST'])
@login_required
def batch_assign_leads_route():
    """批量分配线索给指定用户"""
    lead_ids = request.form.getlist('lead_ids')
    user_id = request.form.get('user_id')
    
    # 记录调试信息
    app_logger.info(f'批量分配线索 - 目标用户: {user_id}, 线索IDs: {lead_ids}')
    
    if not user_id or not lead_ids:
        flash('请选择要分配的线索和目标用户', 'danger')
        return redirect(url_for('leads.leads_unified'))
    
    try:
        success_count = batch_assign_leads(lead_ids, user_id, current_user)
        user = User.query.get(user_id)
        flash(f'成功分配 {success_count} 条线索给 {user.username}', 'success')
    except Exception as e:
        app_logger.error(f'批量分配线索失败: {str(e)}', exc_info=True)
        flash(f'分配失败：{str(e)}', 'danger')
    
    return redirect(url_for('leads.leads_unified'))

@bp.route('/released-leads/deal-done')
@login_required
def view_deal_done_released_leads():
    # 查询当前用户放出且签约的线索
    user_id = current_user.id
    
    # 首先找出用户放出的线索
    transfer_records = LeadTransferHistory.query.filter_by(
        from_user_id=user_id,
        transfer_type='release'
    ).all()
    
    lead_ids = [record.lead_id for record in transfer_records]
    
    # 查询这些线索中签约的线索详情，包括认领用户和公司信息
    deal_done_leads = []
    for lead_id in lead_ids:
        lead = Lead.query.get(lead_id)
        if lead and lead.is_deal_done:
            # 获取当前拥有者信息
            owner = User.query.get(lead.owner_id) if lead.owner_id else None
            company = Company.query.get(owner.company_id) if owner else None
            
            deal_done_leads.append({
                'id': lead.id,
                'name': lead.name,
                'company_name': lead.company_name,
                'phone': mask_phone_number(lead.phone),
                'claimed_by': owner.username if owner else '未知',
                'claimed_company': company.name if company else '未知',
                'deal_date': lead.deal_date.strftime('%Y-%m-%d') if lead.deal_date else '未知'
            })
    
    return render_template('deal_done_released_leads.html', 
                         leads=deal_done_leads, 
                         total_count=len(deal_done_leads))

def batch_delete_leads(lead_ids, current_user):
    """批量删除线索"""
    try:
        success_count = 0
        for lead_id in lead_ids:
            try:
                lead = Lead.query.get(int(lead_id))
                # 检查权限
                if lead and has_permission_to_delete(lead, current_user):
                    # 删除相关的活动记录
                    Activity.query.filter_by(lead_id=lead.id).delete()
                    
                    # 删除相关的状态历史记录
                    LeadStatusHistory.query.filter_by(lead_id=lead.id).delete()
                    
                    # 删除相关的转移历史记录
                    LeadTransferHistory.query.filter_by(lead_id=lead.id).delete()
                    
                    # 删除线索
                    db.session.delete(lead)
                    success_count += 1
            except Exception as e:
                app_logger.error(f'删除线索 {lead_id} 失败: {str(e)}')
                continue
            
        db.session.commit()
        return success_count
    except Exception as e:
        db.session.rollback()
        app_logger.error(f'批量删除线索失败: {str(e)}', exc_info=True)
        raise

@bp.route('/leads/batch-delete', methods=['POST'])
@login_required
def batch_delete_leads_route():
    """批量删除线索的路由处理函数"""
    lead_ids = request.form.getlist('lead_ids')
    
    if not lead_ids:
        flash('请选择要删除的线索', 'warning')
        return redirect(url_for('leads.leads_unified'))
    
    try:
        success_count = batch_delete_leads(lead_ids, current_user)
        flash(f'成功删除 {success_count} 条线索', 'success')
    except Exception as e:
        app_logger.error(f'批量删除线索失败: {str(e)}', exc_info=True)
        flash(f'删除失败：{str(e)}', 'danger')
    
    return redirect(url_for('leads.leads_unified'))

@bp.route('/download-template')
@login_required
def download_template():
    """下载线索导入模板"""
    try:
        template_content = "姓名,省份,城市,电话,来源,备注\n张三,,广州市,13800138000,手动录入,这是一个示例（省份可选，会根据城市自动匹配）"
        return send_file(
            io.BytesIO(template_content.encode('gbk')),
            mimetype='text/csv',
            as_attachment=True,
            download_name='leads_template.csv'
        )
    except Exception as e:
        app_logger.error(f'下载模板失败: {str(e)}', exc_info=True)
        flash(f'下载模板失败：{str(e)}', 'danger')
        return redirect(url_for('leads.leads_unified'))

# 导入请求管理路由（统一界面）
@bp.route('/import-requests-unified')
@login_required
def import_requests_unified():
    """统一的导入请求管理界面"""
    # 权限检查：仅管理员可访问
    if not current_user.has_permission('manage_import_requests'):
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('leads.leads_unified'))

    # 获取统计数据
    try:
        base_query = LeadImportRequest.query.filter_by(target_company_id=current_user.company_id)

        stats = {
            'pending_count': base_query.filter_by(status='pending').count(),
            'processing_count': base_query.filter_by(status='processing').count(),
            'completed_count': base_query.filter_by(status='completed').count(),
            'rejected_count': base_query.filter_by(status='rejected').count()
        }
    except Exception as e:
        app_logger.error(f'获取导入请求统计失败: {str(e)}', exc_info=True)
        stats = {
            'pending_count': 0,
            'processing_count': 0,
            'completed_count': 0,
            'rejected_count': 0
        }

    return render_template('import_requests_unified.html', stats=stats)

# 步骤 11: 添加导入请求管理路由（保留原有接口）
@bp.route('/import-requests')
@login_required
def import_requests():
    # 权限检查：仅公司管理员可访问
    if not current_user.has_permission('manage_import_requests'):
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('index'))

    # 根据角色决定查询范围
    if current_user.role.code == 'super_admin': # 使用角色代码判断超级管理员
        query = LeadImportRequest.query.all()
    elif current_user.role.code == 'company_admin':
        query = LeadImportRequest.query.filter_by(target_company_id=current_user.company_id).all()
    elif current_user.role.code == 'department_admin':
        query = LeadImportRequest.query.filter_by(target_company_id=current_user.company_id).all()
    else:
        query = LeadImportRequest.query.filter_by(requester_user_id=current_user.id).all()

    # 可选：添加分页
    page = request.args.get('page', 1, type=int)
    per_page = 15 # 每页显示数量
    pagination = query.order_by(LeadImportRequest.requested_at.desc()).paginate(
        page, per_page, error_out=False
    )
    requests_list = pagination.items

    return render_template('import_requests.html',
                           requests_list=requests_list,
                           pagination=pagination)

# 步骤 12: 处理批准导入请求
@bp.route('/import-requests/<int:request_id>/approve', methods=['POST'])
@login_required
def approve_import_request(request_id):
    # 权限检查：只有目标公司的管理员或超级管理员可以批准
    req = LeadImportRequest.query.get_or_404(request_id)
    if not current_user.has_permission('manage_import_requests') or \
       (current_user.role.code != 'super_admin' and req.target_company_id != current_user.company_id): # 使用角色代码判断
        return jsonify({'status': 'error', 'message': '权限不足'}), 403
    
    # 检查请求状态
    if req.status != 'pending':
        return jsonify({'status': 'error', 'message': '此请求无法被批准'}), 400

    try:
        req.status = 'processing'
        req.approver_user_id = current_user.id
        req.processed_at = get_shanghai_now()
        db.session.add(req)
        db.session.commit() # 先提交状态变更

        # 打开并处理文件
        file_stream = None
        try:
            # 确保文件存在
            if not os.path.exists(req.file_path):
                 raise FileNotFoundError(f"导入文件 {req.file_path} 未找到")
                 
            with open(req.file_path, 'rb') as f:
                file_content = f.read()
            
            file_stream = _process_file_content(file_content, req.original_filename)
            if file_stream is None:
                raise ValueError('无法处理文件内容')
            if not _validate_csv_format(file_stream):
                 raise ValueError('文件格式无效')

            # 调用核心导入逻辑
            success_count, error_count, error_messages = _process_lead_import_data(
                file_stream,
                req.target_company_id,
                current_user.id # 分配给审批者
            )

            # 更新请求状态和结果
            req.status = 'completed'
            req.imported_count = success_count
            req.failed_count = error_count
            if error_messages:
                 req.failure_reason = "部分行导入失败: " + "; ".join(error_messages[:5]) # 最多记录5条
            flash(f'导入请求已批准并处理完成。成功: {success_count}, 失败: {error_count}', 'success')

        except Exception as process_e:
            db.session.rollback() 
            req = LeadImportRequest.query.get(request_id) 
            req.status = 'failed'
            req.failure_reason = str(process_e)
            req.processed_at = get_shanghai_now()
            db.session.add(req)
            db.session.commit()
            app_logger.error(f"批准导入请求 {request_id} 后处理失败: {str(process_e)}", exc_info=True)
            flash(f'处理导入文件时出错: {str(process_e)}', 'danger')
        finally:
             if file_stream:
                  file_stream.close()
             try:
                  if os.path.exists(req.file_path):
                       os.remove(req.file_path)
                       app_logger.info(f"清理临时导入文件: {req.file_path}")
             except Exception as remove_e:
                  app_logger.error(f"清理临时文件 {req.file_path} 失败: {remove_e}")

        # 发送通知给请求者
        if req.requester_user:
             result_status = "成功" if req.status == 'completed' else "失败"
             notify_msg = f"您发起的向 {req.target_company.name} 的导入请求 (文件: {req.original_filename}) 已被处理，结果: {result_status} (成功{req.imported_count or 0}, 失败{req.failed_count or 0})"
             notification = Notification(
                  user_id=req.requester_user_id,
                  message=notify_msg,
                  related_url=url_for('.import_requests')
             )
             db.session.add(notification)
             db.session.commit() # 提交本次状态更新和通知

        # --- 移除这里的邮件发送逻辑 --- 
        # app_logger.info(f"准备检查是否发送邮件通知给批准者 (请求 ID: {request_id})")
        # if current_user.email: 
        #    try:
        #        # ... (原来的 smtplib 发送代码块已移除)
        #    except Exception as mail_e:
        #        app_logger.error(f"发送导入完成邮件通知给 {current_user.email} 失败 (请求 ID: {request_id}) - 使用 smtplib: {str(mail_e)}", exc_info=True)
        # else:
        #    app_logger.warning(f"无法发送导入完成邮件通知，用户 {current_user.username} 没有配置邮箱地址 (请求 ID: {request_id})")

    except Exception as e:
        db.session.rollback()
        app_logger.error(f"批准导入请求 {request_id} 时发生错误: {str(e)}", exc_info=True)
        flash('批准请求时发生内部错误', 'danger')

    return redirect(url_for('.import_requests'))

# 步骤 13: 处理拒绝导入请求
@bp.route('/import-requests/<int:request_id>/reject', methods=['POST'])
@login_required
def reject_import_request(request_id):
    # 权限检查：只有目标公司的管理员或超级管理员可以拒绝
    req = LeadImportRequest.query.get_or_404(request_id)
    if not current_user.has_permission('manage_import_requests') or \
       (current_user.role.code != 'super_admin' and req.target_company_id != current_user.company_id): # 使用角色代码判断
        return jsonify({'status': 'error', 'message': '权限不足'}), 403

    notes = request.json.get('notes', '').strip()
    if not notes:
        return jsonify({'status': 'error', 'message': '请提供拒绝原因'}), 400

    try:
        req.status = 'rejected'
        req.approver_user_id = current_user.id
        req.processed_at = get_shanghai_now()
        req.approver_notes = notes
        db.session.add(req)

        # 发送通知给请求者
        if req.requester_user:
            notify_msg = f"您发起的向 {req.target_company.name} 的导入请求 (文件: {req.original_filename}) 已被 {current_user.username} 拒绝。"
            if notes:
                 notify_msg += f" 拒绝原因: {notes}"
            notification = Notification(
                user_id=req.requester_user_id,
                message=notify_msg,
                related_url=url_for('.import_requests')
            )
            db.session.add(notification)

        db.session.commit()
        flash('导入请求已拒绝', 'success')

        # 清理临时文件
        try:
             if os.path.exists(req.file_path):
                  os.remove(req.file_path)
                  app_logger.info(f"清理临时导入文件: {req.file_path}")
        except Exception as remove_e:
             app_logger.error(f"清理临时文件 {req.file_path} 失败: {remove_e}")

    except Exception as e:
        db.session.rollback()
        app_logger.error(f"拒绝导入请求 {request_id} 时发生错误: {str(e)}", exc_info=True)
        flash('拒绝请求时发生内部错误', 'danger')

    return redirect(url_for('.import_requests'))

# 后续步骤将添加 approve 和 reject 路由
# ^^^ 这个注释现在可以移除了

def push_lead_to_cross_location_pool(lead_id, target_company_id, pushing_user):
    """将线索推送到目标公司的异地到店待认领池"""
    try:
        # 1. 验证输入和获取对象
        lead = Lead.query.get_or_404(lead_id)
        target_company = Company.query.get(target_company_id)
        if not target_company:
            raise ValueError(f'目标公司 ID {target_company_id} 不存在')
        if target_company.id == pushing_user.company_id:
            raise ValueError('不能将线索推送到自己所在的公司')

        app_logger.info(f"用户 {pushing_user.username} (公司 {pushing_user.company_id}) 准备将线索 {lead.id} 推送给公司 {target_company.name} ({target_company_id})")

        # 2. 权限检查
        # 2.1 推送权限检查
        if not has_permission_to_push(lead, pushing_user):
             app_logger.warning(f"权限拒绝 (推送异地): 用户 {pushing_user.id} 对线索 {lead.id} 没有推送权限")
             raise PermissionError('您没有权限推送此线索')
             
        # 2.2 检查线索状态是否满足推送条件 (例如 is_compliant=True)
        if not lead.is_intentional:
            # 根据实际业务调整此条件
            app_logger.warning(f'推送线索 {lead.id} 失败：线索尚未达到"意向客户"(is_intentional=True)状态') 
            raise ValueError('线索必须达到"意向客户"状态才能推送到异地公司')
            
        # 2.3 检查线索是否已在处理中 (避免重复推送)
        if lead.current_processing_company_id is not None and lead.current_processing_company_id != lead.company_id:
             app_logger.warning(f"推送线索 {lead.id} 失败：线索已由公司 {lead.current_processing_company_id} 处理中")
             raise ValueError('该线索已被推送到其他公司处理中')

        # 3. 获取目标公司的异地到店池
        target_pool = LeadPool.query.filter_by(
            company_id=target_company_id,
            pool_type='CROSS_LOCATION_PENDING'
        ).first()
        if not target_pool:
            app_logger.error(f"推送线索 {lead.id} 失败：未找到目标公司 {target_company.name} 的异地到店线索池")
            # 可以考虑动态创建，但通常应该预先存在
            raise ValueError(f'目标公司 {target_company.name} 缺少异地到店线索池，请联系管理员')
        
        app_logger.info(f"找到目标异地池: {target_pool.name} (ID: {target_pool.id})")
        
        # 4. 更新 Lead 实例
        original_owner_id = lead.owner_id # 记录原始负责人，可能用于撤回
        lead.owner_id = None  # 清除负责人，变为待认领
        lead.pool_id = target_pool.id # 指向目标异地池
        # lead.company_id 保持不变 (A公司)
        lead.current_processing_company_id = target_company_id # 标记当前由B公司处理
        lead.last_cross_location_push_time = get_shanghai_now() # 记录推送时间，使用上海时区
        # 可以考虑更新线索状态 status 字段
        # lead.status = 'pending_cross_location_claim' 

        app_logger.info(f"线索 {lead.id} 状态已更新：owner=None, pool={target_pool.id}, processing_company={target_company_id}")

        # 5. 创建 LeadTransferHistory 记录
        history = LeadTransferHistory(
            lead_id=lead.id,
            from_company_id=lead.company_id, # A公司
            to_pool_id=target_pool.id,       # B公司异地池
            from_user_id=original_owner_id,  # 原负责人
            transfer_type='PUSH_TO_CROSS_PENDING',
            created_by=pushing_user.id,
            target_company_id_for_processing=target_company_id # B公司
        )
        db.session.add(history)
        app_logger.info(f"为线索 {lead.id} 添加了推送历史记录")

        # 6. 创建活动记录
        activity = Activity(
            lead_id=lead.id,
            user_id=pushing_user.id,
            description=f'将线索推送到 {target_company.name} 的异地到店池'
        )
        db.session.add(activity)

        # 7. 发送通知给目标公司B (通知所有公司管理员)
        target_admins = get_company_admins(target_company_id)
        if target_admins:
            notification_message_template = "来自 {pushing_company_name} 的 {pushing_user_name} 推送了一条异地到店线索 '{lead_name}' (来自省份: {lead_province}, 城市: {lead_city}) 等待认领。"
            related_url = url_for('leads.cross_location_pool', _external=True)

            for admin_user in target_admins:
                # 构造个性化消息
                notification_message = notification_message_template.format(
                    pushing_company_name=pushing_user.company.name,
                    pushing_user_name=pushing_user.username,
                    lead_name=lead.name,
                    lead_province=lead.company_name, # 线索的公司名字段存的是省份
                    lead_city=lead.email,        # 线索的邮件字段存的是城市
                )
                
                # 创建站内通知
                site_notification = Notification(
                    user_id=admin_user.id,
                    message=notification_message,
                    related_url=related_url
                )
                db.session.add(site_notification)
                app_logger.info(f"已为管理员 {admin_user.username} (ID: {admin_user.id}) 创建站内通知，针对线索 {lead.id}")

                # 发送邮件通知
                if admin_user.email:
                    try:
                        mail_subject = f"[CRM] 新的异地到店线索待处理: {lead.name}"
                        # 使用简单 HTML 格式邮件
                        mail_body_html = render_template(
                            'emails/cross_location_push_notification.html', # 需要创建这个邮件模板
                            admin_name=admin_user.username,
                            lead_name=lead.name,
                            pushing_company_name=pushing_user.company.name,
                            pushing_user_name=pushing_user.username,
                            lead_province=lead.company_name,
                            lead_city=lead.email,
                            related_url=related_url
                        )
                        
                        msg = Message(
                            subject=mail_subject,
                            recipients=[admin_user.email],
                            html=mail_body_html,
                            sender=current_app.config.get('MAIL_DEFAULT_SENDER') or current_app.config.get('MAIL_USERNAME')
                        )
                        current_app.mail.send(msg) # 使用 current_app.mail 
                        app_logger.info(f"成功发送异地线索推送邮件通知给 {admin_user.email} (管理员: {admin_user.username})")
                    except Exception as mail_err:
                        app_logger.error(f"发送异地线索推送邮件给 {admin_user.email} (管理员: {admin_user.username}) 失败: {mail_err}", exc_info=True)
                else:
                    app_logger.warning(f"管理员 {admin_user.username} (ID: {admin_user.id}) 没有配置邮箱地址，跳过邮件通知")
        else:
            app_logger.warning(f"未找到目标公司 {target_company_id} 的任何管理员，无法发送通知")

        db.session.commit()
        app_logger.info(f"线索 {lead.id} 已成功推送到公司 {target_company.name} 的异地池")
        return lead

    except ValueError as ve:
        db.session.rollback()
        app_logger.error(f'推送异地线索失败 (ValueError): {str(ve)}')
        raise ve # Re-raise for handling in the route
    except PermissionError as pe:
        db.session.rollback()
        app_logger.error(f'推送异地线索失败 (PermissionError): {str(pe)}')
        raise pe # Re-raise for handling in the route
    except Exception as e:
        db.session.rollback()
        app_logger.error(f'推送异地线索时发生意外错误: {str(e)}', exc_info=True)
        raise RuntimeError('推送异地线索时发生内部错误') # Raise a generic error

def claim_lead_from_cross_location_pool(lead_id, claiming_user):
    """B公司员工从其公司的异地到店池认领线索"""
    try:
        # 1. 验证输入和获取对象
        lead = Lead.query.get_or_404(lead_id)
        if not claiming_user or not hasattr(claiming_user, 'company_id'):
             raise ValueError('无效的认领用户信息')

        claiming_company_id = claiming_user.company_id
        app_logger.info(f"用户 {claiming_user.username} (公司 {claiming_company_id}) 尝试认领异地线索 {lead.id}")

        # 2. 验证线索状态和认领者资格
        # 2.1 检查线索是否在认领者公司的 CROSS_LOCATION_PENDING 池中
        if not lead.pool_id:
             app_logger.warning(f"认领线索 {lead.id} 失败：线索不在任何池中")
             raise ValueError('该线索不在任何线索池中')
        
        target_pool = LeadPool.query.get(lead.pool_id)
        if not target_pool:
             app_logger.error(f"认领线索 {lead.id} 失败：找不到线索池 ID {lead.pool_id}")
             raise ValueError('找不到线索所在的线索池')

        if target_pool.pool_type != 'CROSS_LOCATION_PENDING' or target_pool.company_id != claiming_company_id:
            app_logger.warning(f"认领线索 {lead.id} 失败：线索不在公司 {claiming_company_id} 的异地到店待认领池中 (当前池: {target_pool.name}, 类型: {target_pool.pool_type}, 公司: {target_pool.company_id})")
            raise PermissionError('您只能认领本公司异地到店池中的线索')
            
        # 2.2 检查线索是否未被分配 (owner_id is None)
        if lead.owner_id is not None:
            existing_owner = User.query.get(lead.owner_id)
            app_logger.warning(f"认领线索 {lead.id} 失败：线索已被用户 {existing_owner.username if existing_owner else lead.owner_id} 认领")
            raise ValueError('该线索已被其他人认领')
            
        # 2.3 确认当前处理公司是认领者公司
        if lead.current_processing_company_id != claiming_company_id:
             app_logger.warning(f"认领线索 {lead.id} 失败：线索当前处理公司 ({lead.current_processing_company_id}) 与认领者公司 ({claiming_company_id}) 不符")
             # 这个情况理论上不应发生，如果发生了说明数据可能不一致
             raise ValueError('线索当前处理公司与认领者公司不符')

        app_logger.info(f"线索 {lead.id} 符合认领条件")

        # 3. 更新 Lead 实例
        lead.owner_id = claiming_user.id # 设置负责人为认领者
        original_pool_id = lead.pool_id # 记录原始池ID用于历史记录
        lead.pool_id = None            # 从异地池中移除 (或者可以移到认领者的私有池，但None更简单)
        # lead.company_id 保持不变 (A公司)
        # lead.current_processing_company_id 保持不变 (B公司)
        # 可以考虑更新线索状态 status 字段
        # lead.status = 'claimed_cross_location'
        app_logger.info(f"线索 {lead.id} 状态已更新：owner={claiming_user.id}, pool=None")

        # 4. 创建 LeadTransferHistory 记录
        history = LeadTransferHistory(
            lead_id=lead.id,
            from_pool_id=original_pool_id,         # B公司异地池
            to_user_id=claiming_user.id,         # B公司认领员工
            transfer_type='CLAIM_FROM_CROSS_PENDING',
            created_by=claiming_user.id,
            target_company_id_for_processing=claiming_company_id # B公司
        )
        db.session.add(history)
        app_logger.info(f"为线索 {lead.id} 添加了认领历史记录")

        # 5. 创建活动记录
        activity = Activity(
            lead_id=lead.id,
            user_id=claiming_user.id,
            description=f'从异地到店池认领了线索'
        )
        db.session.add(activity)

        # 6. 发送通知给原推送方A公司 (示例：通知线索的原始负责人或创建者)
        # (逻辑依赖 notification_utils.py, 先注释占位)
        # original_notifier_id = lead.creator_id # 或者找到推送者？需要结合业务决定通知谁
        # if original_notifier_id:
        #     try:
        #         from utils.notification_utils import send_notification
        #         notification_message = f"您推送的异地线索 "{lead.name}" 已被 {claiming_user.company.name} 的 {claiming_user.username} 认领。"
        #         related_url = url_for('leads.view_lead', lead_id=lead.id, _external=True) 
        #         send_notification(original_notifier_id, notification_message, related_url)
        #         app_logger.info(f"已尝试向用户 {original_notifier_id} 发送异地线索被认领通知")
        #     except ImportError:
        #         app_logger.warning("无法导入 notification_utils 或相关函数，跳过发送通知")
        #     except Exception as notify_err:
        #         app_logger.error(f"发送异地线索被认领通知失败: {notify_err}", exc_info=True)

        db.session.commit()
        app_logger.info(f"用户 {claiming_user.username} 成功认领线索 {lead.id}")
        return lead

    except ValueError as ve:
        db.session.rollback()
        app_logger.error(f'认领异地线索失败 (ValueError): {str(ve)}')
        raise ve
    except PermissionError as pe:
        db.session.rollback()
        app_logger.error(f'认领异地线索失败 (PermissionError): {str(pe)}')
        raise pe
    except Exception as e:
        db.session.rollback()
        app_logger.error(f'认领异地线索时发生意外错误: {str(e)}', exc_info=True)
        raise RuntimeError('认领异地线索时发生内部错误')

def recall_lead_from_cross_location(lead_id, recalling_user):
    """A公司员工撤回已推送至其他公司异地池的线索 (主要针对未认领)"""
    try:
        # 1. 验证输入和获取对象
        lead = Lead.query.get_or_404(lead_id)
        if not recalling_user or not hasattr(recalling_user, 'company_id'):
             raise ValueError('无效的撤回用户信息')
        
        # 确认执行撤回操作的用户属于线索的原始公司
        if lead.company_id != recalling_user.company_id:
            app_logger.warning(f"撤回线索 {lead.id} 失败：用户 {recalling_user.username} (公司 {recalling_user.company_id}) 不属于线索原始公司 {lead.company_id}")
            raise PermissionError('您只能撤回本公司推送出去的线索')

        app_logger.info(f"用户 {recalling_user.username} (公司 {recalling_user.company_id}) 尝试撤回异地线索 {lead.id}")

        # 2. 检查线索状态是否允许撤回
        # 2.1 必须是当前由其他公司处理的异地线索
        if not lead.current_processing_company_id or lead.current_processing_company_id == lead.company_id:
            app_logger.warning(f"撤回线索 {lead.id} 失败：线索当前并非由其他公司处理")
            raise ValueError('该线索当前未被推送到异地公司处理')
            
        # 2.2 线索应该在目标公司的异地池中
        if not lead.pool_id:
            app_logger.warning(f"撤回线索 {lead.id} 失败：线索不在任何池中 (可能已被认领或状态错误)")
            raise ValueError('该线索当前不在异地池中')
            
        target_pool = LeadPool.query.get(lead.pool_id)
        if not target_pool or target_pool.pool_type != 'CROSS_LOCATION_PENDING' or target_pool.company_id != lead.current_processing_company_id:
            app_logger.warning(f"撤回线索 {lead.id} 失败：线索不在预期的目标公司异地池中")
            raise ValueError('该线索当前不在目标公司的异地到店池中')
            
        # 2.3 检查线索是否可以撤回
        #    现在允许撤回已被认领的线索，但需要记录相关信息
        if lead.owner_id is not None:
            existing_owner = User.query.get(lead.owner_id)
            app_logger.info(f"撤回已认领线索 {lead.id}：当前负责人为 {existing_owner.username if existing_owner else lead.owner_id}")
            # 不再阻止撤回，而是继续执行撤回逻辑
            
        # 3. 权限检查 (撤回者是否有权限)
        #    允许原始推送者或管理员撤回
        #    首先检查是否是管理员
        if recalling_user.role.code in ['super_admin', 'company_admin']:
            # 管理员有权限撤回
            pass
        else:
            # 检查是否是原始推送者
            push_history = LeadTransferHistory.query.filter_by(
                lead_id=lead_id,
                created_by=recalling_user.id,
                transfer_type='PUSH_TO_CROSS_PENDING'
            ).first()

            if not push_history:
                app_logger.warning(f"权限拒绝 (撤回异地): 用户 {recalling_user.id} 不是线索 {lead.id} 的原始推送者")
                raise PermissionError('您只能撤回自己推送的异地线索')

        app_logger.info(f"线索 {lead.id} 符合撤回条件")

        # 4. 确定线索撤回后的状态 (放回原负责人？还是放回A公司的某个池？)
        #    方案一：尝试找回推送前的负责人
        last_push_history = LeadTransferHistory.query.filter_by(lead_id=lead.id, transfer_type='PUSH_TO_CROSS_PENDING').order_by(LeadTransferHistory.created_at.desc()).first()
        previous_owner_id = last_push_history.from_user_id if last_push_history else None
        #    方案二：放回A公司的普通线索池 (需要先找到池)
        # company_a_normal_pool = LeadPool.query.filter_by(company_id=lead.company_id, pool_type='PRIVATE').first()
        # new_pool_id = company_a_normal_pool.id if company_a_normal_pool else None
        
        # --- 决定采用方案一：尝试恢复原负责人 --- 
        new_owner_id = previous_owner_id
        new_pool_id = None # 恢复负责人后通常不放在池里
        if not new_owner_id:
             # 如果找不到原负责人，可以考虑放入A公司的普通池或公海池
             app_logger.warning(f"撤回线索 {lead.id}: 未找到推送前的负责人，将不分配负责人并放入默认池")
             # 可以在这里查找A公司的默认普通池逻辑
             company_a_normal_pool = LeadPool.query.filter_by(company_id=lead.company_id, pool_type='PRIVATE').first()
             new_pool_id = company_a_normal_pool.id if company_a_normal_pool else None
             # 或者放入公海？ company_a_public_pool = LeadPool.query.filter_by(company_id=lead.company_id, pool_type='PUBLIC_SEA').first()
             # new_pool_id = company_a_public_pool.id if company_a_public_pool else None
        
        # 5. 更新 Lead 实例
        original_pool_id = lead.pool_id # 记录原始异地池ID
        lead.owner_id = new_owner_id
        lead.pool_id = new_pool_id
        lead.current_processing_company_id = lead.company_id # 处理权交还给原始公司A
        # lead.last_cross_location_push_time 保持上次推送时间，或者清空？暂时不清空
        # lead.status = 'recalled' # 更新状态
        app_logger.info(f"线索 {lead.id} 状态已更新：owner={new_owner_id}, pool={new_pool_id}, processing_company={lead.company_id}")

        # 6. 创建 LeadTransferHistory 记录
        history = LeadTransferHistory(
            lead_id=lead.id,
            from_pool_id=original_pool_id,       # B公司异地池
            to_company_id=lead.company_id,       # A公司
            to_user_id=new_owner_id,           # A公司原负责人(或None)
            to_pool_id=new_pool_id,            # A公司池(或None)
            transfer_type='RECALL_FROM_CROSS_PENDING',
            created_by=recalling_user.id,
            target_company_id_for_processing=None # 处理权已不在B公司
        )
        db.session.add(history)
        app_logger.info(f"为线索 {lead.id} 添加了撤回历史记录")

        # 7. 创建活动记录
        activity_desc = f'从 {target_pool.company.name} 的异地池撤回了线索'
        if new_owner_id:
            new_owner_obj = User.query.get(new_owner_id)
            activity_desc += f'，恢复负责人为 {new_owner_obj.username if new_owner_obj else new_owner_id}'
        elif new_pool_id:
             new_pool_obj = LeadPool.query.get(new_pool_id)
             activity_desc += f'，放入线索池 {new_pool_obj.name if new_pool_obj else new_pool_id}'
             
        activity = Activity(
            lead_id=lead.id,
            user_id=recalling_user.id,
            description=activity_desc
        )
        db.session.add(activity)

        # 8. 发送通知 (可选，例如通知原负责人线索已撤回并重新分配给他)
        # ... 

        db.session.commit()
        app_logger.info(f"用户 {recalling_user.username} 成功撤回线索 {lead.id}")
        return lead

    except ValueError as ve:
        db.session.rollback()
        app_logger.error(f'撤回异地线索失败 (ValueError): {str(ve)}')
        raise ve
    except PermissionError as pe:
        db.session.rollback()
        app_logger.error(f'撤回异地线索失败 (PermissionError): {str(pe)}')
        raise pe
    except Exception as e:
        db.session.rollback()
        app_logger.error(f'撤回异地线索时发生意外错误: {str(e)}', exc_info=True)
        raise RuntimeError('撤回异地线索时发生内部错误')

# --- 其他函数定义 ---

# --- 新增异地线索操作路由 --- 

@bp.route('/leads/<int:lead_id>/push-to-cross-location', methods=['POST'])
@login_required
def push_lead_to_cross_location_route(lead_id):
    """路由：处理A公司推送线索到B公司异地池的请求"""
    target_company_id = request.form.get('target_company_id', type=int)
    
    if not target_company_id:
        flash('请选择要推送的目标公司', 'danger')
        # 重定向回来源页，可能需要知道来源页是哪里，或者直接到线索列表
        return redirect(request.referrer or url_for('leads.leads_unified'))
        
    try:
        push_lead_to_cross_location_pool(lead_id, target_company_id, current_user)
        flash('线索已成功推送到目标公司异地池', 'success')
    except (ValueError, PermissionError) as e:
        flash(f'推送失败：{str(e)}', 'danger')
    except Exception as e:
        app_logger.error(f'推送异地线索路由出错: {str(e)}', exc_info=True)
        flash('推送过程中发生内部错误，请联系管理员', 'danger')
        
    # 推送后通常返回列表页或线索详情页
    return redirect(url_for('leads.leads_unified'))

@bp.route('/leads/<int:lead_id>/claim-cross-location', methods=['POST'])
@login_required
def claim_cross_location_lead_route(lead_id):
    """路由：处理B公司员工认领异地池中线索的请求"""
    try:
        claim_lead_from_cross_location_pool(lead_id, current_user)
        flash('线索认领成功', 'success')
        # 认领后跳转到线索详情页或"我的线索"列表
        return redirect(url_for('leads.view_lead', lead_id=lead_id))
    except (ValueError, PermissionError) as e:
        flash(f'认领失败：{str(e)}', 'danger')
    except Exception as e:
        app_logger.error(f'认领异地线索路由出错: {str(e)}', exc_info=True)
        flash('认领过程中发生内部错误，请联系管理员', 'danger')
        
    # 失败时，可能返回来源页（异地池列表）或通用列表页
    return redirect(request.referrer or url_for('leads.leads_unified'))

@bp.route('/leads/<int:lead_id>/recall-cross-location', methods=['POST'])
@login_required
def recall_cross_location_lead_route(lead_id):
    """路由：处理A公司撤回已推送的异地线索的请求"""
    try:
        recall_lead_from_cross_location(lead_id, current_user)
        flash('异地线索已成功撤回', 'success')
    except (ValueError, PermissionError) as e:
        flash(f'撤回失败：{str(e)}', 'danger')
    except Exception as e:
        app_logger.error(f'撤回异地线索路由出错: {str(e)}', exc_info=True)
        flash('撤回过程中发生内部错误，请联系管理员', 'danger')

    # 操作后返回列表页或线索详情页
    return redirect(url_for('leads.leads_unified'))

@bp.route('/api/leads/<int:lead_id>/recall-cross-location', methods=['POST'])
@login_required
def api_recall_cross_location_lead(lead_id):
    """API: 撤回已推送的异地线索"""
    try:
        app_logger.info(f'用户 {current_user.username} (ID: {current_user.id}) 尝试撤回异地线索 {lead_id}')
        recall_lead_from_cross_location(lead_id, current_user)
        app_logger.info(f'异地线索 {lead_id} 撤回成功')
        return jsonify({'success': True, 'message': '线索撤回成功'})
    except (ValueError, PermissionError) as e:
        app_logger.warning(f'撤回异地线索API失败: {str(e)}')
        return jsonify({'success': False, 'message': str(e)})
    except Exception as e:
        app_logger.error(f'撤回异地线索API失败: {str(e)}', exc_info=True)
        return jsonify({'success': False, 'message': '撤回过程中发生内部错误，请联系管理员'})


# --- 其他路由定义保持不变 ---

# 初始化脚本，确保每个公司都有一个异地到店池
def create_cross_location_pools_for_companies():
    """为每个公司创建一个异地到店待认领池（如果不存在）"""
    companies = Company.query.all()
    for company in companies:
        # 检查公司是否已有异地到店池
        existing_pool = LeadPool.query.filter_by(
            company_id=company.id,
            pool_type='CROSS_LOCATION_PENDING'
        ).first()
        
        if not existing_pool:
            # 创建一个新的异地到店池
            new_pool = LeadPool(
                name=f"{company.name} - 异地到店池",
                description="接收其他公司推送的异地到店线索",
                company_id=company.id,
                is_public_sea=False,  # 这不是普通公海
                pool_type='CROSS_LOCATION_PENDING'
            )
            db.session.add(new_pool)
            app_logger.info(f"为公司 {company.name}(ID:{company.id}) 创建了异地到店池")
    
    db.session.commit()
    app_logger.info(f"完成所有公司的异地到店池初始化")
    return True

@bp.route('/admin/initialize-cross-pools', methods=['GET'])
@login_required
def initialize_cross_location_pools():
    """初始化每个公司的异地到店池"""
    if current_user.role.code != 'super_admin':
        flash('只有超级管理员可以执行此操作', 'danger')
        return redirect(url_for('dashboard'))
    
    try:
        create_cross_location_pools_for_companies()
        flash('成功为所有公司创建异地到店池', 'success')
    except Exception as e:
        app_logger.error(f'初始化异地到店池失败: {str(e)}', exc_info=True)
        flash(f'初始化失败: {str(e)}', 'danger')
    
    return redirect(url_for('dashboard'))

# 异地到店池页面路由
@bp.route('/cross-location-pool')
@login_required
def cross_location_pool():
    """显示当前用户公司的异地到店池"""
    # 获取筛选参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    source_company_id = request.args.get('source_company_id', type=int)
    search = request.args.get('search', '')
    
    # 查找当前用户公司的异地池
    company_cross_pool = LeadPool.query.filter_by(
        company_id=current_user.company_id,
        pool_type='CROSS_LOCATION_PENDING'
    ).first()
    
    if not company_cross_pool:
        flash('您的公司没有异地到店池，请联系管理员', 'warning')
        return redirect(url_for('leads.leads_unified'))
    
    app_logger.info(f"【异地到店调试】找到当前公司({current_user.company_id})的异地池: {company_cross_pool.name} (ID: {company_cross_pool.id})")
    
    # 逐步应用过滤条件并记录结果
    # 1. 查询该池中的所有线索(不带任何条件)
    all_leads_in_pool = Lead.query.filter(
        Lead.pool_id == company_cross_pool.id
    ).all()
    app_logger.info(f"【异地到店调试】该池中总共有 {len(all_leads_in_pool)} 条线索")
    
    # 记录所有线索的关键字段
    for lead in all_leads_in_pool:
        app_logger.info(f"【异地到店调试】线索ID={lead.id}, pool_id={lead.pool_id}, owner_id={lead.owner_id}, "
                      f"current_processing_company_id={lead.current_processing_company_id}, "
                      f"company_id={lead.company_id}, name={lead.name}")
    
    # 2. 仅应用池ID条件
    query = Lead.query.filter(Lead.pool_id == company_cross_pool.id)
    count_pool_only = query.count()
    app_logger.info(f"【异地到店调试】仅应用池ID条件 (pool_id={company_cross_pool.id}) 后有 {count_pool_only} 条线索")
    
    # 3. 应用池ID和未分配负责人条件
    query = Lead.query.filter(
        Lead.pool_id == company_cross_pool.id,
        Lead.owner_id == None
    )
    count_pool_and_owner = query.count()
    app_logger.info(f"【异地到店调试】应用池ID和未分配负责人条件后有 {count_pool_and_owner} 条线索")
    
    # 4. 应用池ID、未分配负责人和当前处理公司条件
    query = Lead.query.filter(
        Lead.pool_id == company_cross_pool.id,
        Lead.owner_id == None,
        Lead.current_processing_company_id == current_user.company_id
    )
    count_all_conditions = query.count()
    app_logger.info(f"【异地到店调试】应用全部条件后有 {count_all_conditions} 条线索")
    
    # 如果全部条件应用后没有结果，尝试放宽条件
    if count_all_conditions == 0 and count_pool_only > 0:
        app_logger.warning(f"【异地到店调试】全部条件应用后没有结果，尝试放宽条件...")
        
        # 尝试移除当前处理公司条件
        query_relaxed = Lead.query.filter(
            Lead.pool_id == company_cross_pool.id,
            Lead.owner_id == None
        )
        count_relaxed = query_relaxed.count()
        app_logger.info(f"【异地到店调试】移除当前处理公司条件后有 {count_relaxed} 条线索")
        
        # 如果移除处理公司条件后有结果，使用放宽后的查询
        if count_relaxed > 0:
            app_logger.info(f"【异地到店调试】采用放宽后的查询条件 (不检查current_processing_company_id)")
            query = query_relaxed
    
    # 应用筛选
    if source_company_id:
        pre_filter_count = query.count()
        query = query.filter(Lead.company_id == source_company_id)
        post_filter_count = query.count()
        app_logger.info(f"【异地到店调试】应用来源公司筛选 (company_id={source_company_id}) 前: {pre_filter_count} 条，后: {post_filter_count} 条")
    
    if search:
        pre_search_count = query.count()
        query = query.filter(or_(
            Lead.name.contains(search),
            Lead.phone.contains(search)
        ))
        post_search_count = query.count()
        app_logger.info(f"【异地到店调试】应用搜索筛选 (search='{search}') 前: {pre_search_count} 条，后: {post_search_count} 条")
    
    # 执行分页查询
    pagination = query.order_by(Lead.last_cross_location_push_time.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    leads = pagination.items
    final_count = len(leads)
    app_logger.info(f"【异地到店调试】最终页面显示 {final_count} 条线索 (页码: {page}, 每页: {per_page})")
    
    # 获取所有可能的来源公司列表（用于筛选）
    source_companies = Company.query.join(
        Lead, Company.id == Lead.company_id
    ).filter(
        Lead.pool_id == company_cross_pool.id,
        Lead.current_processing_company_id == current_user.company_id
    ).distinct().all()
    
    # 计算统计数据
    from datetime import datetime, timedelta
    now = datetime.now()
    today_start = datetime.combine(now.date(), datetime.min.time())
    three_days_ago = now - timedelta(days=3)

    return render_template(
        'cross_location_pool.html',
        leads=leads,
        pagination=pagination,
        source_companies=source_companies,
        now=now,
        today_start=today_start,
        three_days_ago=three_days_ago
    )

# 我的异地线索页面路由
@bp.route('/my_cross_location_leads')
@login_required
def my_cross_location_leads():
    try:
        source_company_id = request.args.get('source_company_id')
        status_filter = request.args.get('status')
        search_term = request.args.get('search', '').strip()
        page = request.args.get('page', 1, type=int)
        per_page = current_app.config.get('LEADS_PER_PAGE', 15)

        query = Lead.query.filter(
            Lead.owner_id == current_user.id,
            Lead.company_id != current_user.company_id, # 线索的原始公司不是当前用户的公司
            Lead.current_processing_company_id == current_user.company_id # 线索当前由用户所在公司处理
        )

        if source_company_id:
            query = query.filter(Lead.company_id == source_company_id)
        
        if status_filter:
            # 根据选择的状态筛选，这里假设状态字段是布尔型的
            if hasattr(Lead, status_filter) and isinstance(getattr(Lead, status_filter).property.columns[0].type, db.Boolean):
                query = query.filter(getattr(Lead, status_filter) == True)

        if search_term:
            search_conditions = []
            search_conditions.append(Lead.name.ilike(f'%{search_term}%'))
            search_conditions.append(Lead.phone.ilike(f'%{search_term}%'))
            # 注意：company_name 在此上下文中是"省份"，email 是"城市"
            search_conditions.append(Lead.company_name.ilike(f'%{search_term}%')) # 搜索省份
            search_conditions.append(Lead.email.ilike(f'%{search_term}%')) # 搜索城市
            
            # 如果要允许通过来源公司名称搜索，可以加入
            # search_conditions.append(Lead.company.has(Company.name.ilike(f'%{search_term}%')))
            query = query.filter(or_(*search_conditions))

        pagination = query.order_by(Lead.updated_at.desc()).paginate(page, per_page, error_out=False)
        leads_on_page = pagination.items

        leads_data = []
        if leads_on_page:
            lead_ids = [lead.id for lead in leads_on_page]

            # 批量获取最新的推送历史记录
            # 使用子查询找到每个 lead_id 最新的 PUSH_TO_CROSS_PENDING 记录的 created_at 时间
            latest_push_subquery = db.session.query(
                LeadTransferHistory.lead_id,
                db.func.max(LeadTransferHistory.created_at).label('max_created_at')
            ).filter(
                LeadTransferHistory.lead_id.in_(lead_ids),
                LeadTransferHistory.transfer_type == 'PUSH_TO_CROSS_PENDING'
            ).group_by(LeadTransferHistory.lead_id).subquery()

            # 根据 lead_id 和最新的 created_at 时间获取实际的推送历史记录
            push_histories_query = db.session.query(LeadTransferHistory).join(
                latest_push_subquery,
                db.and_(
                    LeadTransferHistory.lead_id == latest_push_subquery.c.lead_id,
                    LeadTransferHistory.created_at == latest_push_subquery.c.max_created_at
                )
            ).filter(LeadTransferHistory.transfer_type == 'PUSH_TO_CROSS_PENDING')
            
            push_histories = push_histories_query.all()
            
            pusher_ids = list(set(h.created_by for h in push_histories if h.created_by))
            pushers = {}
            if pusher_ids:
                users = User.query.filter(User.id.in_(pusher_ids)).all()
                pushers = {user.id: user.username for user in users}

            lead_to_pusher_map = {h.lead_id: pushers.get(h.created_by, "未知") for h in push_histories}

            for lead_obj in leads_on_page:
                leads_data.append({
                    'lead': lead_obj,
                    'pusher_name': lead_to_pusher_map.get(lead_obj.id, "未知")
                })

        # 获取来源公司列表 (用于过滤器)
        source_companies = db.session.query(Company).join(Lead, Company.id == Lead.company_id)\
            .filter(Lead.owner_id == current_user.id, Lead.company_id != current_user.company_id, Lead.current_processing_company_id == current_user.company_id)\
            .distinct(Company.id).order_by(Company.name).all()
            
        # 获取每个线索的认领时间（辅助函数）
        # 注意：这个函数在循环中使用可能会导致N+1查询，如果性能敏感，也应考虑批量获取
        def get_claim_time(lead_id):
            history = LeadTransferHistory.query.filter_by(
                lead_id=lead_id,
                transfer_type='CLAIM_FROM_CROSS_PENDING',
                to_user_id=current_user.id # 确保是当前用户认领的记录
            ).order_by(LeadTransferHistory.created_at.desc()).first()
            return history.created_at if history else None
        
        return render_template(
            'my_cross_location_leads.html',
            leads_data=leads_data, # 修改传递给模板的变量名
            pagination=pagination,
            source_companies=source_companies,
            get_claim_time=get_claim_time 
        )
    except Exception as e:
        app_logger.error(f'查询我的异地到店线索失败: {str(e)}', exc_info=True)
        flash('查询失败，请稍后重试', 'danger')
        return redirect(url_for('leads.leads_unified'))