"""Add LeasePlan model

Revision ID: 7f68664f218a
Revises: fc35bbb880d3
Create Date: 2025-05-09 10:30:55.930886

"""
from alembic import op
import sqlalchemy as sa
# from utils.timezone_compat import get_shanghai_now # get_shanghai_now is for application layer, not directly usable in server_default for all DBs


# revision identifiers, used by Alembic.
revision = '7f68664f218a'
down_revision = 'fc35bbb880d3'
branch_labels = None
depends_on = None


def upgrade():
    # ### Manually adjusted Alembic commands ###
    # op.drop_index('ix_rental_plan_lead_id', table_name='rental_plan') # Commented out as it likely doesn't exist
    # op.drop_table('rental_plan') # Commented out as it likely doesn't exist
    # op.drop_column('lead', 'is_rental_plan_agreed') # Commented out as it likely doesn't exist

    op.create_table('lease_plan',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('lead_id', sa.Integer(), nullable=False),
    sa.Column('term_months', sa.Integer(), nullable=True),
    sa.Column('free_rent_days', sa.Integer(), nullable=True, server_default=sa.text('0')),
    sa.Column('custom_notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True, server_default=sa.func.current_timestamp()),
    sa.Column('updated_at', sa.DateTime(), nullable=True, server_default=sa.func.current_timestamp(), onupdate=sa.func.current_timestamp()),
    sa.ForeignKeyConstraint(['lead_id'], ['lead.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_lease_plan_lead_id'), 'lease_plan', ['lead_id'], unique=True)
    # ### end Alembic commands ###


def downgrade():
    # ### Manually adjusted Alembic commands ###
    op.drop_index(op.f('ix_lease_plan_lead_id'), table_name='lease_plan')
    op.drop_table('lease_plan')

    # The following are commented out as they relate to a potentially non-existent 'rental_plan' table
    # op.add_column('lead', sa.Column('is_rental_plan_agreed', sa.BOOLEAN(), nullable=False))
    # op.create_table('rental_plan',
    # sa.Column('id', sa.INTEGER(), nullable=False),
    # sa.Column('lead_id', sa.INTEGER(), nullable=False),
    # sa.Column('rental_period_preset', sa.INTEGER(), nullable=True),
    # sa.Column('rental_period_custom_months', sa.INTEGER(), nullable=True),
    # sa.Column('free_days', sa.INTEGER(), nullable=True),
    # sa.Column('gift_items', sa.TEXT(), nullable=True),
    # sa.Column('other_terms', sa.TEXT(), nullable=True),
    # sa.Column('created_at', sa.DATETIME(), nullable=True),
    # sa.Column('updated_at', sa.DATETIME(), nullable=True),
    # sa.ForeignKeyConstraint(['lead_id'], ['lead.id'], name='fk_rental_plan_lead_id'),
    # sa.PrimaryKeyConstraint('id')
    # )
    # op.create_index('ix_rental_plan_lead_id', 'rental_plan', ['lead_id'], unique=1)
    # ### end Alembic commands ###
