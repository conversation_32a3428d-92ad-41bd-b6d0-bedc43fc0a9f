{% extends 'base.html' %}

{% block title %}系统消息 - CRM系统{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3">系统消息 <span class="badge bg-primary">{{ unread_count }}</span></h1>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-4">
            <div class="list-group message-list">
                {% for message in messages %}
                <a href="#message-{{ message.id }}" class="list-group-item list-group-item-action {% if not message.is_read %}unread{% endif %}" onclick="showMessage({{ message.id }})">
                    <div class="d-flex w-100 justify-content-between">
                        <h5 class="mb-1">{{ message.title }}</h5>
                        <small>{{ message.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                    </div>
                    <p class="mb-1">{{ message.content|truncate(50) }}</p>
                    <small>{{ '未读' if not message.is_read else '已读' }}</small>
                </a>
                {% endfor %}
                
                {% if not messages %}
                <div class="list-group-item text-center py-4">
                    <p class="mb-0 text-muted">暂无消息</p>
                </div>
                {% endif %}
            </div>
            
            <div class="mt-3">
                {{ render_pagination(pagination, '.system_messages') }}
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card message-detail">
                <div class="card-body">
                    <div id="message-placeholder" class="text-center py-5">
                        <p class="text-muted">选择一条消息查看详情</p>
                    </div>
                    
                    {% for message in messages %}
                    <div id="message-{{ message.id }}" class="message-content" style="display: none;">
                        <h4>{{ message.title }}</h4>
                        <p class="text-muted">
                            {{ message.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                        </p>
                        <hr>
                        <div class="message-body">
                            {{ message.content|nl2br }}
                        </div>
                        
                        {% if message.message_type == 'assignment_request' and message.related_id %}
                        <div class="mt-4">
                            <form method="post" action="{{ url_for('leads.process_request_route', request_id=message.related_id) }}">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <div class="mb-3">
                                    <label for="notes-{{ message.id }}" class="form-label">回复备注</label>
                                    <textarea class="form-control" id="notes-{{ message.id }}" name="notes" rows="2"></textarea>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <button type="submit" name="status" value="rejected" class="btn btn-danger me-2">拒绝</button>
                                    <button type="submit" name="status" value="accepted" class="btn btn-success">接受</button>
                                </div>
                            </form>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showMessage(messageId) {
    // 隐藏所有消息内容
    document.querySelectorAll('.message-content').forEach(el => {
        el.style.display = 'none';
    });
    
    // 隐藏占位符
    document.getElementById('message-placeholder').style.display = 'none';
    
    // 显示选中的消息
    const messageContent = document.getElementById('message-' + messageId);
    if (messageContent) {
        messageContent.style.display = 'block';
    }
    
    // 标记为已读
    fetch(`/messages/${messageId}/read`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    });
    
    // 更新UI
    const messageItem = document.querySelector(`.message-list a[href="#message-${messageId}"]`);
    if (messageItem && messageItem.classList.contains('unread')) {
        messageItem.classList.remove('unread');
        
        // 更新未读计数
        const badge = document.querySelector('.badge');
        const count = parseInt(badge.textContent);
        if (!isNaN(count) && count > 0) {
            badge.textContent = count - 1;
        }
    }
}

// 显示URL中指定的消息
document.addEventListener('DOMContentLoaded', function() {
    const hash = window.location.hash;
    if (hash && hash.startsWith('#message-')) {
        const messageId = hash.replace('#message-', '');
        showMessage(messageId);
    }
});
</script>

<style>
.unread {
    font-weight: bold;
    background-color: #f8f9fa;
}
.message-body {
    white-space: pre-line;
}
</style>
{% endblock %} 