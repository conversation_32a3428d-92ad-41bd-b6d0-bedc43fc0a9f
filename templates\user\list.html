{% extends 'base.html' %}

{% block title %}用户管理 - CRM系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题区域 -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">
                    <i class="fas fa-users me-2"></i>用户管理
                </h1>
                <p class="page-subtitle">管理系统用户信息，包括用户的基本信息、角色权限和状态管理</p>
            </div>
            <div class="text-end">
                <div class="btn-group-theme">
                    <a href="{{ url_for('user.add_user') }}" class="btn btn-theme-primary">
                        <i class="fas fa-plus me-1"></i>新增用户
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>用户名</th>
                            <th>邮箱</th>
                            <th>电话</th>
                            <th>部门</th>
                            <th>角色</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>{{ user.username }}</td>
                            <td>{{ user.email }}</td>
                            <td>{{ user.phone }}</td>
                            <td>{{ user.department.name if user.department else '-' }}</td>
                            <td>{{ user.role.name if user.role else '-' }}</td>
                            <td>
                                <span class="badge bg-{{ 'success' if user.status == 'active' else 'danger' }}">
                                    {{ '启用' if user.status == 'active' else '禁用' }}
                                </span>
                            </td>
                            <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ url_for('user.edit_user', user_id=user.id) }}"
                                       class="btn btn-sm btn-theme-warning">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    {% if user.id != current_user.id %}
                                    <button type="button" class="btn btn-sm btn-theme-danger delete-user"
                                            data-id="{{ user.id }}">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 删除用户
    document.querySelectorAll('.delete-user').forEach(button => {
        button.addEventListener('click', function() {
            if (confirm('确定要删除这个用户吗？')) {
                const userId = this.dataset.id;
                fetch(`/user/users/${userId}/delete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token() }}'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || '删除失败');
                    }
                });
            }
        });
    });
});
</script>
{% endblock %} 