<!-- 部门节点模板 -->
<div class="org-node department-node" data-type="department" data-id="{{ department.id }}">
    <div class="node-header" onclick="toggleNode(this)">
        <i class="fas fa-users-cog text-success"></i>
        <strong>{{ department.name }}</strong>
        <span class="badge bg-success ms-2">{{ department.type }}</span>
        <small class="text-muted ms-2">
            {% if department.subdepartments %}
                {{ department.total_departments - 1 }} 个子部门，
            {% endif %}
            {{ department.total_users }} 名员工
        </small>
        {% if department.subdepartments or department.users %}
        <i class="fas fa-chevron-down toggle-icon float-end"></i>
        {% endif %}
    </div>
    
    {% if department.subdepartments or department.users %}
    <div class="node-children" style="display: none;">
        <!-- 直属员工 -->
        {% if department.users %}
        <div class="users-section mb-3">
            <div class="text-muted mb-2">
                <i class="fas fa-users"></i> 直属员工 ({{ department.users|length }})
            </div>
            {% for user in department.users %}
            <div class="user-node" data-type="user" data-id="{{ user.id }}">
                <div class="node-header user-header">
                    <i class="fas fa-user text-info"></i>
                    <span>{{ user.name }}</span>
                    <small class="text-muted ms-2">({{ user.username }})</small>
                    <span class="badge bg-info ms-2">{{ user.role }}</span>
                    {% if user.status == 'active' %}
                    <span class="badge bg-success ms-1">在职</span>
                    {% else %}
                    <span class="badge bg-secondary ms-1">{{ user.status }}</span>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        <!-- 子部门 -->
        {% for subdepartment in department.subdepartments %}
            {% set department = subdepartment %}
            {% include 'organization_unified/_department_node.html' %}
        {% endfor %}
    </div>
    {% endif %}
</div>

<style>
.user-header {
    background-color: #f1f3f4 !important;
    border-color: #e1e5e9 !important;
    padding: 8px 12px !important;
    margin-bottom: 5px;
    cursor: default;
}

.user-header:hover {
    background-color: #e8eaed !important;
}

.users-section {
    padding: 10px;
    background-color: #fafbfc;
    border-radius: 5px;
    border: 1px solid #e9ecef;
}
</style>
