{% macro render_field(field) %}
  <div class="form-group {% if field.errors %}has-error{% endif %}">
    {% if field.type not in ['BooleanField', 'SubmitField'] %}
      {{ field.label }}
    {% endif %}
    {{ field(class='form-control' + (' is-invalid' if field.errors else ''), **kwargs)|safe }}
    {% if field.errors %}
      <div class="invalid-feedback">
        {% for error in field.errors %}
          {{ error }}
        {% endfor %}
      </div>
    {% endif %}
    {% if field.description %}
      <small class="form-text text-muted">{{ field.description }}</small>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_checkbox_field(field) %}
  <div class="form-check">
    {{ field(class='form-check-input', **kwargs)|safe }}
    {{ field.label(class='form-check-label') }}
    {% if field.errors %}
      <div class="invalid-feedback">
        {% for error in field.errors %}
          {{ error }}
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_radio_field(field) %}
  <div class="form-check">
    {{ field(class='form-check-input', **kwargs)|safe }}
    {{ field.label(class='form-check-label') }}
    {% if field.errors %}
      <div class="invalid-feedback">
        {% for error in field.errors %}
          {{ error }}
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_submit_field(field) %}
  <div class="form-group">
    {{ field(class='btn btn-primary', **kwargs)|safe }}
  </div>
{% endmacro %} 