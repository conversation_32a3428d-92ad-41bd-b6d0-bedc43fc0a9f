{% extends 'base.html' %}

{% block title %}组织管理 - CRM系统{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-1">
                                <i class="fas fa-sitemap"></i> 组织管理
                            </h1>
                            <p class="text-muted mb-0">统一管理公司、部门和员工信息</p>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">权限范围：{{ scope.role }}</small>
                            <br>
                            <div class="btn-group mt-2">
                                {% if scope.can_manage_companies %}
                                <a href="{{ url_for('organization_unified.add_company') }}" class="btn btn-sm btn-success">
                                    <i class="fas fa-plus"></i> 添加公司
                                </a>
                                {% endif %}
                                {% if scope.can_manage_departments %}
                                <a href="{{ url_for('organization_unified.add_department') }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-plus"></i> 添加部门
                                </a>
                                {% endif %}
                                {% if scope.can_manage_users %}
                                <a href="{{ url_for('organization_unified.add_employee') }}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-plus"></i> 添加员工
                                </a>
                                {% endif %}
                                <button class="btn btn-sm btn-outline-primary" onclick="refreshData()">
                                    <i class="fas fa-sync-alt"></i> 刷新
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计概览 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-primary shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-building fa-2x"></i>
                    </div>
                    <h5 class="card-title text-primary">公司数量</h5>
                    <h3 class="mb-0">{{ org_tree.total_companies }}</h3>
                    {% if scope.can_manage_companies %}
                    <div class="mt-2">
                        <a href="{{ url_for('organization_unified.companies') }}" class="btn btn-sm btn-primary me-1">
                            <i class="fas fa-cog"></i> 管理
                        </a>
                        <a href="{{ url_for('organization_unified.add_company') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-plus"></i> 添加
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-success shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-users-cog fa-2x"></i>
                    </div>
                    <h5 class="card-title text-success">部门数量</h5>
                    <h3 class="mb-0">{{ org_tree.total_departments }}</h3>
                    {% if scope.can_manage_departments %}
                    <a href="{{ url_for('organization_unified.departments') }}" class="btn btn-sm btn-success mt-2">
                        <i class="fas fa-cog"></i> 管理
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-info shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                    <h5 class="card-title text-info">员工数量</h5>
                    <h3 class="mb-0">{{ org_tree.total_users }}</h3>
                    {% if scope.can_manage_users %}
                    <a href="{{ url_for('organization_unified.employees') }}" class="btn btn-sm btn-info mt-2">
                        <i class="fas fa-cog"></i> 管理
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-warning shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-chart-org fa-2x"></i>
                    </div>
                    <h5 class="card-title text-warning">组织架构</h5>
                    <h3 class="mb-0">{{ org_tree.companies|length }}</h3>
                    <button class="btn btn-sm btn-warning mt-2" onclick="showOrgTree()">
                        <i class="fas fa-eye"></i> 查看
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速导航 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-tachometer-alt"></i> 快速导航
                    </h5>
                    <div class="row">
                        {% if scope.can_manage_companies %}
                        <div class="col-md-4 mb-3">
                            <div class="list-group">
                                <div class="list-group-item list-group-item-action bg-light">
                                    <strong><i class="fas fa-building"></i> 公司管理</strong>
                                </div>
                                <a href="{{ url_for('organization_unified.companies') }}" class="list-group-item list-group-item-action">
                                    <i class="fas fa-list"></i> 公司列表
                                </a>
                                <a href="{{ url_for('organization_unified.add_company') }}" class="list-group-item list-group-item-action">
                                    <i class="fas fa-plus"></i> 添加公司
                                </a>
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if scope.can_manage_departments %}
                        <div class="col-md-4 mb-3">
                            <div class="list-group">
                                <div class="list-group-item list-group-item-action bg-light">
                                    <strong><i class="fas fa-users-cog"></i> 部门管理</strong>
                                </div>
                                <a href="{{ url_for('organization_unified.departments') }}" class="list-group-item list-group-item-action">
                                    <i class="fas fa-list"></i> 部门列表
                                </a>
                                <a href="{{ url_for('organization_unified.add_department') }}" class="list-group-item list-group-item-action">
                                    <i class="fas fa-plus"></i> 添加部门
                                </a>
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if scope.can_manage_users %}
                        <div class="col-md-4 mb-3">
                            <div class="list-group">
                                <div class="list-group-item list-group-item-action bg-light">
                                    <strong><i class="fas fa-users"></i> 员工管理</strong>
                                </div>
                                <a href="{{ url_for('organization_unified.employees') }}" class="list-group-item list-group-item-action">
                                    <i class="fas fa-list"></i> 员工列表
                                </a>
                                <a href="{{ url_for('organization_unified.add_employee') }}" class="list-group-item list-group-item-action">
                                    <i class="fas fa-plus"></i> 添加员工
                                </a>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 组织架构树 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-sitemap"></i> 组织架构
                        <div class="float-end">
                            {% if scope.can_manage_companies %}
                            <a href="{{ url_for('organization_unified.add_company') }}" class="btn btn-sm btn-success me-2">
                                <i class="fas fa-plus"></i> 添加公司
                            </a>
                            {% endif %}
                            <button class="btn btn-sm btn-outline-secondary me-2" onclick="collapseAll()">
                                <i class="fas fa-compress-arrows-alt"></i> 收起全部
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="expandAll()">
                                <i class="fas fa-expand-arrows-alt"></i> 展开全部
                            </button>
                        </div>
                    </h5>
                    <div id="orgTree" class="mt-3">
                        {% for company in org_tree.companies %}
                        <div class="org-node company-node" data-type="company" data-id="{{ company.id }}">
                            <div class="node-header" onclick="toggleNode(this)">
                                <i class="fas fa-building text-primary"></i>
                                <strong>{{ company.name }}</strong>
                                <span class="badge bg-primary ms-2">{{ company.type }}</span>
                                <small class="text-muted ms-2">
                                    {{ company.department_count }} 个部门，{{ company.user_count }} 名员工
                                </small>
                                <i class="fas fa-chevron-down toggle-icon float-end"></i>
                            </div>
                            <div class="node-children" style="display: none;">
                                {% for department in company.departments %}
                                    {% include 'organization_unified/_department_node.html' %}
                                {% endfor %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近活动 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-history"></i> 最近活动
                    </h5>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>操作</th>
                                    <th>对象</th>
                                    <th>操作人</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="4" class="text-center text-muted">暂无活动记录</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 组织架构模态框 -->
<div class="modal fade" id="orgTreeModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-sitemap"></i> 组织架构图
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="orgTreeChart"></div>
            </div>
        </div>
    </div>
</div>

<style>
.org-node {
    margin-left: 20px;
    margin-bottom: 10px;
}

.company-node {
    margin-left: 0;
}

.node-header {
    padding: 10px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.node-header:hover {
    background-color: #e9ecef;
}

.node-children {
    margin-top: 10px;
    padding-left: 20px;
    border-left: 2px solid #dee2e6;
}

.toggle-icon {
    transition: transform 0.3s;
}

.toggle-icon.rotated {
    transform: rotate(180deg);
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
</style>

<script>
function refreshData() {
    location.reload();
}

function showOrgTree() {
    $('#orgTreeModal').modal('show');
    // 这里可以加载更详细的组织架构图
}

function toggleNode(element) {
    const children = element.nextElementSibling;
    const icon = element.querySelector('.toggle-icon');
    
    if (children.style.display === 'none') {
        children.style.display = 'block';
        icon.classList.add('rotated');
    } else {
        children.style.display = 'none';
        icon.classList.remove('rotated');
    }
}

function expandAll() {
    document.querySelectorAll('.node-children').forEach(node => {
        node.style.display = 'block';
    });
    document.querySelectorAll('.toggle-icon').forEach(icon => {
        icon.classList.add('rotated');
    });
}

function collapseAll() {
    document.querySelectorAll('.node-children').forEach(node => {
        node.style.display = 'none';
    });
    document.querySelectorAll('.toggle-icon').forEach(icon => {
        icon.classList.remove('rotated');
    });
}

// 页面加载完成后默认展开第一层
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.company-node .node-children').forEach(node => {
        node.style.display = 'block';
    });
    document.querySelectorAll('.company-node .toggle-icon').forEach(icon => {
        icon.classList.add('rotated');
    });
});
</script>
{% endblock %}
