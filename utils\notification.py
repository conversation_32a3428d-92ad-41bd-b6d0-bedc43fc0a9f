from flask import render_template
from flask_mail import Message
from models import db
from models.user import User
from models.company import Company
from models.notification import Notification
from flask import current_app

def send_deal_notification(lead, deal_history):
    """发送线索签约状态变更通知"""
    # 获取原始公司的管理员用户
    admin_users = User.query.filter_by(
        company_id=lead.original_company_id,
        is_admin=True,
        is_active=True
    ).all()
    
    # 创建通知内容
    notification_data = {
        'title': f'线索签约状态更新: {lead.name}',
        'content': f'''
            线索 {lead.name} 的签约状态已更新：
            - 签约状态：{format_deal_status(deal_history.new_status)}
            - 签约时间：{deal_history.deal_time.strftime('%Y-%m-%d') if deal_history.deal_time else '未设置'}
            - 签约金额：{'¥' + str(deal_history.deal_amount) if deal_history.deal_amount else '未设置'}
            - 反馈内容：{deal_history.feedback_content or '无'}
        '''.strip(),
        'type': 'deal_feedback',
        'related_id': lead.id
    }
    
    # 为每个管理员创建系统通知
    for admin in admin_users:
        notification = Notification(
            user_id=admin.id,
            **notification_data
        )
        db.session.add(notification)
    
    # 发送邮件通知
    if admin_users:
        company = Company.query.get(lead.original_company_id)
        msg = Message(
            subject=notification_data['title'],
            recipients=[admin.email for admin in admin_users if admin.email],
            html=render_template(
                'emails/deal_notification.html',
                lead=lead,
                deal_history=deal_history,
                company=company
            )
        )
        current_app.extensions['mail'].send(msg)
    
    db.session.commit()

def format_deal_status(status):
    """格式化签约状态"""
    status_map = {
        'won': '签约',
        'lost': '未签约',
        'pending': '待定'
    }
    return status_map.get(status, status)