#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接检查SQLite数据库
"""

import sqlite3
import os
from werkzeug.security import check_password_hash

def check_sqlite_db():
    """直接检查SQLite数据库"""
    try:
        db_path = 'crm.db'
        if not os.path.exists(db_path):
            print(f"❌ 数据库文件不存在: {db_path}")
            return False
        
        print("🔍 直接检查SQLite数据库")
        print("="*60)
        print(f"数据库文件: {db_path}")
        print(f"文件大小: {os.path.getsize(db_path)} 字节")
        print()
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 检查表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"📋 数据库表: {[table[0] for table in tables]}")
        print()
        
        # 2. 检查用户表
        if ('user',) in tables:
            cursor.execute("SELECT COUNT(*) FROM user;")
            user_count = cursor.fetchone()[0]
            print(f"👥 用户总数: {user_count}")
            
            # 获取所有用户
            cursor.execute("""
                SELECT id, username, email, name, status, password_hash, role_id, company_id, department_id 
                FROM user;
            """)
            users = cursor.fetchall()
            
            print("\n📋 用户列表:")
            print("-" * 80)
            for user in users:
                user_id, username, email, name, status, password_hash, role_id, company_id, department_id = user
                print(f"ID: {user_id}")
                print(f"   用户名: {username}")
                print(f"   邮箱: {email}")
                print(f"   姓名: {name}")
                print(f"   状态: {status}")
                print(f"   角色ID: {role_id}")
                print(f"   公司ID: {company_id}")
                print(f"   部门ID: {department_id}")
                print(f"   密码哈希: {password_hash[:50]}..." if password_hash else "   密码哈希: 无")
                
                # 测试密码
                if username == 'admin' and password_hash:
                    test_passwords = ['admin123', 'admin', '123456']
                    for pwd in test_passwords:
                        try:
                            result = check_password_hash(password_hash, pwd)
                            print(f"   测试密码 '{pwd}': {'✅ 正确' if result else '❌ 错误'}")
                        except Exception as e:
                            print(f"   测试密码 '{pwd}': ❌ 验证失败 - {e}")
                
                print("-" * 40)
        else:
            print("❌ 用户表不存在")
        
        # 3. 检查角色表
        if ('role',) in tables:
            cursor.execute("SELECT COUNT(*) FROM role;")
            role_count = cursor.fetchone()[0]
            print(f"\n🎭 角色总数: {role_count}")
            
            cursor.execute("SELECT id, name, code, description FROM role;")
            roles = cursor.fetchall()
            
            print("\n📋 角色列表:")
            print("-" * 60)
            for role in roles:
                role_id, name, code, description = role
                print(f"ID: {role_id} | 名称: {name} | 代码: {code} | 描述: {description}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_new_admin():
    """直接在数据库中创建新的管理员"""
    try:
        from werkzeug.security import generate_password_hash
        
        db_path = 'crm.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n🔧 创建新的管理员用户")
        print("="*50)
        
        # 1. 查找超级管理员角色
        cursor.execute("SELECT id FROM role WHERE code = 'super_admin';")
        role_result = cursor.fetchone()
        if not role_result:
            print("❌ 超级管理员角色不存在")
            return False
        
        role_id = role_result[0]
        print(f"✅ 找到超级管理员角色ID: {role_id}")
        
        # 2. 删除现有的admin用户
        cursor.execute("DELETE FROM user WHERE username = 'admin';")
        print("🗑️  删除现有admin用户")
        
        # 3. 创建新的admin用户
        username = 'admin'
        password = 'admin123'
        email = '<EMAIL>'
        name = '系统管理员'
        status = 'active'
        password_hash = generate_password_hash(password)
        
        cursor.execute("""
            INSERT INTO user (username, email, name, status, password_hash, role_id)
            VALUES (?, ?, ?, ?, ?, ?);
        """, (username, email, name, status, password_hash, role_id))
        
        conn.commit()
        
        # 4. 验证创建结果
        cursor.execute("SELECT id, username, status FROM user WHERE username = 'admin';")
        result = cursor.fetchone()
        if result:
            user_id, username, status = result
            print(f"✅ 管理员用户创建成功")
            print(f"   ID: {user_id}")
            print(f"   用户名: {username}")
            print(f"   状态: {status}")
            print(f"   密码: {password}")
            
            # 测试密码
            test_result = check_password_hash(password_hash, password)
            print(f"   密码验证: {'✅ 通过' if test_result else '❌ 失败'}")
        else:
            print("❌ 创建失败")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    # 检查数据库
    check_sqlite_db()
    
    # 询问是否重新创建管理员
    print("\n" + "="*60)
    choice = input("是否重新创建管理员用户？(y/N): ").lower().strip()
    if choice in ['y', 'yes']:
        create_new_admin()
        print("\n🎯 请重新启动应用并尝试登录")
