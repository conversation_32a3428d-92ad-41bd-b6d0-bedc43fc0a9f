# -*- coding: utf-8 -*-
from flask import render_template, redirect, url_for, flash, request, current_app, session, send_file
from flask_login import login_user, logout_user, login_required, current_user
from models import db, User
from datetime import datetime, timezone
from utils.behavior_captcha import BehaviorCaptcha
from utils.captcha_utils import generate_captcha
import logging
import re
import random
import string
import requests
import io
from werkzeug.security import check_password_hash

def get_utc_now():
    """获取当前UTC时间"""
    return datetime.now(timezone.utc)

def validate_password(password):
    """验证密码复杂度"""
    if len(password) < 8:
        return False, "密码长度至少为8个字符"
    if not any(c.isupper() for c in password):
        return False, "密码必须包含至少一个大写字母"
    if not any(c.islower() for c in password):
        return False, "密码必须包含至少一个小写字母"
    if not any(c.isdigit() for c in password):
        return False, "密码必须包含至少一个数字"
    if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?~`\"\\/')" for c in password):
        return False, "密码必须包含至少一个特殊字符，如：!@#$%^&*()_+-=[]{}|;:,.<>?~`\"\\/')"
    return True, "密码符合要求"

def validate_email(email):
    """验证邮箱格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_phone(phone):
    """验证手机号格式"""
    pattern = r'^1[3-9]\d{9}$'
    return re.match(pattern, phone) is not None

def get_login_type(identifier):
    """判断登录类型"""
    if validate_email(identifier): return 'email'
    if validate_phone(identifier): return 'phone'
    return None

CAPTCHA_SECRET_KEY = 'YOUR_CAPTCHA_SECRET_KEY'  # 腾讯验证码密钥
CAPTCHA_APPID = 'YOUR_CAPTCHA_APPID'  # 腾讯验证码应用ID

def verify_tcaptcha(ticket, randstr, user_ip):
    """验证腾讯验证码"""
    params = {
        'aid': CAPTCHA_APPID,
        'AppSecretKey': CAPTCHA_SECRET_KEY,
        'Ticket': ticket,
        'Randstr': randstr,
        'UserIP': user_ip
    }
    response = requests.get('https://ssl.captcha.qq.com/ticket/verify', params=params)
    result = response.json()
    return result['response'] == '1'

def handle_login():
    """处理用户登录"""
    try:
        # 只在GET请求时检查是否已认证
        if request.method == 'GET' and current_user.is_authenticated:
            return redirect(url_for('index'))

        if request.method == 'POST':
            identifier = request.form.get('identifier')
            password = request.form.get('password')
            captcha = request.form.get('captcha')
            
            if not all([identifier, password]):
                flash('请输入用户名和密码', 'danger')
                return render_template('login.html')
            
            # 获取验证码配置
            enable_captcha = current_app.config.get('ENABLE_CAPTCHA', False)
            captcha_attempts = current_app.config.get('CAPTCHA_ATTEMPTS', 3)
            
            # 判断是否需要验证码
            if enable_captcha and 'login_attempts' in session and session['login_attempts'] >= captcha_attempts:
                if not captcha:
                    flash('请输入验证码', 'danger')
                    return render_template('login.html', show_captcha=True)
                
                if captcha.lower() != session.get('captcha_code', '').lower():
                    flash('验证码错误', 'danger')
                    return render_template('login.html', show_captcha=True)
            
            # 查询用户 - 支持用户名、邮箱、手机号登录
            user = User.query.filter(
                (User.username == identifier) |
                (User.email == identifier) |
                (User.phone == identifier)
            ).first()

            if user and user.check_password(password):
                login_user(user)
                session.pop('login_attempts', None)
                session.pop('captcha_code', None)
                return redirect(url_for('index'))
            
            # 登录失败，增加失败次数
            session['login_attempts'] = session.get('login_attempts', 0) + 1
            
            if enable_captcha and session['login_attempts'] >= captcha_attempts:
                flash('用户名或密码错误，请输入验证码', 'danger')
                return render_template('login.html', show_captcha=True)
            
            flash('用户名或密码错误', 'danger')
            return render_template('login.html')
        
        # GET请求
        enable_captcha = current_app.config.get('ENABLE_CAPTCHA', False)
        captcha_attempts = current_app.config.get('CAPTCHA_ATTEMPTS', 3)
        show_captcha = enable_captcha and session.get('login_attempts', 0) >= captcha_attempts
        return render_template('login.html', show_captcha=show_captcha)
        
    except Exception as e:
        current_app.logger.error('登录过程发生错误: {}'.format(str(e)), exc_info=True)
        flash('系统错误，请稍后重试', 'danger')
        return render_template('login.html')

def handle_logout():
    """处理用户登出"""
    logout_user()
    return redirect(url_for('login'))

def handle_change_password():
    """处理密码修改"""
    if request.method == 'POST':
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        
        if password != confirm_password:
            flash('两次输入的密码不一致', 'danger')
            return render_template('change_password.html')
        
        # 简化的密码验证
        if len(password) < 6:
            flash('密码长度至少6位', 'danger')
            return render_template('change_password.html')

        if len(password) > 50:
            flash('密码长度不能超过50位', 'danger')
            return render_template('change_password.html')
        
        user_id = session.get('temp_user_id') or current_user.id
        user = User.query.get(user_id)
        if user:
            user.set_password(password)
            user.password_changed_at = get_utc_now()
            db.session.commit()
            flash('密码修改成功', 'success')
            
            if 'temp_user_id' in session:
                session.pop('temp_user_id')
                return redirect(url_for('login'))
            return redirect(url_for('index'))
    
    return render_template('change_password.html')

def create_admin(username, email, password):
    """创建管理员账户"""
    admin = User(username=username, email=email, role='admin')
    admin.set_password(password)
    
    db.session.add(admin)
    db.session.commit()
    return True

def get_captcha():
    """获取验证码图片"""
    try:
        img_data, code = generate_captcha()
        session['captcha_code'] = code
        return send_file(
            io.BytesIO(img_data),
            mimetype='image/png',
            cache_timeout=0  # 禁用缓存
        )
    except Exception as e:
        current_app.logger.error('生成验证码错误: {}'.format(str(e)), exc_info=True)
        return '', 500 