from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app as app
from flask_login import login_required, current_user
from models import db, LeadPool, Lead, Company, User, Activity, LeadTransferHistory
from datetime import datetime, timezone, timedelta
from sqlalchemy import or_, func, desc
from functools import wraps

bp = Blueprint('pool_management', __name__)

# 定义admin_required装饰器
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if current_user.role.code not in ['super_admin', 'company_admin', 'department_admin']:
            flash('您没有权限访问此页面', 'danger')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

@bp.route('/')
@login_required
def index():
    """线索池管理首页"""
    # 检查权限
    if current_user.role.code not in ['super_admin', 'company_admin']:
        flash('您没有权限访问此页面', 'danger')
        return redirect(url_for('index'))
    
    # 根据用户角色获取线索池列表
    if current_user.role.code == 'super_admin':
        pools = LeadPool.query.all()
    else:
        pools = LeadPool.query.filter_by(company_id=current_user.company_id).all()
    
    return render_template('pool_management/index.html', pools=pools)

@bp.route('/add', methods=['GET', 'POST'])
@login_required
def add_pool():
    """添加线索池"""
    # 检查权限
    if current_user.role.code not in ['super_admin', 'company_admin']:
        flash('您没有权限执行此操作', 'danger')
        return redirect(url_for('pool_management.index'))
    
    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        company_id = request.form.get('company_id') if current_user.role.code == 'super_admin' else current_user.company_id
        is_public_sea = request.form.get('is_public_sea', type=bool)
        
        try:
            pool = LeadPool(
                name=name,
                description=description,
                company_id=company_id,
                is_public_sea=is_public_sea
            )
            db.session.add(pool)
            db.session.commit()
            flash('线索池创建成功', 'success')
            return redirect(url_for('pool_management.index'))
        except Exception as e:
            db.session.rollback()
            flash(f'创建失败：{str(e)}', 'danger')
    
    # 获取可选的公司列表
    companies = Company.query.all() if current_user.role.code == 'super_admin' else [current_user.company]
    return render_template('pool_management/form.html', companies=companies)

@bp.route('/edit/<int:pool_id>', methods=['GET', 'POST'])
@login_required
def edit_pool(pool_id):
    """编辑线索池"""
    pool = LeadPool.query.get_or_404(pool_id)
    
    # 检查权限
    if current_user.role.code != 'super_admin' and pool.company_id != current_user.company_id:
        flash('您没有权限编辑此线索池', 'danger')
        return redirect(url_for('pool_management.index'))
    
    if request.method == 'POST':
        pool.name = request.form.get('name')
        pool.description = request.form.get('description')
        if current_user.role.code == 'super_admin':
            pool.company_id = request.form.get('company_id', type=int)
        pool.is_public_sea = request.form.get('is_public_sea', type=bool)
        
        try:
            db.session.commit()
            flash('线索池更新成功', 'success')
            return redirect(url_for('pool_management.index'))
        except Exception as e:
            db.session.rollback()
            flash(f'更新失败：{str(e)}', 'danger')
    
    # 获取可选的公司列表
    companies = Company.query.all() if current_user.role.code == 'super_admin' else [current_user.company]
    return render_template('pool_management/form.html', pool=pool, companies=companies)

@bp.route('/delete/<int:pool_id>', methods=['POST'])
@login_required
def delete_pool(pool_id):
    """删除线索池"""
    pool = LeadPool.query.get_or_404(pool_id)
    
    # 检查权限
    if current_user.role.code != 'super_admin' and pool.company_id != current_user.company_id:
        flash('您没有权限删除此线索池', 'danger')
        return redirect(url_for('pool_management.index'))
    
    # 检查是否为公海池
    if pool.is_public_sea:
        flash('不能删除公海池', 'danger')
        return redirect(url_for('pool_management.index'))
    
    try:
        # 将该池中的线索移动到公海池
        public_sea = LeadPool.query.filter_by(company_id=pool.company_id, is_public_sea=True).first()
        if public_sea:
            Lead.query.filter_by(pool_id=pool.id).update({'pool_id': public_sea.id})
        
        db.session.delete(pool)
        db.session.commit()
        flash('线索池删除成功', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'删除失败：{str(e)}', 'danger')
    
    return redirect(url_for('pool_management.index'))

@bp.route('/<int:pool_id>/leads')
@login_required
def pool_leads(pool_id):
    """查看线索池中的线索"""
    pool = LeadPool.query.get_or_404(pool_id)
    
    # 检查权限
    if current_user.role.code not in ['super_admin', 'company_admin'] or (current_user.role.code == 'company_admin' and pool.company_id != current_user.company_id):
        flash('您没有权限查看此线索池', 'danger')
        return redirect(url_for('pool_management.index'))
    
    # 获取搜索和分页参数
    search = request.args.get('search', '')
    page = request.args.get('page', 1, type=int)
    
    # 构建查询
    query = Lead.query.filter_by(pool_id=pool_id)
    
    # 如果有搜索条件，添加过滤
    if search:
        query = query.filter(or_(
            Lead.name.ilike(f'%{search}%'),
            Lead.company_name.ilike(f'%{search}%'),
            Lead.email.ilike(f'%{search}%'),
            Lead.phone.ilike(f'%{search}%')
        ))
    
    # 分页
    pagination = query.order_by(Lead.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    
    # 加密电话号码
    mask_phone_number = getattr(app, 'mask_phone_number', None)
    if mask_phone_number is None:
        # 如果找不到函数，使用默认实现
        def mask_phone_number(phone):
            if not phone or len(phone) < 7: return phone
            return phone[:3] + '*' * (len(phone) - 6) + phone[-3:]
    
    for lead in pagination.items:
        if lead.phone:
            lead.masked_phone = mask_phone_number(lead.phone)
    
    # 获取可分配的用户列表
    if current_user.role.code == 'super_admin':
        users = User.query.filter(User.role_id.notin_([1])).all()  # 排除超级管理员
    elif current_user.role.code == 'company_admin':
        users = User.query.filter_by(company_id=current_user.company_id).filter(User.role_id.notin_([1, 2])).all()
    else:  # 部门管理员
        users = User.query.filter_by(department_id=current_user.department_id).filter(User.role_id.notin_([1, 2, 3])).all()
    
    return render_template('pool_management/leads.html', 
                          pool=pool, 
                          leads=pagination.items,
                          pagination=pagination,
                          search=search,
                          users=users)

@bp.route('/<int:pool_id>/assign', methods=['POST'])
@login_required
def assign_leads(pool_id):
    """分配线索池中的线索"""
    pool = LeadPool.query.get_or_404(pool_id)
    
    # 检查权限
    if current_user.role.code not in ['super_admin', 'company_admin'] or (current_user.role.code == 'company_admin' and pool.company_id != current_user.company_id):
        flash('您没有权限分配此线索池中的线索', 'danger')
        return redirect(url_for('pool_management.index'))
    
    user_id = request.form.get('user_id', type=int)
    lead_ids = request.form.getlist('lead_ids[]')
    
    # 记录调试信息
    app.logger.info(f'线索池分配 - 池ID: {pool_id}, 目标用户: {user_id}, 线索IDs: {lead_ids}')
    
    if not user_id or not lead_ids:
        flash('请选择要分配的线索和目标用户', 'danger')
        return redirect(url_for('pool_management.pool_leads', pool_id=pool_id))
    
    try:
        user = User.query.get(user_id)
        if not user:
            flash('找不到目标用户', 'danger')
            return redirect(url_for('pool_management.pool_leads', pool_id=pool_id))
        
        success_count = 0
        for lead_id in lead_ids:
            try:
                lead_id = int(lead_id) if lead_id else None
                lead = Lead.query.get(lead_id)
                if lead and lead.pool_id == pool_id:
                    # 记录转移历史
                    transfer_history = LeadTransferHistory(
                        lead_id=lead.id,
                        from_pool_id=pool_id,
                        to_pool_id=None,
                        from_user_id=None,
                        to_user_id=user_id,
                        transfer_type='assign',
                        created_by=current_user.id,
                        notes='从线索池分配'
                    )
                    db.session.add(transfer_history)
                    
                    # 更新线索状态
                    lead.owner_id = user_id
                    lead.company_id = user.company_id
                    lead.pool_id = None
                    lead.is_in_public_sea = False
                    lead.public_sea_time = None
                    
                    # 添加活动记录
                    activity = Activity(
                        lead_id=lead.id,
                        user_id=current_user.id,
                        description=f'将线索从{pool.name}分配给{user.username}'
                    )
                    db.session.add(activity)
                    success_count += 1
            except Exception as e:
                app.logger.error(f'分配线索 {lead_id} 失败: {str(e)}')
        
        db.session.commit()
        flash(f'成功分配 {success_count} 条线索给 {user.username}', 'success')
    except Exception as e:
        db.session.rollback()
        app.logger.error(f'线索分配失败: {str(e)}', exc_info=True)
        flash(f'分配失败：{str(e)}', 'danger')
    
    return redirect(url_for('pool_management.pool_leads', pool_id=pool_id)) 