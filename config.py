import os

class Config:
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'jeyue_crm_secret_key_2024'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 数据库配置
    DB_USER = os.environ.get('DB_USER') or 'sql_crm_jeyue_ne'  
    DB_PASSWORD = os.environ.get('DB_PASSWORD') or 'fWyErEz75PkY8DDc'
    DB_HOST = os.environ.get('DB_HOST') or 'localhost'
    DB_NAME = os.environ.get('DB_NAME') or 'sql_crm_jeyue_ne'
    
    # 默认使用SQLite数据库
    SQLALCHEMY_DATABASE_URI = 'sqlite:///crm.db'
    
    # 如果明确设置使用MySQL，则使用MySQL
    if os.environ.get('DB_USE_MYSQL', '').lower() == 'true':
        SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or f'mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}/{DB_NAME}'
    
    # 安全配置
    SESSION_COOKIE_SECURE = False  # 宝塔面板可能没有HTTPS
    REMEMBER_COOKIE_SECURE = False
    SESSION_COOKIE_HTTPONLY = True
    REMEMBER_COOKIE_HTTPONLY = True
    
    # 验证码配置
    ENABLE_CAPTCHA = os.environ.get('ENABLE_CAPTCHA', '').lower() == 'true'  # 默认关闭验证码功能
    CAPTCHA_ATTEMPTS = int(os.environ.get('CAPTCHA_ATTEMPTS', '999'))  # 触发验证码的登录失败次数，设置较大值实际上禁用了验证码
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER') or os.path.join(os.getcwd(), 'uploads')
    
    # 日志配置
    LOG_FOLDER = os.environ.get('LOG_FOLDER') or os.path.join(os.getcwd(), 'logs')
    
    @staticmethod
    def init_app(app):
        # 创建上传文件夹
        if not os.path.exists(Config.UPLOAD_FOLDER):
            os.makedirs(Config.UPLOAD_FOLDER)
            os.chmod(Config.UPLOAD_FOLDER, 0o755)
        
        # 创建日志文件夹
        if not os.path.exists(Config.LOG_FOLDER):
            os.makedirs(Config.LOG_FOLDER)
            os.chmod(Config.LOG_FOLDER, 0o755)

class DevelopmentConfig(Config):
    DEBUG = True
    SESSION_COOKIE_SECURE = False
    REMEMBER_COOKIE_SECURE = False

class ProductionConfig(Config):
    DEBUG = False
    # 使用与基础配置相同的数据库设置
    
    # 生产环境下的文件路径
    UPLOAD_FOLDER = '/www/wwwroot/crm.jeyue.net/uploads'
    LOG_FOLDER = '/www/wwwroot/crm.jeyue.net/logs'

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': ProductionConfig  # 默认使用生产环境配置
}