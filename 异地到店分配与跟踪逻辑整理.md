# 异地到店分配与跟踪逻辑整理

## 1. 业务流程概述

### 1.1 核心概念
- **A公司**: 线索原始录入公司，拥有线索所有权
- **B公司**: 目标处理公司，负责异地到店服务
- **异地到店池**: 每个公司都有的特殊线索池，用于接收其他公司推送的异地线索
- **跨公司处理**: 线索所有权不变，但处理权临时转移

### 1.2 业务场景
1. A公司录入线索，客户在B公司所在城市
2. A公司将意向客户推送到B公司异地到店池
3. B公司员工认领并跟进线索
4. 完成服务后反馈结果给A公司

## 2. 数据库设计

### 2.1 核心字段

#### Lead表关键字段
```sql
-- 基础信息
company_id                      -- 线索所属公司（A公司，不变）
original_company_id             -- 原始录入公司（A公司）
current_processing_company_id   -- 当前处理公司（B公司）
owner_id                        -- 当前负责人
pool_id                         -- 所在线索池

-- 异地到店相关
last_cross_location_push_time   -- 最近推送时间
```

#### LeadPool表
```sql
pool_type                       -- 池类型：CROSS_LOCATION_PENDING
company_id                      -- 所属公司
is_public_sea                   -- 是否公海（异地池为false）
```

#### LeadTransferHistory表
```sql
transfer_type                   -- 转移类型
target_company_id_for_processing -- 目标处理公司
from_pool_id / to_pool_id       -- 源池/目标池
from_user_id / to_user_id       -- 源用户/目标用户
```

### 2.2 转移类型定义
- `PUSH_TO_CROSS_PENDING`: 推送到异地池
- `CLAIM_FROM_CROSS_PENDING`: 从异地池认领
- `RECALL_FROM_CROSS_PENDING`: 从异地池撤回

## 3. 核心功能实现

### 3.1 推送到异地池

#### 触发条件
- 线索状态为意向客户 (`is_intentional = true`)
- 推送者为A公司员工
- 目标公司B存在且不同于A公司

#### 实现逻辑 (`push_lead_to_cross_location_pool`)
```python
def push_lead_to_cross_location_pool(lead_id, target_company_id, pushing_user):
    # 1. 验证权限和状态
    # 2. 获取目标公司异地池
    # 3. 更新线索状态
    lead.owner_id = None  # 清除负责人
    lead.pool_id = target_pool.id  # 进入异地池
    lead.current_processing_company_id = target_company_id  # 标记处理公司
    lead.last_cross_location_push_time = get_shanghai_now()
    
    # 4. 记录转移历史
    # 5. 发送通知给目标公司管理员
```

#### 权限控制
- 公司管理员和超级管理员可推送
- 不能推送到自己公司
- 线索不能重复推送

### 3.2 认领异地线索

#### 触发条件
- 线索在B公司异地池中
- 线索未被认领 (`owner_id = None`)
- 认领者为B公司员工

#### 实现逻辑 (`claim_lead_from_cross_location_pool`)
```python
def claim_lead_from_cross_location_pool(lead_id, claiming_user):
    # 1. 验证线索状态和权限
    # 2. 更新线索状态
    lead.owner_id = claiming_user.id  # 设置负责人
    lead.pool_id = None  # 从池中移除
    # company_id 保持不变（A公司）
    # current_processing_company_id 保持不变（B公司）
    
    # 3. 记录转移历史
    # 4. 创建活动记录
```

### 3.3 撤回异地线索

#### 触发条件
- 线索在异地池中且未被认领
- 撤回者为A公司管理员
- 线索状态允许撤回

#### 实现逻辑 (`recall_lead_from_cross_location`)
```python
def recall_lead_from_cross_location(lead_id, recalling_user):
    # 1. 验证权限（仅A公司管理员）
    # 2. 检查线索状态（未被认领）
    # 3. 恢复线索状态
    lead.owner_id = previous_owner_id  # 恢复原负责人
    lead.pool_id = None  # 或放入A公司池
    lead.current_processing_company_id = None  # 清除处理公司
    
    # 4. 记录转移历史
```

## 4. 页面展示与交互

### 4.1 异地到店池页面 (`/cross-location-pool`)

#### 功能特性
- 显示当前公司异地池中的待认领线索
- 支持按来源公司、推送时间、阶段筛选
- 支持搜索客户信息
- 批量认领功能
- 实时更新线索状态

#### 数据查询逻辑
```python
# 获取当前公司异地池中的待认领线索
query = Lead.query.filter(
    Lead.pool_id == company_cross_pool.id,
    Lead.owner_id == None,  # 未被认领
    Lead.current_processing_company_id == current_user.company_id
)
```

### 4.2 线索详情页推送功能

#### 推送条件检查
- 线索为意向客户
- 当前用户有推送权限
- 线索未被推送到其他公司

#### 交互流程
1. 点击"推送到异地"按钮
2. 搜索选择目标公司
3. 确认推送
4. 系统自动发送通知

## 5. 通知机制

### 5.1 推送通知
- **接收方**: 目标公司所有管理员
- **通知方式**: 站内通知 + 邮件通知
- **内容**: 推送公司、推送人、线索信息、客户位置

### 5.2 认领通知
- **接收方**: 原推送公司相关人员
- **通知内容**: 认领人信息、认领时间

## 6. 权限控制

### 6.1 推送权限
- **超级管理员**: 可推送任意线索到任意公司
- **公司管理员**: 可推送本公司线索到其他公司
- **普通员工**: 无推送权限

### 6.2 认领权限
- **所有员工**: 可认领本公司异地池中的线索

### 6.3 撤回权限
- **超级管理员**: 可撤回任意异地线索
- **公司管理员**: 可撤回本公司推送的异地线索

## 7. 状态跟踪

### 7.1 线索状态标识
```python
# 异地线索的状态特征
lead.company_id != lead.current_processing_company_id  # 跨公司处理
lead.pool_id != None and pool.pool_type == 'CROSS_LOCATION_PENDING'  # 在异地池中
lead.owner_id == None  # 待认领状态
```

### 7.2 历史记录跟踪
- 推送记录: `PUSH_TO_CROSS_PENDING`
- 认领记录: `CLAIM_FROM_CROSS_PENDING`
- 撤回记录: `RECALL_FROM_CROSS_PENDING`

## 8. API接口

### 8.1 核心API
- `POST /leads/<id>/push-to-cross-location`: 推送到异地池
- `POST /leads/<id>/claim-cross-location`: 认领异地线索
- `POST /leads/<id>/recall-cross-location`: 撤回异地线索
- `GET /api/leads/cross-location`: 获取异地池线索列表

### 8.2 批量操作API
- `POST /api/leads/batch-claim-cross-location`: 批量认领异地线索

## 9. 业务规则

### 9.1 推送规则
1. 只有意向客户才能推送
2. 不能推送到自己公司
3. 同一线索不能同时推送到多个公司
4. 推送后原公司保留所有权

### 9.2 认领规则
1. 只能认领本公司异地池中的线索
2. 认领后成为线索负责人
3. 认领的线索不能再次推送

### 9.3 撤回规则
1. 只能撤回未被认领的线索
2. 撤回后恢复原状态
3. 已认领的线索需要协商处理

## 10. 监控与统计

### 10.1 关键指标
- 异地推送数量
- 认领率和认领时效
- 撤回率
- 异地转化率

### 10.2 报表功能
- 异地到店推送统计
- 各公司异地池活跃度
- 异地线索处理效率分析

## 11. 技术实现细节

### 11.1 数据库初始化
```python
# 为每个公司创建异地到店池
def create_cross_location_pools_for_companies():
    companies = Company.query.all()
    for company in companies:
        existing_pool = LeadPool.query.filter_by(
            company_id=company.id,
            pool_type='CROSS_LOCATION_PENDING'
        ).first()

        if not existing_pool:
            new_pool = LeadPool(
                name=f"{company.name} - 异地到店池",
                description="接收其他公司推送的异地到店线索",
                company_id=company.id,
                is_public_sea=False,
                pool_type='CROSS_LOCATION_PENDING'
            )
            db.session.add(new_pool)
```

### 11.2 前端交互实现

#### 异地池页面筛选功能
```javascript
function filterLeads() {
    const sourceCompanyFilter = $('#source-company-filter').val();
    const timeFilter = $('#time-filter').val();
    const stageFilter = $('#stage-filter').val();
    const searchText = $('#search-input').val().toLowerCase();

    $('.lead-item').each(function() {
        const $item = $(this);
        let show = true;

        // 来源公司筛选
        if (sourceCompanyFilter && $item.data('source-company') != sourceCompanyFilter)
            show = false;

        // 时间筛选逻辑
        if (timeFilter && $item.data('push-time')) {
            const pushDate = new Date($item.data('push-time'));
            // 根据timeFilter值进行时间范围判断
        }

        $item.toggle(show);
    });
}
```

#### 批量认领功能
```javascript
function batchClaimLeads() {
    const selectedLeads = $('.lead-checkbox:checked').map(function() {
        return $(this).val();
    }).get();

    if (selectedLeads.length === 0) {
        alert('请选择要认领的线索');
        return;
    }

    // 发送批量认领请求
    $.post('/api/leads/batch-claim-cross-location', {
        lead_ids: selectedLeads
    }).done(function(response) {
        if (response.success) {
            location.reload();
        }
    });
}
```

### 11.3 邮件通知模板
```python
# 异地推送邮件通知
def send_cross_location_push_notification(target_admins, lead, pushing_user):
    for admin_user in target_admins:
        if admin_user.email:
            msg = Message(
                subject=f'异地到店线索推送通知 - {lead.name}',
                recipients=[admin_user.email],
                html=render_template('emails/cross_location_push.html',
                    admin_name=admin_user.username,
                    lead=lead,
                    pushing_company=pushing_user.company.name,
                    pushing_user=pushing_user.username
                )
            )
            current_app.mail.send(msg)
```

## 12. 异常处理与容错

### 12.1 常见异常场景
1. **目标公司异地池不存在**: 自动创建或提示管理员配置
2. **线索状态冲突**: 检查并提示当前状态
3. **权限不足**: 明确提示权限要求
4. **网络异常**: 重试机制和用户友好提示

### 12.2 数据一致性保障
```python
# 使用数据库事务确保操作原子性
try:
    # 更新线索状态
    # 创建历史记录
    # 发送通知
    db.session.commit()
except Exception as e:
    db.session.rollback()
    app_logger.error(f'异地推送失败: {str(e)}', exc_info=True)
    raise
```

## 13. 性能优化

### 13.1 查询优化
```python
# 使用索引优化异地池查询
query = Lead.query.filter(
    Lead.pool_id == company_cross_pool.id,
    Lead.owner_id == None,
    Lead.current_processing_company_id == current_user.company_id
).options(
    joinedload(Lead.company),  # 预加载关联数据
    joinedload(Lead.pool)
)
```

### 13.2 缓存策略
- 公司列表缓存
- 异地池配置缓存
- 用户权限缓存

## 14. 安全考虑

### 14.1 数据安全
- 敏感信息脱敏显示
- 操作日志记录
- 权限严格控制

### 14.2 业务安全
- 防止恶意推送
- 限制推送频率
- 审计跟踪完整

## 15. 扩展功能

### 15.1 自动分配
- 基于地理位置自动推荐目标公司
- 智能负载均衡分配

### 15.2 服务质量监控
- 异地服务评价系统
- 响应时效监控
- 客户满意度跟踪

### 15.3 结算管理
- 异地服务费用结算
- 佣金分配机制
- 财务对账功能
