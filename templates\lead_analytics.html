{% extends "base.html" %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">线索分析</h1>
    <div class="row">
        <div class="col-xl-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-funnel me-1"></i>
                    销售漏斗
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <canvas id="salesFunnel" width="100%" height="50"></canvas>
                        </div>
                        <div class="col-md-4">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>阶段</th>
                                            <th>数量</th>
                                            <th>转化率</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>总线索</td>
                                            <td>{{ total_leads }}</td>
                                            <td>100%</td>
                                        </tr>
                                        <tr>
                                            <td>已拨电话</td>
                                            <td>{{ called_leads }}</td>
                                            <td>{{ (called_leads/total_leads*100)|round(1) if total_leads > 0 else 0 }}%</td>
                                        </tr>
                                        <tr>
                                            <td>有效通话</td>
                                            <td>{{ valid_call_leads }}</td>
                                            <td>{{ (valid_call_leads/called_leads*100)|round(1) if called_leads > 0 else 0 }}%</td>
                                        </tr>
                                        <tr>
                                            <td>添加微信</td>
                                            <td>{{ wechat_added_leads }}</td>
                                            <td>{{ (wechat_added_leads/valid_call_leads*100)|round(1) if valid_call_leads > 0 else 0 }}%</td>
                                        </tr>
                                        <tr>
                                            <td>意向客户</td>
                                            <td>{{ intentional_leads }}</td>
                                            <td>{{ (intentional_leads/wechat_added_leads*100)|round(1) if wechat_added_leads > 0 else 0 }}%</td>
                                        </tr>
                                        <tr>
                                            <td>确认到面</td>
                                            <td>{{ visited_leads }}</td>
                                            <td>{{ (visited_leads/intentional_leads*100)|round(1) if intentional_leads > 0 else 0 }}%</td>
                                        </tr>
                                        <tr>
                                            <td>信息合规</td>
                                            <td>{{ compliant_leads }}</td>
                                            <td>{{ (compliant_leads/visited_leads*100)|round(1) if visited_leads > 0 else 0 }}%</td>
                                        </tr>
                                        <tr>
                                            <td>完成签约</td>
                                            <td>{{ deal_done_leads }}</td>
                                            <td>{{ (deal_done_leads/compliant_leads*100)|round(1) if compliant_leads > 0 else 0 }}%</td>
                                        </tr>
                                        <tr>
                                            <td>完成提车</td>
                                            <td>{{ car_selected_leads }}</td>
                                            <td>{{ (car_selected_leads/deal_done_leads*100)|round(1) if deal_done_leads > 0 else 0 }}%</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-bar me-1"></i>
                    每日新增线索
                </div>
                <div class="card-body">
                    <canvas id="dailyLeads" width="100%" height="50"></canvas>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-pie me-1"></i>
                    线索来源分布
                </div>
                <div class="card-body">
                    <canvas id="leadSources" width="100%" height="50"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 线索列表部分 -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-list me-1"></i>
                    线索明细
                </div>
                <div class="card-body">
                    <!-- 筛选表单 -->
                    <form method="get" class="row g-3 mb-4">
                        <div class="col-md-3">
                            <label for="stage" class="form-label">当前阶段</label>
                            <select class="form-select" id="stage" name="stage">
                                <option value="">全部阶段</option>
                                <option value="not_called" {% if request.args.get('stage') == 'not_called' %}selected{% endif %}>未拨打</option>
                                <option value="not_connected" {% if request.args.get('stage') == 'not_connected' %}selected{% endif %}>未接通</option>
                                <option value="invalid_call" {% if request.args.get('stage') == 'invalid_call' %}selected{% endif %}>无效通话</option>
                                <option value="not_wechat" {% if request.args.get('stage') == 'not_wechat' %}selected{% endif %}>未加微</option>
                                <option value="not_intentional" {% if request.args.get('stage') == 'not_intentional' %}selected{% endif %}>非意向</option>
                                <option value="not_compliant" {% if request.args.get('stage') == 'not_compliant' %}selected{% endif %}>不合规</option>
                                <option value="not_visited" {% if request.args.get('stage') == 'not_visited' %}selected{% endif %}>未到面</option>
                                <option value="not_deal_done" {% if request.args.get('stage') == 'not_deal_done' %}selected{% endif %}>未签约</option>
                                <option value="not_car_selected" {% if request.args.get('stage') == 'not_car_selected' %}selected{% endif %}>未提车</option>
                                <option value="deal_done" {% if request.args.get('stage') == 'deal_done' %}selected{% endif %}>签约</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="owner" class="form-label">负责人</label>
                            <select class="form-select" id="owner" name="owner_id">
                                <option value="">全部负责人</option>
                                {% for user in users %}
                                <option value="{{ user.id }}" {% if request.args.get('owner_id')|int == user.id %}selected{% endif %}>{{ user.username }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="search" class="form-label">搜索</label>
                            <input type="text" class="form-control" id="search" name="search" value="{{ request.args.get('search', '') }}" placeholder="名称、公司、邮箱或电话">
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">筛选</button>
                        </div>
                    </form>
                    
                    <!-- 线索列表 -->
                    {% if leads %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>名称</th>
                                    <th>公司</th>
                                    <th>联系方式</th>
                                    <th>当前阶段</th>
                                    <th>负责人</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for lead in leads %}
                                <tr>
                                    <td><a href="{{ url_for('leads.view_lead', lead_id=lead.id) }}">{{ lead.name }}</a></td>
                                    <td>{{ lead.company_name }}</td>
                                    <td>
                                        {% if lead.email %}<div><small><i class="bi bi-envelope"></i> {{ lead.email }}</small></div>{% endif %}
                                        {% if lead.phone %}<div><small><i class="bi bi-telephone"></i> {{ lead.masked_phone }}</small></div>{% endif %}
                                    </td>
                                    <td>
                                        <div class="funnel-stage">
                                            <div class="stage-name">{{ lead.current_stage }}</div>
                                            <div class="stage-progress">
                                                {% set total_stages = 9 %}
                                                {% set completed_stages = namespace(count=0) %}
                                                {% if lead.is_called %}{% set completed_stages.count = completed_stages.count + 1 %}{% endif %}
                                                {% if lead.is_connected %}{% set completed_stages.count = completed_stages.count + 1 %}{% endif %}
                                                {% if lead.is_valid_call %}{% set completed_stages.count = completed_stages.count + 1 %}{% endif %}
                                                {% if lead.is_wechat_added %}{% set completed_stages.count = completed_stages.count + 1 %}{% endif %}
                                                {% if lead.is_intentional %}{% set completed_stages.count = completed_stages.count + 1 %}{% endif %}
                                                {% if lead.is_compliant %}{% set completed_stages.count = completed_stages.count + 1 %}{% endif %}
                                                {% if lead.is_visited %}{% set completed_stages.count = completed_stages.count + 1 %}{% endif %}
                                                {% if lead.is_car_selected %}{% set completed_stages.count = completed_stages.count + 1 %}{% endif %}
                                                {% if lead.is_deal_done %}{% set completed_stages.count = completed_stages.count + 1 %}{% endif %}
                                                <div class="progress" style="height: 5px;">
                                                    <div class="progress-bar" role="progressbar" 
                                                         style="width: {{ (completed_stages.count / total_stages * 100)|round }}%"
                                                         aria-valuenow="{{ completed_stages.count }}" 
                                                         aria-valuemin="0" 
                                                         aria-valuemax="{{ total_stages }}">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ lead.owner.username if lead.owner else '未分配' }}</td>
                                    <td>{{ lead.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('leads.edit_lead', lead_id=lead.id) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            {% if current_user.role.code in ['super_admin', 'company_admin', 'department_admin'] %}
                                            <a href="{{ url_for('leads.assign_lead_route', lead_id=lead.id) }}" class="btn btn-sm btn-outline-info">
                                                <i class="bi bi-person-plus"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    {% if pagination.pages > 1 %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('analytics', page=pagination.prev_num, **request.args) }}">上一页</a>
                            </li>
                            {% endif %}

                            {% for page in pagination.iter_pages() %}
                                {% if page %}
                                    <li class="page-item {% if page == pagination.page %}active{% endif %}">
                                        <a class="page-link" href="{{ url_for('analytics', page=page, **request.args) }}">{{ page }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled"><span class="page-link">...</span></li>
                                {% endif %}
                            {% endfor %}

                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('analytics', page=pagination.next_num, **request.args) }}">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="alert alert-info">暂无线索数据</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.funnel-stage {
    min-width: 120px;
}

.stage-name {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.stage-progress {
    width: 100%;
}

.progress {
    background-color: #e9ecef;
    border-radius: 0.25rem;
}

.progress-bar {
    background-color: #0d6efd;
    border-radius: 0.25rem;
}
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 添加Bootstrap图标库
    const iconLink = document.createElement('link');
    iconLink.rel = 'stylesheet';
    iconLink.href = 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.0/font/bootstrap-icons.css';
    document.head.appendChild(iconLink);
    
    // 销售漏斗图
    new Chart(document.getElementById('salesFunnel'), {
        type: 'bar',
        data: {
            labels: ['总线索', '已拨电话', '有效通话', '添加微信', '意向客户', '确认到面', '信息合规', '完成签约', '完成提车'],
            datasets: [{
                label: '数量',
                data: [
                    {{ total_leads }},
                    {{ called_leads }},
                    {{ valid_call_leads }},
                    {{ wechat_added_leads }},
                    {{ intentional_leads }},
                    {{ visited_leads }},
                    {{ compliant_leads }},
                    {{ deal_done_leads }},
                    {{ car_selected_leads }}
                ],
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            indexAxis: 'y',
            scales: {
                x: {
                    beginAtZero: true
                }
            }
        }
    });

    // 每日新增线索图
    new Chart(document.getElementById('dailyLeads'), {
        type: 'line',
        data: {
            labels: {{ daily_dates|tojson }},
            datasets: [{
                label: '新增线索',
                data: {{ daily_counts|tojson }},
                fill: false,
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1
            }]
        }
    });

    // 线索来源分布图
    new Chart(document.getElementById('leadSources'), {
        type: 'pie',
        data: {
            labels: {{ source_labels|tojson }},
            datasets: [{
                data: {{ source_counts|tojson }},
                backgroundColor: [
                    'rgba(255, 99, 132, 0.5)',
                    'rgba(54, 162, 235, 0.5)',
                    'rgba(255, 206, 86, 0.5)',
                    'rgba(75, 192, 192, 0.5)',
                    'rgba(153, 102, 255, 0.5)'
                ]
            }]
        }
    });
});
</script>
{% endblock %} 