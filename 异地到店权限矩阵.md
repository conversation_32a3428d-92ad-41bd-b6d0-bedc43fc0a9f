# 异地到店权限矩阵

## 权限角色定义
- **超级管理员**: 系统最高权限，可操作所有公司数据
- **公司管理员**: 公司级管理权限，可管理本公司及跨公司业务
- **部门管理员**: 部门级管理权限，仅限本部门业务
- **普通员工**: 基础操作权限，处理分配给自己的线索

## 操作权限矩阵

| 操作 | 超级管理员 | 公司管理员 | 部门管理员 | 普通员工 | 备注 |
|------|------------|------------|------------|----------|------|
| **推送相关** |
| 推送线索到异地池 | ✅ 全部 | ✅ 本公司 | ❌ | ❌ | 需要线索为意向客户 |
| 查看推送历史 | ✅ 全部 | ✅ 本公司 | ✅ 本部门 | ✅ 自己的 | |
| 撤回异地线索 | ✅ 全部 | ✅ 本公司 | ❌ | ❌ | 仅限未认领状态 |
| **认领相关** |
| 认领异地线索 | ✅ 全部 | ✅ 本公司池 | ✅ 本公司池 | ✅ 本公司池 | 仅限本公司异地池 |
| 批量认领线索 | ✅ 全部 | ✅ 本公司池 | ✅ 本公司池 | ✅ 本公司池 | |
| 查看异地池 | ✅ 全部 | ✅ 本公司 | ✅ 本公司 | ✅ 本公司 | |
| **管理相关** |
| 创建异地池 | ✅ | ❌ | ❌ | ❌ | 系统自动创建 |
| 配置异地池 | ✅ | ✅ 本公司 | ❌ | ❌ | |
| 查看跨公司统计 | ✅ | ✅ 本公司 | ❌ | ❌ | |
| **通知相关** |
| 接收推送通知 | ✅ | ✅ | ❌ | ❌ | 仅公司管理员 |
| 发送推送通知 | ✅ | ✅ | ❌ | ❌ | 推送时自动发送 |

## 详细权限说明

### 1. 推送权限
```python
def can_push_to_cross_location(user, lead):
    # 超级管理员可推送任意线索
    if user.role.code == 'super_admin':
        return True
    
    # 公司管理员可推送本公司线索
    if user.role.code == 'company_admin' and lead.company_id == user.company_id:
        return True
    
    return False
```

### 2. 认领权限
```python
def can_claim_cross_location_lead(user, lead):
    # 线索必须在用户公司的异地池中
    if lead.current_processing_company_id != user.company_id:
        return False
    
    # 线索必须未被认领
    if lead.owner_id is not None:
        return False
    
    # 所有本公司员工都可以认领
    return True
```

### 3. 撤回权限
```python
def can_recall_cross_location_lead(user, lead):
    # 必须是线索原始公司的管理员
    if lead.company_id != user.company_id:
        return False
    
    # 必须是管理员级别
    if user.role.code not in ['super_admin', 'company_admin']:
        return False
    
    # 线索必须未被认领
    if lead.owner_id is not None:
        return False
    
    return True
```

## 数据访问权限

### 1. 异地池数据访问
- **查看权限**: 仅能查看本公司异地池
- **操作权限**: 仅能操作本公司异地池中的线索
- **统计权限**: 管理员可查看跨公司统计数据

### 2. 线索数据访问
- **原始数据**: 线索所属公司保持不变
- **处理权限**: 当前处理公司员工可操作
- **历史记录**: 根据用户权限级别显示相应范围

### 3. 敏感信息保护
- **手机号脱敏**: 非负责人查看时显示脱敏手机号
- **详细信息**: 仅负责人和管理员可查看完整信息
- **操作日志**: 记录所有敏感操作的详细日志

## 权限验证流程

### 1. 前端权限控制
```javascript
// 根据用户角色显示/隐藏操作按钮
function updateUIBasedOnPermissions(userRole, leadStatus) {
    const pushBtn = document.getElementById('pushToCrossBtn');
    const claimBtn = document.getElementById('claimBtn');
    const recallBtn = document.getElementById('recallBtn');
    
    // 推送按钮权限
    if (userRole === 'company_admin' || userRole === 'super_admin') {
        pushBtn.style.display = leadStatus.canPush ? 'block' : 'none';
    } else {
        pushBtn.style.display = 'none';
    }
    
    // 认领按钮权限
    claimBtn.style.display = leadStatus.canClaim ? 'block' : 'none';
    
    // 撤回按钮权限
    recallBtn.style.display = leadStatus.canRecall ? 'block' : 'none';
}
```

### 2. 后端权限验证
```python
@bp.route('/leads/<int:lead_id>/push-to-cross-location', methods=['POST'])
@login_required
def push_lead_to_cross_location_route(lead_id):
    lead = Lead.query.get_or_404(lead_id)
    
    # 权限检查
    if not can_push_to_cross_location(current_user, lead):
        flash('您没有权限推送此线索', 'danger')
        return redirect(url_for('leads.leads_unified'))
    
    # 执行推送逻辑
    # ...
```

## 异常情况处理

### 1. 权限变更
- 用户角色变更时，自动更新相关权限
- 公司调整时，重新评估跨公司权限

### 2. 数据一致性
- 定期检查异地池数据一致性
- 修复权限异常导致的数据问题

### 3. 安全审计
- 记录所有权限相关操作
- 定期审计异常权限使用情况
- 监控跨公司数据访问行为

## 权限配置建议

### 1. 最小权限原则
- 用户仅获得完成工作所需的最小权限
- 定期审查和调整权限配置

### 2. 职责分离
- 推送和认领权限分离
- 管理和操作权限分离

### 3. 审计跟踪
- 所有权限变更都有审计记录
- 敏感操作需要额外验证
