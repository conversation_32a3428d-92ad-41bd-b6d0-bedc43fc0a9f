{% extends 'base.html' %}

{% block title %}导入请求管理 - CRM系统{% endblock %}

{% block content %}
<!-- 页面标题区域 -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">
                <i class="fas fa-file-import me-2"></i>导入请求管理
            </h1>
            <p class="page-subtitle">管理线索导入请求，包括审批、处理和历史记录查看</p>
        </div>
        <div class="text-end">
            <div class="btn-group">
                <button type="button" class="btn btn-outline-light" onclick="refreshRequests()">
                    <i class="fas fa-sync-alt me-1"></i>刷新
                </button>
                <a href="{{ url_for('leads.leads_unified') }}" class="btn btn-outline-light">
                    <i class="fas fa-arrow-left me-1"></i>返回线索管理
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <i class="bi bi-clock text-warning fs-1"></i>
                    <h5 class="card-title text-warning">待审批</h5>
                    <h3 class="mb-0">{{ stats.pending_count }}</h3>
                    <small class="text-muted">需要处理</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <i class="bi bi-gear text-info fs-1"></i>
                    <h5 class="card-title text-info">处理中</h5>
                    <h3 class="mb-0">{{ stats.processing_count }}</h3>
                    <small class="text-muted">正在导入</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <i class="bi bi-check-circle text-success fs-1"></i>
                    <h5 class="card-title text-success">已完成</h5>
                    <h3 class="mb-0">{{ stats.completed_count }}</h3>
                    <small class="text-muted">导入成功</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-danger">
                <div class="card-body text-center">
                    <i class="bi bi-x-circle text-danger fs-1"></i>
                    <h5 class="card-title text-danger">已拒绝</h5>
                    <h3 class="mb-0">{{ stats.rejected_count }}</h3>
                    <small class="text-muted">审批未通过</small>
                </div>
            </div>
        </div>
    </div>

    <!-- 标签页导航 -->
    <div class="card">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" id="requestsTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="incoming-tab" data-bs-toggle="tab" data-bs-target="#incoming" type="button" role="tab">
                        <i class="bi bi-inbox"></i> 收到的请求 <span class="badge bg-warning ms-1">{{ stats.pending_count }}</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="outgoing-tab" data-bs-toggle="tab" data-bs-target="#outgoing" type="button" role="tab">
                        <i class="bi bi-send"></i> 发出的请求
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" type="button" role="tab">
                        <i class="bi bi-clock-history"></i> 历史记录
                    </button>
                </li>
            </ul>
        </div>
        
        <div class="card-body">
            <div class="tab-content" id="requestsTabContent">
                <!-- 收到的请求 -->
                <div class="tab-pane fade show active" id="incoming" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">收到的导入请求</h5>
                        <div class="d-flex gap-2">
                            <select class="form-select form-select-sm" id="incomingStatusFilter" style="width: auto;">
                                <option value="">全部状态</option>
                                <option value="pending">待审批</option>
                                <option value="processing">处理中</option>
                                <option value="completed">已完成</option>
                                <option value="rejected">已拒绝</option>
                            </select>
                        </div>
                    </div>
                    <div id="incomingRequestsContent">
                        <!-- 动态加载内容 -->
                        <div class="text-center py-4">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 发出的请求 -->
                <div class="tab-pane fade" id="outgoing" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">发出的导入请求</h5>
                        <div class="d-flex gap-2">
                            <select class="form-select form-select-sm" id="outgoingStatusFilter" style="width: auto;">
                                <option value="">全部状态</option>
                                <option value="pending">待审批</option>
                                <option value="processing">处理中</option>
                                <option value="completed">已完成</option>
                                <option value="rejected">已拒绝</option>
                            </select>
                        </div>
                    </div>
                    <div id="outgoingRequestsContent">
                        <!-- 动态加载内容 -->
                    </div>
                </div>

                <!-- 历史记录 -->
                <div class="tab-pane fade" id="history" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">所有导入记录</h5>
                        <div class="d-flex gap-2">
                            <input type="date" class="form-control form-control-sm" id="historyDateFilter" style="width: auto;">
                            <select class="form-select form-select-sm" id="historyStatusFilter" style="width: auto;">
                                <option value="">全部状态</option>
                                <option value="completed">已完成</option>
                                <option value="rejected">已拒绝</option>
                            </select>
                        </div>
                    </div>
                    <div id="historyRequestsContent">
                        <!-- 动态加载内容 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 审批模态框 -->
<div class="modal fade" id="approveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">审批导入请求</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="approveRequestDetails">
                    <!-- 动态内容 -->
                </div>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> 批准后，系统将自动导入线索并分配给您。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" id="confirmApprove">
                    <i class="bi bi-check-circle"></i> 批准导入
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 拒绝模态框 -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">拒绝导入请求</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="rejectRequestDetails">
                    <!-- 动态内容 -->
                </div>
                <div class="mb-3">
                    <label for="rejectReason" class="form-label">拒绝原因 <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="rejectReason" rows="3" placeholder="请说明拒绝的原因..." required></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmReject">
                    <i class="bi bi-x-circle"></i> 拒绝请求
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
}

.nav-tabs .nav-link.active {
    background-color: transparent;
    border-bottom: 2px solid #0d6efd;
    color: #0d6efd;
}

.badge {
    font-size: 0.7em;
}

.request-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.2s;
}

.request-item:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.status-badge {
    font-size: 0.75em;
    padding: 0.25em 0.5em;
}

.status-pending { background-color: #ffc107; color: #000; }
.status-processing { background-color: #17a2b8; color: #fff; }
.status-completed { background-color: #28a745; color: #fff; }
.status-rejected { background-color: #dc3545; color: #fff; }
</style>
{% endblock %}

{% block scripts %}
<script>
// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    loadIncomingRequests();
    
    // 标签页切换事件
    document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(e) {
            const target = e.target.getAttribute('data-bs-target');
            switch(target) {
                case '#incoming':
                    loadIncomingRequests();
                    break;
                case '#outgoing':
                    loadOutgoingRequests();
                    break;
                case '#history':
                    loadHistoryRequests();
                    break;
            }
        });
    });
    
    // 筛选器事件
    setupFilters();
});

// 加载收到的请求
function loadIncomingRequests() {
    const content = document.getElementById('incomingRequestsContent');
    content.innerHTML = '<div class="text-center py-4"><div class="spinner-border" role="status"></div></div>';
    
    const statusFilter = document.getElementById('incomingStatusFilter')?.value || '';
    let url = '/api/import-requests/incoming?';
    if (statusFilter) url += `status=${statusFilter}&`;
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                content.innerHTML = renderRequestsList(data.requests, 'incoming');
            } else {
                content.innerHTML = '<div class="alert alert-danger">加载失败，请重试</div>';
            }
        })
        .catch(error => {
            content.innerHTML = '<div class="alert alert-danger">加载失败，请重试</div>';
        });
}

// 加载发出的请求
function loadOutgoingRequests() {
    const content = document.getElementById('outgoingRequestsContent');
    content.innerHTML = '<div class="text-center py-4"><div class="spinner-border" role="status"></div></div>';
    
    const statusFilter = document.getElementById('outgoingStatusFilter')?.value || '';
    let url = '/api/import-requests/outgoing?';
    if (statusFilter) url += `status=${statusFilter}&`;
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                content.innerHTML = renderRequestsList(data.requests, 'outgoing');
            } else {
                content.innerHTML = '<div class="alert alert-danger">加载失败，请重试</div>';
            }
        })
        .catch(error => {
            content.innerHTML = '<div class="alert alert-danger">加载失败，请重试</div>';
        });
}

// 渲染请求列表
function renderRequestsList(requests, type) {
    if (!requests || requests.length === 0) {
        return '<div class="alert alert-info">暂无数据</div>';
    }
    
    return requests.map(request => `
        <div class="request-item">
            <div class="row">
                <div class="col-md-8">
                    <h6 class="mb-2">
                        ${type === 'incoming' ? '来自' : '发送到'}: 
                        <strong>${type === 'incoming' ? request.requester_company : request.target_company}</strong>
                    </h6>
                    <p class="mb-1">
                        <i class="bi bi-file-earmark"></i> 文件: ${request.original_filename}
                        <span class="ms-3"><i class="bi bi-calendar"></i> ${request.requested_at}</span>
                    </p>
                    ${request.notes ? `<p class="mb-1 text-muted"><i class="bi bi-chat-left-text"></i> ${request.notes}</p>` : ''}
                    ${request.approver_notes ? `<p class="mb-1 text-danger"><i class="bi bi-exclamation-triangle"></i> ${request.approver_notes}</p>` : ''}
                </div>
                <div class="col-md-4 text-end">
                    <span class="badge status-${request.status} status-badge">${getStatusText(request.status)}</span>
                    <div class="mt-2">
                        ${getActionButtons(request, type)}
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'pending': '待审批',
        'processing': '处理中',
        'completed': '已完成',
        'rejected': '已拒绝'
    };
    return statusMap[status] || status;
}

// 获取操作按钮
function getActionButtons(request, type) {
    let buttons = '';
    
    if (type === 'incoming' && request.status === 'pending') {
        buttons += `
            <button class="btn btn-sm btn-success me-1" onclick="approveRequest(${request.id})">
                <i class="bi bi-check-circle"></i> 批准
            </button>
            <button class="btn btn-sm btn-danger" onclick="rejectRequest(${request.id})">
                <i class="bi bi-x-circle"></i> 拒绝
            </button>
        `;
    }
    
    buttons += `
        <button class="btn btn-sm btn-outline-info" onclick="viewRequestDetails(${request.id})">
            <i class="bi bi-eye"></i> 详情
        </button>
    `;
    
    return buttons;
}

// 批准请求
function approveRequest(requestId) {
    // 显示批准模态框
    showApproveModal(requestId);
}

// 拒绝请求
function rejectRequest(requestId) {
    // 显示拒绝模态框
    showRejectModal(requestId);
}

// 设置筛选器
function setupFilters() {
    const filters = ['incomingStatusFilter', 'outgoingStatusFilter', 'historyStatusFilter'];
    filters.forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) {
            filter.addEventListener('change', function() {
                const activeTab = document.querySelector('.nav-link.active').getAttribute('data-bs-target');
                if (activeTab === '#incoming') loadIncomingRequests();
                else if (activeTab === '#outgoing') loadOutgoingRequests();
                else if (activeTab === '#history') loadHistoryRequests();
            });
        }
    });
}

// 刷新请求
function refreshRequests() {
    const activeTab = document.querySelector('.nav-link.active').getAttribute('data-bs-target');
    if (activeTab === '#incoming') loadIncomingRequests();
    else if (activeTab === '#outgoing') loadOutgoingRequests();
    else if (activeTab === '#history') loadHistoryRequests();
}
</script>
{% endblock %}
