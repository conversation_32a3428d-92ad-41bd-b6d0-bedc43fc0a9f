#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试线索数据，用于测试销售漏斗转化率切换功能
"""

import os
import sys
import random
from datetime import datetime, timed<PERSON><PERSON>

def create_test_leads():
    """创建测试线索数据"""
    try:
        # 确保在正确的目录
        if not os.path.isfile('app.py'):
            print("❌ 错误: 请在项目根目录下运行此脚本！")
            return False

        # 导入应用和模型
        from app import create_app
        from config import Config
        from models import db, User, Company, Lead, LeadPool
        
        app = create_app(Config)
        
        with app.app_context():
            print("🧪 创建测试线索数据")
            print("="*60)
            
            # 1. 获取管理员用户和公司
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                print("❌ 未找到admin用户")
                return False
            
            # 获取第一个公司
            company = Company.query.first()
            if not company:
                print("❌ 未找到公司")
                return False
            
            # 获取或创建线索池
            lead_pool = LeadPool.query.filter_by(company_id=company.id).first()
            if not lead_pool:
                print("🔧 创建线索池...")
                lead_pool = LeadPool(
                    name=f'{company.name}线索池',
                    company_id=company.id,
                    description='自动创建的测试线索池'
                )
                db.session.add(lead_pool)
                db.session.commit()
                print(f"✅ 创建线索池: {lead_pool.name}")

            print(f"✅ 使用公司: {company.name}")
            print(f"✅ 使用用户: {admin_user.name}")
            print(f"✅ 使用线索池: {lead_pool.name}")
            
            # 2. 清理现有测试数据
            existing_leads = Lead.query.filter(Lead.name.like('测试客户%')).all()
            if existing_leads:
                print(f"🗑️  删除现有测试数据: {len(existing_leads)} 条")
                for lead in existing_leads:
                    db.session.delete(lead)
                db.session.commit()
            
            # 3. 创建测试线索数据
            print("\n📊 创建测试线索数据...")
            
            # 定义转化率（模拟真实业务场景）
            total_leads = 100  # 总线索数
            conversion_rates = {
                'called': 0.85,      # 85% 已拨电话
                'connected': 0.70,   # 70% 接通电话
                'valid_call': 0.60,  # 60% 有效通话
                'wechat_added': 0.45, # 45% 添加微信
                'intentional': 0.30,  # 30% 意向客户
                'visited': 0.20,     # 20% 确认到面
                'compliant': 0.15,   # 15% 信息合规
                'deal_done': 0.10,   # 10% 完成签约
                'car_selected': 0.08  # 8% 完成提车
            }
            
            # 计算各阶段数量
            stage_counts = {}
            for stage, rate in conversion_rates.items():
                stage_counts[stage] = int(total_leads * rate)
            
            print(f"📈 预期转化数据:")
            print(f"   总线索: {total_leads}")
            for stage, count in stage_counts.items():
                rate = count / total_leads * 100
                print(f"   {stage}: {count} ({rate:.1f}%)")
            
            # 4. 创建线索
            leads_created = []
            
            for i in range(total_leads):
                # 基础信息
                lead = Lead(
                    name=f'测试客户{i+1:03d}',
                    company_name=f'测试公司{random.randint(1, 20)}',
                    email=f'test{i+1}@example.com',
                    phone=f'138{random.randint(10000000, 99999999)}',
                    notes=f'这是第{i+1}个测试线索',
                    status='new',
                    owner_id=admin_user.id,
                    company_id=company.id,
                    original_company_id=company.id,
                    pool_id=lead_pool.id,
                    is_self_created=True,
                    created_at=datetime.now() - timedelta(days=random.randint(1, 30))
                )
                
                # 根据转化率设置各阶段状态
                # 使用递减的概率确保漏斗形状
                if i < stage_counts['called']:
                    lead.is_called = True
                    
                    if i < stage_counts['connected']:
                        lead.is_connected = True
                        
                        if i < stage_counts['valid_call']:
                            lead.is_valid_call = True
                            
                            if i < stage_counts['wechat_added']:
                                lead.is_wechat_added = True
                                
                                if i < stage_counts['intentional']:
                                    lead.is_intentional = True
                                    
                                    if i < stage_counts['visited']:
                                        lead.is_visited = True
                                        
                                        if i < stage_counts['compliant']:
                                            lead.is_compliant = True
                                            
                                            if i < stage_counts['deal_done']:
                                                lead.is_deal_done = True
                                                lead.deal_status = 'done'
                                                
                                                if i < stage_counts['car_selected']:
                                                    lead.is_car_selected = True
                
                db.session.add(lead)
                leads_created.append(lead)
            
            # 5. 提交数据
            db.session.commit()
            
            print(f"\n✅ 成功创建 {len(leads_created)} 条测试线索")
            
            # 6. 验证数据
            print("\n🔍 验证创建的数据:")
            total_created = Lead.query.filter(Lead.name.like('测试客户%')).count()
            print(f"   总线索: {total_created}")
            
            # 验证各阶段数量
            verification_data = {
                'called': Lead.query.filter(Lead.name.like('测试客户%'), Lead.is_called == True).count(),
                'connected': Lead.query.filter(Lead.name.like('测试客户%'), Lead.is_connected == True).count(),
                'valid_call': Lead.query.filter(Lead.name.like('测试客户%'), Lead.is_valid_call == True).count(),
                'wechat_added': Lead.query.filter(Lead.name.like('测试客户%'), Lead.is_wechat_added == True).count(),
                'intentional': Lead.query.filter(Lead.name.like('测试客户%'), Lead.is_intentional == True).count(),
                'visited': Lead.query.filter(Lead.name.like('测试客户%'), Lead.is_visited == True).count(),
                'compliant': Lead.query.filter(Lead.name.like('测试客户%'), Lead.is_compliant == True).count(),
                'deal_done': Lead.query.filter(Lead.name.like('测试客户%'), Lead.is_deal_done == True).count(),
                'car_selected': Lead.query.filter(Lead.name.like('测试客户%'), Lead.is_car_selected == True).count()
            }
            
            print("\n📊 实际创建的数据:")
            for stage, count in verification_data.items():
                rate_total = count / total_created * 100 if total_created > 0 else 0
                print(f"   {stage}: {count} ({rate_total:.1f}%)")
            
            print("\n🎯 测试说明:")
            print("   1. 访问首页查看销售漏斗统计")
            print("   2. 点击右上角的开关切换百分比模式")
            print("   3. 观察各阶段百分比的变化:")
            print("      - 关闭状态: 显示阶段转化率")
            print("      - 开启状态: 显示总体占比")
            
            return True
            
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def clean_test_data():
    """清理测试数据"""
    try:
        from app import create_app
        from config import Config
        from models import db, Lead
        
        app = create_app(Config)
        
        with app.app_context():
            print("🗑️  清理测试数据")
            print("="*40)
            
            # 删除测试线索
            test_leads = Lead.query.filter(Lead.name.like('测试客户%')).all()
            count = len(test_leads)
            
            for lead in test_leads:
                db.session.delete(lead)
            
            db.session.commit()
            print(f"✅ 已删除 {count} 条测试线索")
            
            return True
            
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return False

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='测试线索数据管理')
    parser.add_argument('--clean', action='store_true', help='清理测试数据')
    parser.add_argument('--create', action='store_true', help='创建测试数据')
    
    args = parser.parse_args()
    
    if args.clean:
        clean_test_data()
    elif args.create:
        create_test_leads()
    else:
        # 默认创建测试数据
        print("🚀 开始创建测试数据")
        print("   使用 --clean 参数可以清理测试数据")
        print("   使用 --create 参数可以创建测试数据")
        print()
        
        choice = input("是否创建测试数据？(y/N): ").lower().strip()
        if choice in ['y', 'yes']:
            create_test_leads()
        else:
            print("操作已取消")
