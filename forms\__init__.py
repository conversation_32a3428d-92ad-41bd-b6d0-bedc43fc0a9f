from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, BooleanField, SubmitField, SelectField, TextAreaField
from wtforms.validators import DataRequired, Email, Length, EqualTo, ValidationError, Regexp, Optional
from models import User, Company, Department, Role

class BaseForm(FlaskForm):
    """一个基础表单，只包含CSRF令牌，用于不需要其他字段的POST请求"""
    pass

class LoginForm(FlaskForm):
    account = StringField('邮箱/手机号', validators=[
        DataRequired(message='请输入邮箱或手机号'),
        Length(max=120, message='邮箱或手机号长度不正确')
    ])
    password = PasswordField('密码', validators=[
        DataRequired(message='请输入密码'),
        Length(min=6, message='密码长度不能小于6个字符')
    ])
    captcha = StringField('验证码', validators=[
        DataRequired(message='请输入验证码'),
        Length(min=4, max=4, message='验证码长度必须为4位')
    ])
    submit = SubmitField('登录')

class CompanyForm(FlaskForm):
    name = StringField('公司名称', validators=[DataRequired(), Length(min=2, max=100)])
    code = StringField('公司编码', validators=[DataRequired(), Length(min=2, max=50)])
    type = SelectField('公司类型', choices=[('headquarters', '总部'), ('subsidiary', '子公司')])
    parent_id = SelectField('上级公司', coerce=int)
    manager_id = SelectField('公司管理员', coerce=int)
    description = TextAreaField('公司描述')
    is_default = BooleanField('是否默认')
    submit = SubmitField('提交')

    def __init__(self, *args, **kwargs):
        super(CompanyForm, self).__init__(*args, **kwargs)
        # 初始化选项，由路由处理具体数据
        self.parent_id.choices = [(0, '无')]
        self.manager_id.choices = [(0, '请选择管理员')]

class UserForm(FlaskForm):
    username = StringField('用户名', validators=[DataRequired(), Length(min=2, max=50)])
    name = StringField('姓名', validators=[DataRequired(), Length(min=2, max=50)])
    email = StringField('邮箱', validators=[DataRequired(), Email()])
    phone = StringField('手机号码', validators=[
        Optional(),
        Regexp(r'^1[3-9]\d{9}$', message='请输入有效的手机号码')
    ])
    password = PasswordField('密码', validators=[
        Optional(),  # 使密码字段可选
        Length(min=6, max=50, message='密码长度为6-50个字符')
    ])
    company_id = SelectField('所属公司', coerce=int, validators=[DataRequired()])
    department_id = SelectField('所属部门', coerce=int, validators=[Optional()])
    role_id = SelectField('角色', coerce=int, validators=[DataRequired()])
    submit = SubmitField('保存')

    def __init__(self, *args, **kwargs):
        super(UserForm, self).__init__(*args, **kwargs)
        self.obj = kwargs.get('obj')  # 添加obj属性支持

    def validate_email(self, field):
        user = User.query.filter_by(email=field.data).first()
        if user and (not self.obj or user.id != self.obj.id):
            raise ValidationError('邮箱已被使用')
            
    def validate_phone(self, field):
        if field.data:
            user = User.query.filter_by(phone=field.data).first()
            if user and (not self.obj or user.id != self.obj.id):
                raise ValidationError('手机号已被使用')

class DepartmentForm(FlaskForm):
    name = StringField('部门名称', validators=[DataRequired(), Length(min=2, max=100)])
    code = StringField('部门编码', validators=[DataRequired(), Length(min=2, max=50)])
    company_id = SelectField('所属公司', coerce=int, validators=[DataRequired()])
    parent_id = SelectField('上级部门', coerce=int)
    manager_id = SelectField('部门管理员', coerce=int)
    type = SelectField('类型', choices=[('department', '部门'), ('group', '小组')], default='department')
    description = TextAreaField('部门描述')
    submit = SubmitField('提交')

    def __init__(self, *args, **kwargs):
        super(DepartmentForm, self).__init__(*args, **kwargs)
        # 初始化选项，由路由处理具体数据
        self.parent_id.choices = [(0, '无')]
        self.manager_id.choices = [(0, '请选择管理员')]