#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试首页数据显示问题
"""

import os
import sys

def debug_homepage_data():
    """调试首页数据"""
    try:
        # 确保在正确的目录
        if not os.path.isfile('app.py'):
            print("❌ 错误: 请在项目根目录下运行此脚本！")
            return False

        # 导入应用和模型
        from app import create_app
        from config import Config
        from models import db, User, Lead
        from flask_login import login_user
        
        app = create_app(Config)
        
        with app.app_context():
            print("🔍 调试首页数据显示问题")
            print("="*60)
            
            # 1. 检查数据库中的线索
            total_leads = Lead.query.count()
            test_leads = Lead.query.filter(Lead.name.like('测试客户%')).count()
            
            print(f"📊 数据库中的线索:")
            print(f"   总线索数: {total_leads}")
            print(f"   测试线索数: {test_leads}")
            
            if test_leads == 0:
                print("❌ 没有测试线索，需要先创建测试数据")
                return False
            
            # 2. 获取admin用户
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                print("❌ 未找到admin用户")
                return False
            
            print(f"\n👤 Admin用户信息:")
            print(f"   ID: {admin_user.id}")
            print(f"   用户名: {admin_user.username}")
            print(f"   角色: {admin_user.role.name if admin_user.role else '无角色'}")
            print(f"   角色代码: {admin_user.role.code if admin_user.role else '无'}")
            
            # 3. 模拟登录并测试首页数据获取
            with app.test_client() as client:
                # 模拟登录
                with client.session_transaction() as sess:
                    sess['_user_id'] = str(admin_user.id)
                    sess['_fresh'] = True
                
                print(f"\n🏠 模拟首页请求:")
                
                # 发送首页请求
                response = client.get('/')
                print(f"   响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    # 检查响应内容中是否包含数据
                    content = response.data.decode('utf-8')
                    
                    # 查找销售漏斗统计区域
                    if '销售漏斗统计' in content:
                        print("   ✅ 找到销售漏斗统计区域")
                        
                        # 查找具体的数字
                        import re
                        
                        # 查找已拨电话的数字
                        called_match = re.search(r'已拨电话.*?<h4[^>]*>(\d+)</h4>', content, re.DOTALL)
                        if called_match:
                            called_count = called_match.group(1)
                            print(f"   ✅ 已拨电话数量: {called_count}")
                        else:
                            print("   ❌ 未找到已拨电话数量")
                        
                        # 查找其他阶段
                        stages = ['接通电话', '有效通话', '添加微信', '意向客户', '确认到面', '信息合规', '完成签约', '完成提车']
                        for stage in stages:
                            stage_match = re.search(f'{stage}.*?<h4[^>]*>(\\d+)</h4>', content, re.DOTALL)
                            if stage_match:
                                count = stage_match.group(1)
                                print(f"   ✅ {stage}数量: {count}")
                            else:
                                print(f"   ❌ 未找到{stage}数量")
                    else:
                        print("   ❌ 未找到销售漏斗统计区域")
                        
                        # 检查是否有错误信息
                        if '获取统计数据失败' in content:
                            print("   ❌ 页面显示获取统计数据失败")
                        
                        # 保存响应内容到文件以便检查
                        with open('debug_homepage_response.html', 'w', encoding='utf-8') as f:
                            f.write(content)
                        print("   📄 响应内容已保存到 debug_homepage_response.html")
                
                else:
                    print(f"   ❌ 首页请求失败，状态码: {response.status_code}")
            
            # 4. 直接测试数据获取函数
            print(f"\n🔧 直接测试数据获取函数:")
            
            try:
                # 模拟Flask-Login的current_user
                from flask_login import current_user
                from flask import g
                
                # 手动设置current_user
                with app.test_request_context():
                    login_user(admin_user)
                    
                    # 测试get_funnel_stats函数
                    from utils.homepage_utils import get_funnel_stats
                    
                    funnel_stats = get_funnel_stats()
                    print(f"   漏斗统计结果:")
                    for key, value in funnel_stats.items():
                        print(f"     {key}: {value}")
                        
            except Exception as e:
                print(f"   ❌ 直接测试失败: {e}")
                import traceback
                traceback.print_exc()
            
            return True
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_simple_test_data():
    """创建简单的测试数据"""
    try:
        from app import create_app
        from config import Config
        from models import db, User, Lead, Company, LeadPool
        from datetime import datetime
        
        app = create_app(Config)
        
        with app.app_context():
            print("\n🔧 创建简单测试数据")
            print("="*50)
            
            # 获取admin用户和默认公司
            admin_user = User.query.filter_by(username='admin').first()
            company = Company.query.first()
            
            if not admin_user or not company:
                print("❌ 缺少基础数据")
                return False
            
            # 获取或创建线索池
            lead_pool = LeadPool.query.filter_by(company_id=company.id).first()
            if not lead_pool:
                lead_pool = LeadPool(
                    name=f'{company.name}线索池',
                    company_id=company.id,
                    description='测试线索池'
                )
                db.session.add(lead_pool)
                db.session.commit()
            
            # 删除现有测试数据
            Lead.query.filter(Lead.name.like('简单测试%')).delete()
            db.session.commit()
            
            # 创建10条简单测试线索
            for i in range(10):
                lead = Lead(
                    name=f'简单测试{i+1:02d}',
                    company_name=f'测试公司{i+1}',
                    email=f'test{i+1}@test.com',
                    phone=f'1380000{i+1:04d}',
                    notes=f'简单测试线索{i+1}',
                    status='new',
                    owner_id=admin_user.id,
                    company_id=company.id,
                    original_company_id=company.id,
                    pool_id=lead_pool.id,
                    is_self_created=True,
                    created_at=datetime.now()
                )
                
                # 设置一些阶段状态
                if i >= 0:  # 所有线索都已拨电话
                    lead.is_called = True
                if i >= 2:  # 8条线索接通电话
                    lead.is_connected = True
                if i >= 4:  # 6条线索有效通话
                    lead.is_valid_call = True
                if i >= 6:  # 4条线索添加微信
                    lead.is_wechat_added = True
                if i >= 8:  # 2条线索意向客户
                    lead.is_intentional = True
                if i >= 9:  # 1条线索确认到面
                    lead.is_visited = True
                    lead.is_compliant = True
                    lead.is_deal_done = True
                    lead.is_car_selected = True
                
                db.session.add(lead)
            
            db.session.commit()
            
            # 验证创建结果
            created_count = Lead.query.filter(Lead.name.like('简单测试%')).count()
            print(f"✅ 创建了 {created_count} 条简单测试线索")
            
            # 显示各阶段统计
            base_query = Lead.query.filter(Lead.name.like('简单测试%'))
            print(f"   已拨电话: {base_query.filter(Lead.is_called == True).count()}")
            print(f"   接通电话: {base_query.filter(Lead.is_connected == True).count()}")
            print(f"   有效通话: {base_query.filter(Lead.is_valid_call == True).count()}")
            print(f"   添加微信: {base_query.filter(Lead.is_wechat_added == True).count()}")
            print(f"   意向客户: {base_query.filter(Lead.is_intentional == True).count()}")
            print(f"   确认到面: {base_query.filter(Lead.is_visited == True).count()}")
            print(f"   信息合规: {base_query.filter(Lead.is_compliant == True).count()}")
            print(f"   完成签约: {base_query.filter(Lead.is_deal_done == True).count()}")
            print(f"   完成提车: {base_query.filter(Lead.is_car_selected == True).count()}")
            
            return True
            
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    # 调试首页数据
    debug_homepage_data()
    
    # 询问是否创建简单测试数据
    print("\n" + "="*60)
    choice = input("是否创建简单测试数据？(y/N): ").lower().strip()
    if choice in ['y', 'yes']:
        create_simple_test_data()
        print("\n🎯 请刷新首页查看数据")
