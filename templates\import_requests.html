{% extends 'base.html' %}

{% block title %}线索导入请求 - CRM系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-4">线索导入请求管理</h2>

    {% if requests_list %}
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>发起人</th>
                            <th>发起公司</th>
                            <th>文件名</th>
                            <th>请求时间</th>
                            <th>状态</th>
                            <th>处理人</th>
                            <th>处理时间</th>
                            <th>结果</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for req in requests_list %}
                        <tr>
                            <td>{{ req.requester_user.username if req.requester_user else '未知' }}</td>
                            <td>{{ req.requester_company.name if req.requester_company else '未知' }}</td>
                            <td>{{ req.original_filename }}</td>
                            <td>{{ req.requested_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                <span class="badge bg-{{ 'secondary' if req.status == 'pending' else ('success' if req.status == 'completed' else ('warning' if req.status in ['processing', 'approved'] else 'danger')) }}">
                                    {{ req.status | title }}
                                </span>
                            </td>
                            <td>{{ req.approver_user.username if req.approver_user else '-' }}</td>
                            <td>{{ req.processed_at.strftime('%Y-%m-%d %H:%M') if req.processed_at else '-' }}</td>
                            <td>
                                {% if req.status == 'completed' %}
                                成功: {{ req.imported_count or 0 }}, 失败: {{ req.failed_count or 0 }}
                                {% elif req.status == 'failed' %}
                                <span class="text-danger">失败: {{ req.failure_reason or '未知错误' }}</span>
                                {% elif req.status == 'rejected' %}
                                已拒绝 {% if req.approver_notes %}({{ req.approver_notes }}){% endif %}
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                {% if req.status == 'pending' %}
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#approveModal{{ req.id }}">批准</button>
                                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#rejectModal{{ req.id }}">拒绝</button>
                                </div>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 分页 -->
    {% include '_pagination.html' %}

    <!-- 模态框 -->
    {% for req in requests_list %}
        {% if req.status == 'pending' %}
        <!-- Approve Modal -->
        <div class="modal fade" id="approveModal{{ req.id }}" tabindex="-1" aria-labelledby="approveModalLabel{{ req.id }}" aria-hidden="true">
            <div class="modal-dialog">
                <form method="post" action="{{ url_for('leads.approve_import_request', request_id=req.id) }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="approveModalLabel{{ req.id }}">确认批准导入请求？</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>您确定要批准来自 <strong>{{ req.requester_user.username if req.requester_user else '未知' }} ({{ req.requester_company.name if req.requester_company else '未知' }})</strong> 的线索导入请求吗？</p>
                            <p>文件名: {{ req.original_filename }}</p>
                            {% if req.notes %}
                            <p>发起人备注: {{ req.notes }}</p>
                            {% endif %}
                            <p class="text-muted">批准后，系统将尝试导入线索并将其分配给您。</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="submit" class="btn btn-success">确认批准</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Reject Modal -->
        <div class="modal fade" id="rejectModal{{ req.id }}" tabindex="-1" aria-labelledby="rejectModalLabel{{ req.id }}" aria-hidden="true">
            <div class="modal-dialog">
                <form method="post" action="{{ url_for('leads.reject_import_request', request_id=req.id) }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="rejectModalLabel{{ req.id }}">确认拒绝导入请求？</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>您确定要拒绝来自 <strong>{{ req.requester_user.username if req.requester_user else '未知' }} ({{ req.requester_company.name if req.requester_company else '未知' }})</strong> 的线索导入请求吗？</p>
                            <p>文件名: {{ req.original_filename }}</p>
                            <div class="mb-3">
                                <label for="reject_notes_{{ req.id }}" class="form-label">拒绝原因 (可选)</label>
                                <textarea class="form-control" id="reject_notes_{{ req.id }}" name="approver_notes" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="submit" class="btn btn-danger">确认拒绝</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        {% endif %}
    {% endfor %}

    {% else %}
    <div class="alert alert-info">暂无待处理或已处理的线索导入请求。</div>
    {% endif %}
</div>
{% endblock %} 