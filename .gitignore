# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
.env

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
*.log
logs/

# Local development
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
*.db
*.sqlite3

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
coverage.xml
*.cover

# Databases
*.sqlite

# Data directories
data/
uploads/
limiter_storage/
instance/

# Docker
.dockerignore
Dockerfile*
docker-compose*.yml
!docker-compose.portainer.yml

# Environment files
.env.development
.env.production

# Keep .env.prod for easy deployment
!.env.prod

# OS files
Thumbs.db 