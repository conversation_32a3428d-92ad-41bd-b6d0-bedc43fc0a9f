#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
组织管理服务层
统一处理公司、部门、员工的管理逻辑
"""

from typing import List, Dict, Optional, Tuple
from sqlalchemy import and_, or_
from models import db, Company, Department, User, Role
from flask_login import current_user
from utils.department_utils import get_child_departments

class OrganizationService:
    """组织管理服务类"""
    
    @staticmethod
    def get_user_scope() -> Dict[str, any]:
        """获取当前用户的数据访问范围 - 支持平台架构"""
        if not current_user.is_authenticated:
            return {'companies': [], 'departments': [], 'users': []}

        scope = {
            'role': current_user.role.code,
            'company_id': current_user.company_id,
            'department_id': current_user.department_id,
            'can_manage_companies': False,
            'can_manage_departments': False,
            'can_manage_users': False
        }

        if current_user.role.code == 'super_admin':
            # 超级管理员可以看到所有数据，包括平台级用户
            scope.update({
                'companies': Company.query.all(),
                'departments': Department.query.all(),
                'users': User.query.all(),  # 包括平台级用户（company_id为None）
                'can_manage_companies': True,
                'can_manage_departments': True,
                'can_manage_users': True
            })
        elif current_user.role.code == 'company_admin':
            # 公司管理员只能看到自己公司的数据
            if current_user.company_id:
                scope.update({
                    'companies': [current_user.company],
                    'departments': Department.query.filter_by(company_id=current_user.company_id).all(),
                    'users': User.query.filter_by(company_id=current_user.company_id).all(),
                    'can_manage_departments': True,
                    'can_manage_users': True
                })
            else:
                # 如果公司管理员没有company_id，返回空数据
                scope.update({
                    'companies': [],
                    'departments': [],
                    'users': []
                })
        elif current_user.role.code == 'department_admin':
            # 部门管理员只能看到自己部门及子部门的数据
            if current_user.department_id and current_user.company_id:
                dept_ids = get_child_departments(current_user.department_id) + [current_user.department_id]
                scope.update({
                    'companies': [current_user.company],
                    'departments': Department.query.filter(Department.id.in_(dept_ids)).all(),
                    'users': User.query.filter(User.department_id.in_(dept_ids)).all(),
                    'can_manage_users': True
                })
            else:
                scope.update({
                    'companies': [],
                    'departments': [],
                    'users': []
                })
        else:
            # 普通员工只能看到自己
            if current_user.company_id:
                scope.update({
                    'companies': [current_user.company] if current_user.company else [],
                    'departments': [current_user.department] if current_user.department else [],
                    'users': [current_user]
                })
            else:
                scope.update({
                    'companies': [],
                    'departments': [],
                    'users': [current_user]
                })

        return scope
    
    @staticmethod
    def get_organization_tree() -> Dict[str, any]:
        """获取组织架构树"""
        scope = OrganizationService.get_user_scope()

        tree = {
            'companies': [],
            'total_companies': 0,
            'total_departments': 0,
            'total_users': 0,
            'permissions': OrganizationService.get_user_tree_permissions()
        }

        for company in scope['companies']:
            company_node = {
                'id': company.id,
                'name': company.name,
                'code': company.code,
                'type': 'company',
                'node_type': 'company',
                'status': 'active',
                'icon': 'fas fa-building',
                'children': [],
                'stats': {
                    'departments': 0,
                    'employees': 0
                },
                'permissions': OrganizationService._get_node_permissions('company', company.id)
            }

            # 获取公司的顶级部门
            top_departments = [d for d in scope['departments']
                             if d.company_id == company.id and d.parent_id is None]

            for dept in top_departments:
                dept_node = OrganizationService._build_unified_tree_node(dept, scope['departments'], scope['users'])
                company_node['children'].append(dept_node)
                company_node['stats']['departments'] += dept_node['stats']['total_departments']
                company_node['stats']['employees'] += dept_node['stats']['total_employees']

            tree['companies'].append(company_node)
            tree['total_departments'] += company_node['stats']['departments']
            tree['total_users'] += company_node['stats']['employees']

        tree['total_companies'] = len(tree['companies'])
        return tree
    
    @staticmethod
    def _build_department_tree(department: Department, all_departments: List[Department], 
                              all_users: List[User]) -> Dict[str, any]:
        """递归构建部门树"""
        dept_node = {
            'id': department.id,
            'name': department.name,
            'code': department.code,
            'type': department.type,
            'subdepartments': [],
            'users': [],
            'total_departments': 1,  # 包含自己
            'total_users': 0
        }
        
        # 添加部门直属用户
        dept_users = [u for u in all_users if u.department_id == department.id]
        for user in dept_users:
            user_node = {
                'id': user.id,
                'name': user.name or user.username,
                'username': user.username,
                'email': user.email,
                'role': user.role.name if user.role else '',
                'status': user.status
            }
            dept_node['users'].append(user_node)
        
        dept_node['total_users'] = len(dept_users)
        
        # 递归添加子部门
        subdepartments = [d for d in all_departments if d.parent_id == department.id]
        for subdept in subdepartments:
            subdept_node = OrganizationService._build_department_tree(subdept, all_departments, all_users)
            dept_node['subdepartments'].append(subdept_node)
            dept_node['total_departments'] += subdept_node['total_departments']
            dept_node['total_users'] += subdept_node['total_users']
        
        return dept_node
    
    @staticmethod
    def get_company_statistics() -> List[Dict[str, any]]:
        """获取公司统计信息 - 优化版本"""
        scope = OrganizationService.get_user_scope()

        if not scope['companies']:
            return []

        # 使用字典预计算统计数据，避免重复循环
        company_ids = [c.id for c in scope['companies']]

        # 预计算部门统计
        dept_stats = {}
        for dept in scope['departments']:
            if dept.company_id in company_ids:
                dept_stats[dept.company_id] = dept_stats.get(dept.company_id, 0) + 1

        # 预计算用户统计
        user_stats = {}
        active_user_stats = {}
        for user in scope['users']:
            if user.company_id in company_ids:
                user_stats[user.company_id] = user_stats.get(user.company_id, 0) + 1
                if user.status == 'active':
                    active_user_stats[user.company_id] = active_user_stats.get(user.company_id, 0) + 1

        # 构建统计结果
        statistics = []
        for company in scope['companies']:
            dept_count = dept_stats.get(company.id, 0)
            user_count = user_stats.get(company.id, 0)
            active_user_count = active_user_stats.get(company.id, 0)

            statistics.append({
                'id': company.id,
                'name': company.name,
                'code': company.code,
                'type': company.type,
                'parent': company.parent,
                'is_default': company.is_default,
                'department_count': dept_count,
                'user_count': user_count,
                'active_user_count': active_user_count,
                'inactive_user_count': user_count - active_user_count,
                'created_at': company.created_at
            })

        return statistics
    
    @staticmethod
    def get_department_statistics(company_id: Optional[int] = None) -> List[Dict[str, any]]:
        """获取部门统计信息 - 优化版本"""
        scope = OrganizationService.get_user_scope()

        departments = scope['departments']
        if company_id:
            departments = [d for d in departments if d.company_id == company_id]

        if not departments:
            return []

        # 预计算用户统计
        user_by_dept = {}
        for user in scope['users']:
            if user.department_id:
                user_by_dept[user.department_id] = user_by_dept.get(user.department_id, 0) + 1

        # 预计算子部门统计
        subdept_stats = {}
        for dept in scope['departments']:
            if dept.parent_id:
                subdept_stats[dept.parent_id] = subdept_stats.get(dept.parent_id, 0) + 1

        statistics = []
        for department in departments:
            # 直属员工数量
            direct_user_count = user_by_dept.get(department.id, 0)

            # 子部门数量
            subdept_count = subdept_stats.get(department.id, 0)

            # 总员工数量（包含子部门）
            dept_ids = get_child_departments(department.id) + [department.id]
            total_user_count = sum(user_by_dept.get(dept_id, 0) for dept_id in dept_ids)

            statistics.append({
                'id': department.id,
                'name': department.name,
                'code': department.code,
                'type': department.type,
                'company_id': department.company_id,
                'company': department.company,
                'company_name': department.company.name if department.company else '',
                'parent': department.parent,
                'parent_name': department.parent.name if department.parent else '',
                'manager': department.manager if hasattr(department, 'manager') else None,
                'description': getattr(department, 'description', ''),
                'direct_user_count': direct_user_count,
                'total_user_count': total_user_count,
                'user_count': total_user_count,  # 为了兼容模板
                'subdepartment_count': subdept_count,
                'created_at': department.created_at
            })

        return statistics
    
    @staticmethod
    def get_user_statistics(company_id: Optional[int] = None,
                           department_id: Optional[int] = None) -> List[Dict[str, any]]:
        """获取员工统计信息 - 优化版本"""
        scope = OrganizationService.get_user_scope()

        users = scope['users']
        if company_id:
            users = [u for u in users if u.company_id == company_id]
        if department_id:
            users = [u for u in users if u.department_id == department_id]

        if not users:
            return []

        statistics = []
        for user in users:
            # 判断是否为管理员
            is_manager = user.role and user.role.code in ['super_admin', 'company_admin', 'department_admin']

            statistics.append({
                'id': user.id,
                'name': user.name or user.username,
                'username': user.username,
                'email': user.email,
                'phone': user.phone,
                'company_id': user.company_id,
                'department_id': user.department_id,
                'company': user.company,
                'department': user.department,
                'role': user.role,
                'company_name': user.company.name if user.company else '',
                'department_name': user.department.name if user.department else '',
                'role_name': user.role.name if user.role else '',
                'status': user.status,
                'is_manager': is_manager,
                'created_at': user.created_at,
                'last_login': getattr(user, 'last_login', None)
            })

        return statistics
    
    @staticmethod
    def validate_company_operation(operation: str, company_id: int, **kwargs) -> Tuple[bool, str]:
        """验证公司操作的合法性"""
        company = Company.query.get(company_id)
        if not company:
            return False, "公司不存在"
        
        if operation == 'delete':
            # 检查是否有子公司
            if len(company.subsidiaries) > 0:
                return False, "该公司有子公司，不能删除"
            
            # 检查是否有部门
            if Department.query.filter_by(company_id=company_id).count() > 0:
                return False, "该公司有部门，不能删除"
            
            # 检查是否有用户
            if User.query.filter_by(company_id=company_id).count() > 0:
                return False, "该公司有员工，不能删除"
        
        elif operation == 'edit':
            # 检查编码唯一性
            if 'code' in kwargs:
                existing = Company.query.filter(
                    Company.code == kwargs['code'],
                    Company.id != company_id
                ).first()
                if existing:
                    return False, "公司编码已存在"
        
        return True, ""
    
    @staticmethod
    def validate_department_operation(operation: str, department_id: int, **kwargs) -> Tuple[bool, str]:
        """验证部门操作的合法性"""
        department = Department.query.get(department_id)
        if not department:
            return False, "部门不存在"
        
        if operation == 'delete':
            # 检查是否有子部门
            if Department.query.filter_by(parent_id=department_id).count() > 0:
                return False, "该部门有子部门，不能删除"
            
            # 检查是否有员工
            if User.query.filter_by(department_id=department_id).count() > 0:
                return False, "该部门有员工，不能删除"
        
        elif operation == 'edit':
            # 检查编码唯一性（同公司内）
            if 'code' in kwargs:
                existing = Department.query.filter(
                    Department.code == kwargs['code'],
                    Department.company_id == department.company_id,
                    Department.id != department_id
                ).first()
                if existing:
                    return False, "部门编码在该公司内已存在"
            
            # 检查父部门循环引用
            if 'parent_id' in kwargs and kwargs['parent_id']:
                parent_id = kwargs['parent_id']
                if parent_id == department_id:
                    return False, "不能将部门设置为自己的上级部门"
                
                # 检查是否会形成循环
                current_parent = Department.query.get(parent_id)
                while current_parent:
                    if current_parent.id == department_id:
                        return False, "不能形成循环的部门层级关系"
                    current_parent = current_parent.parent
        
        return True, ""
    
    @staticmethod
    def validate_user_operation(operation: str, user_id: int, **kwargs) -> Tuple[bool, str]:
        """验证用户操作的合法性"""
        user = User.query.get(user_id)
        if not user:
            return False, "用户不存在"
        
        if operation == 'delete':
            # 检查是否为当前用户
            if user_id == current_user.id:
                return False, "不能删除自己的账号"
            
            # 检查是否为超级管理员（如果只有一个）
            if user.role and user.role.code == 'super_admin':
                admin_count = User.query.join(Role).filter(Role.code == 'super_admin').count()
                if admin_count <= 1:
                    return False, "不能删除最后一个超级管理员"
        
        elif operation == 'edit':
            # 检查用户名唯一性
            if 'username' in kwargs:
                existing = User.query.filter(
                    User.username == kwargs['username'],
                    User.id != user_id
                ).first()
                if existing:
                    return False, "用户名已存在"
            
            # 检查邮箱唯一性
            if 'email' in kwargs:
                existing = User.query.filter(
                    User.email == kwargs['email'],
                    User.id != user_id
                ).first()
                if existing:
                    return False, "邮箱已存在"
        
        return True, ""

    @staticmethod
    def _build_unified_tree_node(department: Department, all_departments: List[Department],
                                all_users: List[User]) -> Dict[str, any]:
        """构建统一的树形节点（用于新的统一界面）"""
        dept_node = {
            'id': department.id,
            'name': department.name,
            'code': department.code,
            'type': 'department',
            'node_type': 'department',
            'status': 'active',
            'icon': 'fas fa-users-cog',
            'children': [],
            'stats': {
                'total_departments': 1,  # 包含自己
                'total_employees': 0,
                'direct_employees': 0
            },
            'permissions': OrganizationService._get_node_permissions('department', department.id)
        }

        # 添加部门直属员工
        dept_users = [u for u in all_users if u.department_id == department.id]
        for user in dept_users:
            user_node = {
                'id': user.id,
                'name': user.name or user.username,
                'username': user.username,
                'type': 'user',
                'node_type': 'user',
                'status': user.status,
                'icon': 'fas fa-user',
                'children': [],
                'stats': {
                    'role': user.role.name if user.role else '',
                    'email': user.email,
                    'phone': user.phone
                },
                'permissions': OrganizationService._get_node_permissions('user', user.id)
            }
            dept_node['children'].append(user_node)

        dept_node['stats']['direct_employees'] = len(dept_users)
        dept_node['stats']['total_employees'] = len(dept_users)

        # 递归添加子部门
        subdepartments = [d for d in all_departments if d.parent_id == department.id]
        for subdept in subdepartments:
            subdept_node = OrganizationService._build_unified_tree_node(subdept, all_departments, all_users)
            dept_node['children'].append(subdept_node)
            dept_node['stats']['total_departments'] += subdept_node['stats']['total_departments']
            dept_node['stats']['total_employees'] += subdept_node['stats']['total_employees']

        return dept_node

    @staticmethod
    def _get_node_permissions(node_type: str, node_id: int) -> List[str]:
        """获取节点的操作权限（优化版本，避免递归调用）"""
        if not current_user.is_authenticated:
            return []

        permissions = []

        # 直接基于用户角色判断权限，避免递归调用get_user_scope
        if current_user.role.code == 'super_admin':
            if node_type == 'company':
                permissions.extend(['view', 'edit', 'delete', 'add_department'])
            elif node_type == 'department':
                permissions.extend(['view', 'edit', 'delete', 'add_subdepartment', 'add_employee'])
            elif node_type == 'user':
                permissions.extend(['view', 'edit', 'delete', 'transfer', 'reset_password'])

        elif current_user.role.code == 'company_admin':
            if node_type == 'company':
                # 只能查看本公司
                if node_id == current_user.company_id:
                    permissions.extend(['view', 'edit', 'add_department'])
                else:
                    permissions.append('view')
            elif node_type == 'department':
                # 检查是否为本公司部门
                dept = Department.query.get(node_id)
                if dept and dept.company_id == current_user.company_id:
                    permissions.extend(['view', 'edit', 'delete', 'add_subdepartment', 'add_employee'])
                else:
                    permissions.append('view')
            elif node_type == 'user':
                # 检查是否为本公司用户
                user = User.query.get(node_id)
                if user and user.company_id == current_user.company_id:
                    permissions.extend(['view', 'edit', 'delete', 'transfer', 'reset_password'])
                else:
                    permissions.append('view')

        elif current_user.role.code == 'department_admin':
            if node_type == 'company':
                permissions.append('view')
            elif node_type == 'department':
                # 检查是否为本部门或子部门
                from utils.department_utils import get_child_departments
                manageable_dept_ids = get_child_departments(current_user.department_id) + [current_user.department_id]
                if node_id in manageable_dept_ids:
                    permissions.extend(['view', 'edit', 'delete', 'add_subdepartment', 'add_employee'])
                else:
                    permissions.append('view')
            elif node_type == 'user':
                # 检查是否为本部门或子部门用户
                user = User.query.get(node_id)
                if user:
                    from utils.department_utils import get_child_departments
                    manageable_dept_ids = get_child_departments(current_user.department_id) + [current_user.department_id]
                    if user.department_id in manageable_dept_ids:
                        permissions.extend(['view', 'edit', 'delete', 'transfer', 'reset_password'])
                    else:
                        permissions.append('view')
        else:
            # 普通员工只有查看权限
            permissions.append('view')

        return permissions

    @staticmethod
    def get_user_tree_permissions() -> Dict[str, List[int]]:
        """获取用户在组织树中的权限范围"""
        scope = OrganizationService.get_user_scope()

        return {
            'viewable_companies': [c.id for c in scope['companies']],
            'manageable_companies': [c.id for c in scope['companies']] if scope['can_manage_companies'] else [],
            'viewable_departments': [d.id for d in scope['departments']],
            'manageable_departments': [d.id for d in scope['departments']] if scope['can_manage_departments'] else [],
            'viewable_users': [u.id for u in scope['users']],
            'manageable_users': [u.id for u in scope['users']] if scope['can_manage_users'] else [],
            'is_super_admin': current_user.role.code == 'super_admin' if current_user and current_user.role else False,
            'user_role': current_user.role.code if current_user and current_user.role else None
        }

    @staticmethod
    def get_node_details(node_type: str, node_id: int) -> Dict[str, any]:
        """获取节点详细信息"""
        try:
            if node_type == 'company':
                return OrganizationService.get_company_details(node_id)
            elif node_type == 'department':
                return OrganizationService.get_department_details(node_id)
            elif node_type == 'user':
                return OrganizationService.get_user_details(node_id)
            else:
                return {}
        except Exception as e:
            print(f"获取节点详情失败: {node_type}/{node_id}, 错误: {str(e)}")
            return {}

    @staticmethod
    def get_company_details(company_id: int) -> Dict[str, any]:
        """获取公司详细信息"""
        company = Company.query.get(company_id)
        if not company:
            return {}

        scope = OrganizationService.get_user_scope()
        if company not in scope['companies']:
            return {}

        # 统计信息
        departments = [d for d in scope['departments'] if d.company_id == company_id]
        users = [u for u in scope['users'] if u.company_id == company_id]
        active_users = [u for u in users if u.status == 'active']

        return {
            'type': 'company',
            'basic_info': {
                'id': company.id,
                'name': company.name,
                'code': company.code,
                'type': company.type,
                'description': getattr(company, 'description', ''),
                'created_at': company.created_at.strftime('%Y-%m-%d') if company.created_at else '',
                'parent_name': company.parent.name if company.parent else ''
            },
            'hierarchy': {
                'parent_id': company.parent_id,
                'parent_name': company.parent.name if company.parent else ''
            },
            'management': {
                'manager_id': company.manager_id,
                'manager_name': company.manager.name if company.manager else ''
            },
            'statistics': {
                'departments': len(departments),
                'total_employees': len(users),
                'active_employees': len(active_users),
                'inactive_employees': len(users) - len(active_users)
            },
            'permissions': OrganizationService._get_node_permissions('company', company_id),
            'recent_activities': []  # 可以后续添加活动记录
        }

    @staticmethod
    def get_department_details(department_id: int) -> Dict[str, any]:
        """获取部门详细信息"""
        department = Department.query.get(department_id)
        if not department:
            return {}

        scope = OrganizationService.get_user_scope()
        if department not in scope['departments']:
            return {}

        # 统计信息
        users = [u for u in scope['users'] if u.department_id == department_id]
        active_users = [u for u in users if u.status == 'active']

        # 子部门统计
        subdepartments = [d for d in scope['departments'] if d.parent_id == department_id]

        return {
            'type': 'department',
            'basic_info': {
                'id': department.id,
                'name': department.name,
                'code': department.code,
                'type': department.type,
                'description': getattr(department, 'description', ''),
                'company_name': department.company.name if department.company else '',
                'parent_name': department.parent.name if department.parent else '',
                'created_at': department.created_at.strftime('%Y-%m-%d') if department.created_at else ''
            },
            'hierarchy': {
                'company_id': department.company_id,
                'company_name': department.company.name if department.company else '',
                'parent_id': department.parent_id,
                'parent_name': department.parent.name if department.parent else ''
            },
            'management': {
                'manager_id': department.manager_id,
                'manager_name': department.manager.name if department.manager else ''
            },
            'statistics': {
                'direct_employees': len(users),
                'active_employees': len(active_users),
                'inactive_employees': len(users) - len(active_users),
                'subdepartments': len(subdepartments)
            },
            'employee_list': [
                {
                    'id': u.id,
                    'name': u.name or u.username,
                    'username': u.username,
                    'role': u.role.name if u.role else '',
                    'status': u.status
                } for u in users[:10]  # 显示前10个员工
            ],
            'permissions': OrganizationService._get_node_permissions('department', department_id),
            'recent_activities': []
        }

    @staticmethod
    def get_user_details(user_id: int) -> Dict[str, any]:
        """获取用户详细信息"""
        user = User.query.get(user_id)
        if not user:
            return {}

        # 对于编辑功能，我们需要检查权限但不完全依赖scope
        try:
            scope = OrganizationService.get_user_scope()
            if user not in scope['users']:
                # 如果用户不在权限范围内，仍然返回基本信息（用于编辑）
                # 但可能需要额外的权限检查
                pass
        except:
            # 如果权限检查失败，继续返回数据（可能是在测试环境）
            pass

        return {
            'type': 'user',
            'basic_info': {
                'id': user.id,
                'name': user.name or user.username,
                'username': user.username,
                'email': user.email,
                'phone': user.phone,
                'status': user.status,
                'created_at': user.created_at.strftime('%Y-%m-%d') if user.created_at else ''
            },
            'organization': {
                'company_id': user.company_id,
                'company_name': user.company.name if user.company else '',
                'department_id': user.department_id,
                'department_name': user.department.name if user.department else '',
                'role_id': user.role_id,
                'role_name': user.role.name if user.role else '',
                'role_code': user.role.code if user.role else ''
            },
            'work_stats': {
                'leads_count': 0,  # 可以后续添加线索统计
                'deals_count': 0,  # 可以后续添加成交统计
                'last_login': getattr(user, 'last_login', None)
            },
            'permissions': OrganizationService._get_node_permissions('user', user_id) if current_user and current_user.is_authenticated else {},
            'recent_activities': []
        }
