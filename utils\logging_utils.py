import logging
from logging.handlers import RotatingFileHandler
import os

def setup_logging(app):  # 配置日志系统
    os.makedirs('logs', exist_ok=True)
    
    handler = RotatingFileHandler('logs/app.log', maxBytes=10000, backupCount=3, delay=True)
    handler.setLevel(logging.INFO)
    formatter = logging.Formatter(
        '[%(asctime)s] %(levelname)s in %(module)s: %(message)s'
    )
    handler.setFormatter(formatter)
    
    app.logger.addHandler(handler)
    app.logger.setLevel(logging.INFO) 