{% extends 'base.html' %}

{% block title %}线索池管理 - CRM系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>线索池管理</h2>
        <a href="{{ url_for('pool_management.add_pool') }}" class="btn btn-primary">
            <i class="bi bi-plus"></i> 新建线索池
        </a>
    </div>

    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>名称</th>
                    <th>描述</th>
                    {% if current_user.role.code == 'super_admin' %}
                    <th>所属公司</th>
                    {% endif %}
                    <th>类型</th>
                    <th>线索数量</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for pool in pools %}
                <tr>
                    <td>{{ pool.name }}</td>
                    <td>{{ pool.description or '无' }}</td>
                    {% if current_user.role.code == 'super_admin' %}
                    <td>{{ pool.company.name }}</td>
                    {% endif %}
                    <td>
                        {% if pool.is_public_sea %}
                        <span class="badge bg-info">公海池</span>
                        {% else %}
                        <span class="badge bg-secondary">普通池</span>
                        {% endif %}
                    </td>
                    <td>
                        <a href="{{ url_for('pool_management.pool_leads', pool_id=pool.id) }}">
                            {{ pool.leads|length }}
                        </a>
                    </td>
                    <td>
                        <a href="{{ url_for('pool_management.pool_leads', pool_id=pool.id) }}" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-eye"></i> 查看线索
                        </a>
                        <a href="{{ url_for('pool_management.edit_pool', pool_id=pool.id) }}" 
                           class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-pencil"></i> 编辑
                        </a>
                        {% if not pool.is_public_sea %}
                        <button type="button" 
                                class="btn btn-sm btn-outline-danger" 
                                onclick="confirmDelete({{ pool.id }})">
                            <i class="bi bi-trash"></i> 删除
                        </button>
                        {% endif %}
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="{% if current_user.role.code == 'super_admin' %}6{% else %}5{% endif %}" class="text-center">
                        暂无线索池
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- 删除确认对话框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除这个线索池吗？池中的线索将被移动到公海池。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(poolId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const form = document.getElementById('deleteForm');
    form.action = "{{ url_for('pool_management.delete_pool', pool_id=0) }}".replace('0', poolId);
    modal.show();
}
</script>
{% endblock %}