import os # Add os import
from models import Company, LeadPool, db  # 导入必要的模型

def init_app(app, db):  # 应用程序初始化
    # Check for migration environment variable - kept for future, but less critical if deleting DB
    if os.environ.get('FLASK_MIGRATE_RUNNING') == '1':
        app.logger.info("迁移模式：跳过 init_app 中的数据库初始化操作")
        return

    app.logger.info('开始初始化应用程序')
    try:
        with app.app_context(): # Ensure operations are within app context
            # Ensure tables are created based on current models
            # This is crucial if the database file was deleted.
            db.create_all() 
            app.logger.info('数据库表已确保存在/已创建')
            
            # It's good practice to begin a transaction for data seeding/initialization
            with db.session.begin_nested():
                companies = Company.query.all()
                app.logger.info(f'发现 {len(companies)} 个公司，准备初始化线索池')
                
                for company in companies:
                    init_company_pools(app, db, company) # Pass app as first argument
            
            db.session.commit()
            app.logger.info('线索池初始化完成')
    except Exception as e:
        app.logger.error(f'应用程序初始化失败：{str(e)}', exc_info=True)
        # Consider re-raising the exception if initialization is critical
        # raise

def init_company_pools(app, db, company):  # 初始化公司的线索池
    # Check for migration environment variable - kept for future
    if os.environ.get('FLASK_MIGRATE_RUNNING') == '1':
        app.logger.info(f"迁移模式：跳过公司 {company.name} 的 init_company_pools 操作")
        return

    app.logger.info(f'为公司 {company.name} 初始化线索池...')
    
    # 检查并创建公海池
    # Query for existing public sea with the correct pool_type if it matters for idempotency
    # For now, we assume if it's a public sea by name/flag, its type should also be PUBLIC_SEA
    public_sea_pool = LeadPool.query.filter_by(company_id=company.id, is_public_sea=True).first()
    if not public_sea_pool:
        public_sea = LeadPool(
            name=f"{company.name}公海池",
            description=f"{company.name}的默认公海池，用于存放无负责人的线索",
            company_id=company.id,
            is_public_sea=True,
            pool_type='PUBLIC_SEA'  # Explicitly set pool_type
        )
        db.session.add(public_sea)
        app.logger.info(f'为公司 {company.name} 创建了公海池 (类型: PUBLIC_SEA)')
    elif public_sea_pool.pool_type != 'PUBLIC_SEA': # If it exists but has wrong type
        public_sea_pool.pool_type = 'PUBLIC_SEA'
        app.logger.info(f'公司 {company.name} 的现有公海池类型已更新为 PUBLIC_SEA')
        
    # 检查并创建普通线索池
    # Query for existing normal pool with the correct pool_type
    normal_pool_instance = LeadPool.query.filter_by(company_id=company.id, is_public_sea=False, pool_type='PRIVATE').first()
    if not normal_pool_instance:
        # Check if a pool exists that is_public_sea=False but has a different/old pool_type
        old_normal_pool = LeadPool.query.filter_by(company_id=company.id, is_public_sea=False).first()
        if old_normal_pool and old_normal_pool.pool_type != 'PRIVATE':
            old_normal_pool.pool_type = 'PRIVATE'
            app.logger.info(f'公司 {company.name} 的现有普通线索池类型已更新为 PRIVATE')
        elif not old_normal_pool:
            normal_pool_to_add = LeadPool(
                name=f"{company.name}线索池",
                description=f"{company.name}的默认线索池，用于存放有负责人的线索",
                company_id=company.id,
                is_public_sea=False,
                pool_type='PRIVATE'  # Explicitly set pool_type (model default is also PRIVATE)
            )
            db.session.add(normal_pool_to_add)
            app.logger.info(f'为公司 {company.name} 创建了普通线索池 (类型: PRIVATE)')
    # Task 1.3 from checklist: Create CROSS_LOCATION_PENDING pool for each company
    # This should be idempotent as well.
    cross_location_pool = LeadPool.query.filter_by(company_id=company.id, pool_type='CROSS_LOCATION_PENDING').first()
    if not cross_location_pool:
        new_cross_pool = LeadPool(
            name=f"{company.name} - 异地到店池",
            description=f"用于接收其他公司推送的异地到店线索，等待 {company.name} 认领",
            company_id=company.id,
            is_public_sea=False, # Typically these are not public seas
            pool_type='CROSS_LOCATION_PENDING'
        )
        db.session.add(new_cross_pool)
        app.logger.info(f'为公司 {company.name} 创建了异地到店池 (类型: CROSS_LOCATION_PENDING)') 