from datetime import datetime
from . import db
from utils.timezone_compat import get_shanghai_now

class Lead(db.Model):
    id = db.Column(db.<PERSON><PERSON><PERSON>, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # 姓名
    company_name = db.Column(db.String(100))  # 公司名称 (之前注释为省份)
    email = db.Column(db.String(100))  # 邮箱 (之前注释为城市)
    phone = db.Column(db.String(20))  # 电话
    notes = db.Column(db.Text)  # 备注

    # 新的状态跟踪字段
    is_called = db.Column(db.<PERSON>, default=False)  # 是否拨打
    is_connected = db.Column(db.Bo<PERSON>, default=False)  # 是否接通
    is_valid_call = db.Column(db.Bo<PERSON>an, default=False)  # 是否有效通话
    is_wechat_added = db.Column(db.<PERSON>, default=False)  # 是否加微
    is_intentional = db.Column(db.<PERSON>ole<PERSON>, default=False)  # 是否意向客户
    is_compliant = db.Column(db.<PERSON>, default=False)  # 是否合规
    is_visited = db.Column(db.Boolean, default=False)  # 是否到面
    is_car_selected = db.Column(db.Boolean, default=False)  # 是否提车
    is_deal_done = db.Column(db.Boolean, default=False)  # 是否签约
    
    # 其他字段保持不变
    status = db.Column(db.String(20), nullable=False, default='new')  # 线索状态
    owner_id = db.Column(db.Integer, db.ForeignKey('user.id'))  # 负责人
    creator_id = db.Column(db.Integer, db.ForeignKey('user.id'))  # 创建者
    company_id = db.Column(db.Integer, db.ForeignKey('company.id'))  # 当前所属公司 (原始归属公司)
    original_company_id = db.Column(db.Integer, db.ForeignKey('company.id'))  # 原始录入公司
    current_processing_company_id = db.Column(db.Integer, db.ForeignKey('company.id'), nullable=True) # 新增字段: 当前实际处理公司
    current_processing_employee_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True) # 新增字段: 当前处理员工
    pool_id = db.Column(db.Integer, db.ForeignKey('lead_pool.id'))  # 所属线索池
    is_in_public_sea = db.Column(db.Boolean, default=False)  # 是否在公海中
    public_sea_time = db.Column(db.DateTime)  # 进入公海时间
    protection_end_time = db.Column(db.DateTime)  # 保护期结束时间
    is_self_created = db.Column(db.Boolean, default=True)  # 是否为自录入
    last_owner_id = db.Column(db.Integer, db.ForeignKey('user.id'))  # 上一个负责人
    deal_status = db.Column(db.String(20), default='pending')  # 签约状态
    deal_feedback_time = db.Column(db.DateTime)  # 签约反馈时间
    last_cross_location_push_time = db.Column(db.DateTime, nullable=True) # 新增字段: 最近一次推送到异地池的时间
    created_at = db.Column(db.DateTime, default=get_shanghai_now)
    updated_at = db.Column(db.DateTime, default=get_shanghai_now, onupdate=get_shanghai_now)
    
    # 关系
    owner = db.relationship('User', foreign_keys=[owner_id], backref='owned_leads')
    creator = db.relationship('User', foreign_keys=[creator_id], backref='created_leads')
    last_owner = db.relationship('User', foreign_keys=[last_owner_id], backref='previously_owned_leads')
    current_processing_employee = db.relationship('User', foreign_keys=[current_processing_employee_id], backref='processing_leads') # 新增关系
    current_company = db.relationship('Company', foreign_keys=[company_id], backref='current_leads') # 注意：这个关系名可能需要调整含义，因为它现在代表原始公司
    original_company = db.relationship('Company', foreign_keys=[original_company_id], backref='created_leads_original') # 修改 backref 以避免冲突
    processing_company = db.relationship('Company', foreign_keys=[current_processing_company_id], backref='processing_leads') # 新增关系
    pool = db.relationship('LeadPool', back_populates='leads')

    @property
    def current_stage(self):
        """获取当前所处阶段"""
        # 根据状态字段确定当前最高阶段，按照业务流程顺序
        if self.is_car_selected:  # 提车是最终阶段
            return "已提车"
        if self.is_deal_done:     # 签约
            return "已签约"
        if self.is_compliant:     # 信息合规
            return "信息合规"
        if self.is_visited:       # 已到面
            return "已到面"
        if self.is_intentional:   # 意向客户
            return "意向客户"
        if self.is_wechat_added:  # 已加微信
            return "已加微信"
        if self.is_valid_call:    # 有效通话
            return "有效通话"
        if self.is_connected:     # 已接通
            return "已接通"
        if self.is_called:        # 已拨电话
            return "已拨电话"
        return "新线索"           # 初始状态

class LeadStatusHistory(db.Model):
    """线索状态变更历史"""
    id = db.Column(db.Integer, primary_key=True)
    lead_id = db.Column(db.Integer, db.ForeignKey('lead.id'), nullable=False)
    status_field = db.Column(db.String(30), nullable=False)  # 状态字段名
    old_value = db.Column(db.Boolean)  # 原状态值
    new_value = db.Column(db.Boolean)  # 新状态值
    notes = db.Column(db.String(200))  # 备注
    created_at = db.Column(db.DateTime, default=get_shanghai_now)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    
    # 关系
    lead = db.relationship('Lead', backref='status_history')
    creator = db.relationship('User', foreign_keys=[created_by])

class LeadTransferHistory(db.Model):
    """线索转移历史"""
    id = db.Column(db.Integer, primary_key=True)
    lead_id = db.Column(db.Integer, db.ForeignKey('lead.id'), nullable=False)
    from_company_id = db.Column(db.Integer, db.ForeignKey('company.id'))
    to_company_id = db.Column(db.Integer, db.ForeignKey('company.id'))
    target_company_id_for_processing = db.Column(db.Integer, db.ForeignKey('company.id'), nullable=True)
    from_pool_id = db.Column(db.Integer, db.ForeignKey('lead_pool.id'))
    to_pool_id = db.Column(db.Integer, db.ForeignKey('lead_pool.id'))
    from_user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    to_user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    transfer_type = db.Column(db.String(20), nullable=False)  # 分配/转移/释放/捞取
    notes = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=get_shanghai_now)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    
    # 关系
    lead = db.relationship('Lead', backref='transfer_history')
    from_company = db.relationship('Company', foreign_keys=[from_company_id])
    to_company = db.relationship('Company', foreign_keys=[to_company_id])
    from_pool = db.relationship('LeadPool', foreign_keys=[from_pool_id])
    to_pool = db.relationship('LeadPool', foreign_keys=[to_pool_id])
    from_user = db.relationship('User', foreign_keys=[from_user_id])
    to_user = db.relationship('User', foreign_keys=[to_user_id])
    creator = db.relationship('User', foreign_keys=[created_by])

class CompanyLeadAssignRequest(db.Model):
    """跨公司线索分配请求"""
    id = db.Column(db.Integer, primary_key=True)
    lead_id = db.Column(db.Integer, db.ForeignKey('lead.id'), nullable=False)
    from_company_id = db.Column(db.Integer, db.ForeignKey('company.id'), nullable=False)
    to_company_id = db.Column(db.Integer, db.ForeignKey('company.id'), nullable=False)
    requested_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    status = db.Column(db.String(20), default='pending', index=True)  # pending, accepted, rejected
    created_at = db.Column(db.DateTime, default=get_shanghai_now)
    processed_at = db.Column(db.DateTime)
    processed_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    notes = db.Column(db.String(255))
    
    # 关系
    lead = db.relationship('Lead', backref='assignment_requests')
    from_company = db.relationship('Company', foreign_keys=[from_company_id])
    to_company = db.relationship('Company', foreign_keys=[to_company_id])
    requester = db.relationship('User', foreign_keys=[requested_by])
    processor = db.relationship('User', foreign_keys=[processed_by])

class SystemMessage(db.Model):
    """系统消息"""
    id = db.Column(db.Integer, primary_key=True)
    message_type = db.Column(db.String(50), nullable=False, index=True)  # assignment_request, system_notice, etc.
    related_id = db.Column(db.Integer)  # 相关对象ID，如请求ID
    target_company_id = db.Column(db.Integer, db.ForeignKey('company.id'), index=True)
    target_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), index=True)
    title = db.Column(db.String(100), nullable=False)
    content = db.Column(db.Text, nullable=False)
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=get_shanghai_now)
    expiry_at = db.Column(db.DateTime)
    
    # 关系
    target_company = db.relationship('Company', foreign_keys=[target_company_id])
    target_user = db.relationship('User', foreign_keys=[target_user_id])