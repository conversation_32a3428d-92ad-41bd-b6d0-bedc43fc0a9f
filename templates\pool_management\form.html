{% extends 'base.html' %}

{% block title %}{{ '编辑' if pool else '新建' }}线索池 - CRM系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">{{ '编辑' if pool else '新建' }}线索池</h3>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate data-skip-validation="true">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <div class="mb-3">
                            <label for="name" class="form-label">名称</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="name" 
                                   name="name" 
                                   value="{{ pool.name if pool else '' }}"
                                   required>
                            <div class="invalid-feedback">
                                请输入线索池名称
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">描述</label>
                            <textarea class="form-control" 
                                      id="description" 
                                      name="description" 
                                      rows="3">{{ pool.description if pool else '' }}</textarea>
                        </div>

                        {% if current_user.role.code == 'super_admin' %}
                        <div class="mb-3">
                            <label for="company_id" class="form-label">所属公司</label>
                            <select class="form-select" id="company_id" name="company_id" required>
                                <option value="">请选择公司</option>
                                {% for company in companies %}
                                <option value="{{ company.id }}" 
                                        {% if pool and pool.company_id == company.id %}selected{% endif %}>
                                    {{ company.name }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                请选择所属公司
                            </div>
                        </div>
                        {% endif %}

                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" 
                                       class="form-check-input" 
                                       id="is_public_sea" 
                                       name="is_public_sea" 
                                       value="1"
                                       {% if pool and pool.is_public_sea %}checked{% endif %}
                                       {% if pool and pool.is_public_sea %}disabled{% endif %}>
                                <label class="form-check-label" for="is_public_sea">
                                    设为公海池
                                </label>
                                {% if pool and pool.is_public_sea %}
                                <div class="form-text text-muted">
                                    公海池类型不可更改
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('pool_management.index') }}" class="btn btn-secondary">返回</a>
                            <button type="submit" class="btn btn-primary">保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 表单验证
(function () {
    'use strict'
    const forms = document.querySelectorAll('.needs-validation')
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()
</script>
{% endblock %} 