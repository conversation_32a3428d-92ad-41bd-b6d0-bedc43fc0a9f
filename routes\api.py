from flask import Blueprint, jsonify, current_app
from flask_login import current_user, login_required
from models import Lead, User, Company, Department
from datetime import datetime, timedelta
from sqlalchemy import func

bp = Blueprint('api', __name__)

@bp.route('/api/dashboard/stats')
@login_required
def dashboard_stats():
    # 根据用户权限获取不同范围的数据
    if current_user.has_permission('view_all'):
        # 超级管理员可以看到所有数据
        leads_query = Lead.query
        users_query = User.query
    elif current_user.has_permission('company_user_manage'):
        # 公司管理员只能看到本公司数据
        leads_query = Lead.query.join(User, Lead.owner_id == User.id).filter(User.company_id == current_user.company_id)
        users_query = User.query.filter_by(company_id=current_user.company_id)
    elif current_user.has_permission('department_user_manage'):
        # 部门管理员只能看到本部门数据
        leads_query = Lead.query.join(User, Lead.owner_id == User.id).filter(
            User.company_id == current_user.company_id,
            User.department_id == current_user.department_id
        )
        users_query = User.query.filter_by(
            company_id=current_user.company_id,
            department_id=current_user.department_id
        )
    else:
        # 普通销售只能看到自己的数据
        leads_query = Lead.query.filter_by(owner_id=current_user.id)
        users_query = User.query.filter_by(id=current_user.id)

    # 获取线索转化漏斗数据
    lead_funnel = [
        leads_query.filter_by(status='new').count(),
        leads_query.filter_by(status='following').count(),
        leads_query.filter_by(status='converted').count(),
        leads_query.filter_by(status='invalid').count()
    ]

    # 获取用户活跃度数据（最近7天）
    now = datetime.now()
    dates = [(now - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(6, -1, -1)]
    activity_data = {
        'labels': dates,
        'values': []
    }

    for date in dates:
        start_date = datetime.strptime(date, '%Y-%m-%d')
        end_date = start_date + timedelta(days=1)
        active_users = leads_query.filter(
            Lead.updated_at >= start_date,
            Lead.updated_at < end_date
        ).distinct(Lead.owner_id).count()
        activity_data['values'].append(active_users)

    # 获取线索来源分布数据
    sources = leads_query.with_entities(
        Lead.source,
        func.count(Lead.id)
    ).group_by(Lead.source).all()
    lead_source = {source: count for source, count in sources}

    return jsonify({
        'leadFunnel': lead_funnel,
        'userActivity': activity_data,
        'leadSource': lead_source
    })

@bp.route('/api/companies/<int:company_id>/departments')
@login_required
def get_company_departments(company_id):
    # 获取指定公司的所有部门
    departments = Department.query.filter_by(company_id=company_id).all()
    return jsonify([
        {
            'id': dept.id,
            'name': dept.name,
            'code': dept.code
        } for dept in departments
    ])

@bp.route('/api/companies/<int:company_id>/employees')
@login_required
def get_company_employees(company_id):
    # 获取指定公司的所有员工
    try:
        # 检查权限 - 允许超级管理员和公司管理员访问其他公司员工信息（用于跨公司分配）
        if current_user.role.code not in ['super_admin', 'company_admin']:
            return jsonify({'error': '没有权限访问此公司的员工信息'}), 403

        employees = User.query.filter_by(company_id=company_id).all()
        return jsonify([
            {
                'id': emp.id,
                'name': emp.name or emp.username,
                'username': emp.username,
                'department_id': emp.department_id
            } for emp in employees
        ])
    except Exception as e:
        current_app.logger.error(f'获取公司 {company_id} 的员工列表失败: {str(e)}')
        return jsonify({'error': '获取员工列表失败'}), 500