import base64
from flask import current_app
from cryptography.fernet import <PERSON><PERSON><PERSON>, InvalidToken
from . import db
import logging

logger = logging.getLogger(__name__)

# --- Encryption Utilities ---

def _get_fernet():
    """Generates a Fernet key from the app's SECRET_KEY."""
    secret_key = current_app.config.get('SECRET_KEY')
    if not secret_key:
        raise ValueError("SECRET_KEY is not set in the application configuration.")
    # Ensure the key is URL-safe base64 encoded and 32 bytes long
    # Using the first 32 bytes of the secret key if it's longer, padded if shorter (less ideal but works)
    key = secret_key.encode('utf-8')
    if len(key) < 32:
        key = key + b'=' * (32 - len(key)) # Pad if needed
    elif len(key) > 32:
        key = key[:32] # Truncate if needed
        
    encoded_key = base64.urlsafe_b64encode(key)
    return Fernet(encoded_key)

def encrypt_value(value):
    """Encrypts a string value using <PERSON><PERSON><PERSON>."""
    if value is None:
        return None
    try:
        f = _get_fernet()
        return f.encrypt(value.encode('utf-8'))
    except Exception as e:
        logger.error("Encryption failed: {}".format(e), exc_info=True)
        raise

def decrypt_value(encrypted_value):
    """Decrypts a Fernet encrypted byte string."""
    if encrypted_value is None:
        return None
    try:
        f = _get_fernet()
        decrypted_bytes = f.decrypt(encrypted_value)
        return decrypted_bytes.decode('utf-8')
    except InvalidToken:
        logger.error("Decryption failed: Invalid token.")
        # Depending on policy, either raise an error or return None/empty string
        return None # Or raise ValueError("Invalid encrypted data")
    except Exception as e:
        logger.error("Decryption failed: {}".format(e), exc_info=True)
        raise

# --- Setting Model ---

class Setting(db.Model):
    __tablename__ = 'settings'

    key = db.Column(db.String(50), primary_key=True)
    value = db.Column(db.LargeBinary, nullable=True) # Store potentially large values, including encrypted ones
    is_encrypted = db.Column(db.Boolean, default=False, nullable=False)

    @classmethod
    def get(cls, key, default=None, decrypt=False):
        """Gets a setting value, optionally decrypting it."""
        setting = cls.query.get(key)
        if setting:
            if setting.is_encrypted and decrypt:
                try:
                    # Assuming value is stored as bytes from encryption
                    return decrypt_value(setting.value)
                except Exception as e:
                    logger.error(f"Failed to decrypt setting '{key}': {e}")
                    return default # Return default if decryption fails
            elif not setting.is_encrypted:
                 # If not encrypted, decode bytes back to string if needed
                 try:
                     return setting.value.decode('utf-8') if setting.value else default
                 except Exception as e:
                     logger.error(f"Failed to decode setting '{key}': {e}")
                     return default
            else: # It's encrypted but decryption wasn't requested
                return setting.value # Return raw encrypted bytes
        return default

    @classmethod
    def set(cls, key, value, encrypt=False):
        """Sets a setting value, optionally encrypting it."""
        setting = cls.query.get(key)
        if not setting:
            setting = cls(key=key)
            db.session.add(setting)

        setting.is_encrypted = encrypt
        if value is None:
             setting.value = None
        elif encrypt:
            try:
                setting.value = encrypt_value(str(value)) # Ensure value is string before encrypting
            except Exception as e:
                 logger.error(f"Failed to encrypt setting '{key}': {e}")
                 # Decide how to handle encryption failure, maybe raise error or skip setting
                 return # Skip setting on encryption failure
        else:
             # Store non-encrypted values as bytes as well for consistency
             setting.value = str(value).encode('utf-8')

    def __repr__(self):
        return f'<Setting {self.key}>' 