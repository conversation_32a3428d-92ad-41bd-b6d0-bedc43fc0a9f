#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CRM系统管理员重置脚本
适用于当前系统版本，用于重置或创建管理员账号
"""

import os
import sys
from datetime import datetime
from werkzeug.security import generate_password_hash

def reset_admin_user(username='admin', password='admin123', email='<EMAIL>'):
    """重置管理员用户"""
    try:
        # 确保在正确的目录
        if not os.path.isfile('app.py'):
            print("❌ 错误: 请在项目根目录下运行此脚本！")
            print("   当前目录:", os.getcwd())
            return False

        # 导入应用和模型
        from app import create_app
        from config import Config
        from models import db, User, Role
        
        app = create_app(Config)
        
        with app.app_context():
            print("🔧 CRM系统管理员重置")
            print("="*50)
            print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print()
            
            # 1. 检查数据库连接
            try:
                user_count = User.query.count()
                print(f"✅ 数据库连接正常，当前用户数: {user_count}")
            except Exception as e:
                print(f"❌ 数据库连接失败: {e}")
                return False
            
            # 2. 查找超级管理员角色
            super_admin_role = Role.query.filter_by(code='super_admin').first()
            if not super_admin_role:
                print("❌ 超级管理员角色不存在")
                print("   请先运行权限初始化脚本")
                return False
            
            print(f"✅ 找到超级管理员角色: {super_admin_role.name}")
            
            # 3. 查找或创建管理员用户
            admin_user = User.query.filter_by(username=username).first()
            
            if admin_user:
                print(f"📝 找到现有用户: {username}")
                print(f"   当前状态: {admin_user.status}")
                print(f"   当前角色: {admin_user.role.name if admin_user.role else '无'}")
                
                # 更新现有用户
                admin_user.password_hash = generate_password_hash(password)
                admin_user.status = 'active'
                admin_user.role_id = super_admin_role.id
                admin_user.email = email
                admin_user.name = '系统管理员'
                
                action = "更新"
            else:
                print(f"🆕 创建新用户: {username}")
                
                # 创建新用户
                admin_user = User(
                    username=username,
                    name='系统管理员',
                    email=email,
                    password_hash=generate_password_hash(password),
                    role_id=super_admin_role.id,
                    status='active'
                )
                db.session.add(admin_user)
                action = "创建"
            
            # 4. 保存更改
            db.session.commit()
            
            print(f"✅ 管理员账号{action}成功")
            print()
            print("="*50)
            print("🎉 重置完成！")
            print("="*50)
            print("📋 管理员账号信息:")
            print(f"   用户名: {username}")
            print(f"   密码: {password}")
            print(f"   邮箱: {email}")
            print(f"   角色: {super_admin_role.name}")
            print(f"   状态: {admin_user.status}")
            print()
            print("⚠️  安全提醒:")
            print("   - 请立即登录系统修改默认密码")
            print("   - 建议设置强密码")
            print("   - 定期更换密码")
            print("="*50)
            
            return True
            
    except Exception as e:
        print(f"❌ 重置失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='CRM系统管理员重置脚本')
    parser.add_argument('--username', '-u', default='admin',
                       help='管理员用户名 (默认: admin)')
    parser.add_argument('--password', '-p', default='admin123',
                       help='管理员密码 (默认: admin123)')
    parser.add_argument('--email', '-e', default='<EMAIL>',
                       help='管理员邮箱 (默认: <EMAIL>)')
    parser.add_argument('--force', '-f', action='store_true',
                       help='强制执行，跳过确认')
    
    args = parser.parse_args()
    
    print(f"📍 当前目录: {os.getcwd()}")
    print(f"⏰ 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 确认操作
    if not args.force:
        print("⚠️  此操作将重置管理员账号密码")
        print(f"   用户名: {args.username}")
        print(f"   新密码: {args.password}")
        print()
        confirm = input("是否继续？(y/N): ").lower().strip()
        if confirm not in ['y', 'yes']:
            print("操作已取消")
            return
    
    print()
    
    # 执行重置
    success = reset_admin_user(
        username=args.username,
        password=args.password,
        email=args.email
    )
    
    if success:
        print("\n🎯 下一步:")
        print("   1. 启动应用: python app.py")
        print("   2. 访问系统: http://127.0.0.1:5000/")
        print(f"   3. 使用 {args.username}/{args.password} 登录")
        print("   4. 立即修改密码")
    else:
        print("\n❌ 重置失败，请检查错误信息")
        sys.exit(1)

if __name__ == '__main__':
    main()
