{% extends 'base.html' %}

{% block title %}{% if form.name.data %}编辑部门{% else %}添加部门{% endif %} - CRM系统{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">{% if form.name.data %}编辑部门{% else %}添加部门{% endif %}</h4>
                </div>
                <div class="card-body">
                    <form method="post" id="departmentForm" data-skip-validation="true">
                        {{ form.csrf_token }}
                        <!-- 隐藏的company_id字段 -->
                        {{ form.company_id(style="display: none;") }}
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">部门名称</label>
                            {{ form.name(class="form-control", id="name", required=true) }}
                            {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.name.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="code" class="form-label">部门代码</label>
                            {{ form.code(class="form-control", id="code", required=true) }}
                            {% if form.code.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.code.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <div class="form-text">唯一的部门标识代码 (注意: 在同一公司内必须唯一)</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="parent_id" class="form-label">上级部门</label>
                            {{ form.parent_id(class="form-select", id="parent_id") }}
                            {% if form.parent_id.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.parent_id.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="type" class="form-label">部门类型</label>
                            {{ form.type(class="form-select", id="type") }}
                            {% if form.type.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.type.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <div class="form-text">选择"部门"或"小组"，小组为部门下的更小单位</div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">保存</button>
                            <a href="{{ url_for('organization.departments', company_id=company.id) }}" class="btn btn-secondary">返回</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('departmentForm').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const code = document.getElementById('code').value.trim();
    
    if (!name || !code) {
        e.preventDefault();
        alert('请填写所有必填字段');
        return false;
    }
    
    // 检查部门代码格式
    if (!/^[A-Za-z0-9_-]+$/.test(code)) {
        e.preventDefault();
        alert('部门代码只能包含字母、数字、下划线和连字符');
        return false;
    }
});
</script>
{% endblock %}