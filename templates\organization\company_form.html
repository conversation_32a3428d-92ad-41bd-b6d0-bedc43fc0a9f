{% extends 'base.html' %}

{% block title %}{% if form.name.data %}编辑公司{% else %}添加公司{% endif %} - CRM系统{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">{% if form.name.data %}编辑公司{% else %}添加公司{% endif %}</h4>
                </div>
                <div class="card-body">
                    <form method="post" data-skip-validation="true">
                        {{ form.csrf_token }}
                        <div class="mb-3">
                            <label for="name" class="form-label">公司名称</label>
                            {{ form.name(class="form-control", id="name", required=true) }}
                            {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.name.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="code" class="form-label">公司代码</label>
                            {{ form.code(class="form-control", id="code", required=true) }}
                            {% if form.code.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.code.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <div class="form-text">唯一的公司标识代码 (全局唯一，不能重复)</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="type" class="form-label">公司类型</label>
                            {{ form.type(class="form-select", id="type", required=true) }}
                            {% if form.type.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.type.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3" id="parent-company-group">
                            <label for="parent_id" class="form-label">上级公司</label>
                            {{ form.parent_id(class="form-select", id="parent_id") }}
                            {% if form.parent_id.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.parent_id.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3 form-check">
                            {{ form.is_default(class="form-check-input", id="is_default") }}
                            <label class="form-check-label" for="is_default">设为默认公司</label>
                            {% if form.is_default.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.is_default.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">保存</button>
                            <a href="{{ url_for('organization.companies') }}" class="btn btn-secondary">返回</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
    // 根据公司类型显示/隐藏上级公司选项
    document.getElementById('type').addEventListener('change', function() {
        const parentGroup = document.getElementById('parent-company-group');
        if (this.value === 'subsidiary') {
            parentGroup.style.display = 'block';
        } else {
            parentGroup.style.display = 'none';
            document.getElementById('parent_id').value = '0';
        }
    });
    
    // 页面加载时触发一次
    document.getElementById('type').dispatchEvent(new Event('change'));
    
    // 省市区联动
    const provinceSelect = document.getElementById('province');
    const citySelect = document.getElementById('city');
    const districtSelect = document.getElementById('district');
    
    // 加载省市区数据
    fetch('/static/data/provinces_cities.json')
        .then(response => response.json())
        .then(data => {
            // 加载省份
            let provinceOptions = '<option value="">请选择</option>';
            data.provinces.forEach(province => {
                provinceOptions += `<option value="${province.name}">${province.name}</option>`;
            });
            provinceSelect.innerHTML = provinceOptions;
            
            // 省份变更时加载城市
            provinceSelect.addEventListener('change', function() {
                const provinceName = this.value;
                citySelect.innerHTML = '<option value="">请选择</option>';
                districtSelect.innerHTML = '<option value="">请选择</option>';
                
                if (!provinceName) return;
                
                const province = data.provinces.find(p => p.name === provinceName);
                if (province && province.cities) {
                    let cityOptions = '<option value="">请选择</option>';
                    province.cities.forEach(city => {
                        cityOptions += `<option value="${city}">${city}</option>`;
                    });
                    citySelect.innerHTML = cityOptions;
                }
            });
            
            // 如果已有选中值，触发省份和城市的变更事件
            if (provinceSelect.value) {
                provinceSelect.dispatchEvent(new Event('change'));
                
                if (citySelect.getAttribute('data-value')) {
                    setTimeout(() => {
                        citySelect.value = citySelect.getAttribute('data-value');
                        citySelect.dispatchEvent(new Event('change'));
                    }, 100);
                }
            }
        })
        .catch(error => console.error('加载省市区数据失败:', error));
</script>
{% endblock %}
{% endblock %}