/**
 * CRM系统UI增强功能
 * 提供统一的交互体验和动画效果
 */

class CRMUIEnhancements {
    constructor() {
        this.init();
    }

    init() {
        this.initTooltips();
        this.initAnimations();
        this.initTableEnhancements();
        this.initFormEnhancements();
        this.initLoadingStates();
        this.initNotifications();
        this.updateLastUpdateTime();
    }

    /**
     * 初始化工具提示
     */
    initTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl, {
                delay: { show: 500, hide: 100 }
            });
        });
    }

    /**
     * 初始化动画效果
     */
    initAnimations() {


        // 页面加载动画
        document.addEventListener('DOMContentLoaded', () => {
            document.body.classList.add('loaded');
        });

        // 卡片悬停效果
        const cards = document.querySelectorAll('.card, .stats-card-enhanced');
        cards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-2px)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });

        // 简化的按钮点击效果
        const buttons = document.querySelectorAll('.btn:not(.page-header .btn)');
        buttons.forEach(button => {
            button.addEventListener('click', function(e) {
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.style.transform = 'scale(0)';
                ripple.classList.add('ripple');

                this.appendChild(ripple);

                setTimeout(() => {
                    if (ripple.parentNode) {
                        ripple.remove();
                    }
                }, 600);
            });
        });
    }

    /**
     * 表格增强功能
     */
    initTableEnhancements() {
        // 表格行点击效果
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('click', function(e) {
                // 如果点击的是按钮或链接，不执行行选择
                if (e.target.closest('button, a')) return;
                
                // 移除其他行的选中状态
                tableRows.forEach(r => r.classList.remove('table-active'));
                
                // 添加当前行的选中状态
                this.classList.add('table-active');
            });
        });

        // 表格排序功能
        const sortableHeaders = document.querySelectorAll('th[data-sort]');
        sortableHeaders.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                this.sortTable(header);
            });
        });
    }

    /**
     * 表单增强功能
     */
    initFormEnhancements() {
        // 表单验证增强 - 排除个人资料表单
        const forms = document.querySelectorAll('form:not([action*="profile"])');
        forms.forEach(form => {
            // 只对特定表单启用验证
            if (!form.hasAttribute('data-skip-validation')) {
                form.addEventListener('submit', (e) => {
                    if (!this.validateForm(form)) {
                        e.preventDefault();
                    }
                });
            }
        });

        // 输入框焦点效果
        const inputs = document.querySelectorAll('.form-control, .form-select');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('focused');
            });
        });

        // 自动保存功能
        const autoSaveInputs = document.querySelectorAll('[data-auto-save]');
        autoSaveInputs.forEach(input => {
            let timeout;
            input.addEventListener('input', () => {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    this.autoSave(input);
                }, 1000);
            });
        });
    }

    /**
     * 加载状态管理
     */
    initLoadingStates() {
        // 为所有提交按钮添加加载状态 - 排除个人资料表单
        const submitButtons = document.querySelectorAll('button[type="submit"]:not([form*="profile"]):not(form[action*="profile"] button[type="submit"])');
        submitButtons.forEach(button => {
            // 检查按钮是否在个人资料表单中
            const form = button.closest('form');
            if (!form || (!form.action.includes('profile') && !form.hasAttribute('data-skip-validation'))) {
                button.addEventListener('click', () => {
                    this.showButtonLoading(button);
                });
            }
        });

        // AJAX请求加载状态
        this.setupAjaxLoading();
    }

    /**
     * 通知系统
     */
    initNotifications() {
        // 自动隐藏成功消息
        const successAlerts = document.querySelectorAll('.alert-success');
        successAlerts.forEach(alert => {
            setTimeout(() => {
                this.fadeOut(alert);
            }, 5000);
        });

        // 错误消息保持显示，需要手动关闭
        const errorAlerts = document.querySelectorAll('.alert-danger');
        errorAlerts.forEach(alert => {
            alert.classList.add('alert-permanent');
        });
    }

    /**
     * 更新最后更新时间
     */
    updateLastUpdateTime() {
        const timeElement = document.getElementById('lastUpdateTime');
        if (timeElement) {
            const now = new Date();
            timeElement.textContent = now.toLocaleString('zh-CN');
        }
    }



    /**
     * 表单验证
     */
    validateForm(form) {
        let isValid = true;
        const requiredInputs = form.querySelectorAll('[required]');
        
        requiredInputs.forEach(input => {
            if (!input.value.trim()) {
                this.showFieldError(input, '此字段为必填项');
                isValid = false;
            } else {
                this.clearFieldError(input);
            }
        });

        return isValid;
    }

    /**
     * 显示字段错误
     */
    showFieldError(input, message) {
        input.classList.add('is-invalid');
        
        let feedback = input.parentElement.querySelector('.invalid-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            input.parentElement.appendChild(feedback);
        }
        feedback.textContent = message;
    }

    /**
     * 清除字段错误
     */
    clearFieldError(input) {
        input.classList.remove('is-invalid');
        const feedback = input.parentElement.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.remove();
        }
    }

    /**
     * 按钮加载状态
     */
    showButtonLoading(button) {
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>处理中...';
        button.disabled = true;
        
        // 5秒后恢复按钮状态（防止卡死）
        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        }, 5000);
    }

    /**
     * 淡出动画
     */
    fadeOut(element) {
        element.style.transition = 'opacity 0.5s ease';
        element.style.opacity = '0';
        setTimeout(() => {
            element.remove();
        }, 500);
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // 自动隐藏
        setTimeout(() => {
            this.fadeOut(notification);
        }, 5000);
    }

    /**
     * AJAX加载状态设置
     */
    setupAjaxLoading() {
        // 如果使用jQuery
        if (typeof $ !== 'undefined') {
            $(document).ajaxStart(() => {
                this.showGlobalLoading();
            }).ajaxStop(() => {
                this.hideGlobalLoading();
            });
        }
    }

    /**
     * 显示全局加载
     */
    showGlobalLoading() {
        if (!document.getElementById('globalLoading')) {
            const loading = document.createElement('div');
            loading.id = 'globalLoading';
            loading.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
            loading.style.cssText = 'background: rgba(255,255,255,0.8); z-index: 9999;';
            loading.innerHTML = '<div class="loading-spinner"></div>';
            document.body.appendChild(loading);
        }
    }

    /**
     * 隐藏全局加载
     */
    hideGlobalLoading() {
        const loading = document.getElementById('globalLoading');
        if (loading) {
            loading.remove();
        }
    }
}

// 初始化UI增强功能
document.addEventListener('DOMContentLoaded', () => {
    new CRMUIEnhancements();
});

// 导出类供其他模块使用
window.CRMUIEnhancements = CRMUIEnhancements;
