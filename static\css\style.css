/* ========================================
   CRM系统统一样式文件
   简约而不单调的设计风格
======================================== */

/* CSS变量定义 */
:root {
    /* 主色调 */
    --primary-color: #ff6b00;
    --primary-light: #ff8533;
    --primary-dark: #cc5500;

    /* 辅助色 */
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;

    /* 中性色 */
    --dark-color: #343a40;
    --gray-color: #6c757d;
    --light-gray: #f8f9fa;
    --border-color: #dee2e6;
    --white: #ffffff;

    /* 字体 */
    --font-family: 'Segoe UI', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;

    /* 间距 */
    --spacing-xs: 0.25rem;  /* 4px */
    --spacing-sm: 0.5rem;   /* 8px */
    --spacing-md: 1rem;     /* 16px */
    --spacing-lg: 1.5rem;   /* 24px */
    --spacing-xl: 2rem;     /* 32px */
    --spacing-xxl: 3rem;    /* 48px */

    /* 圆角 */
    --border-radius-sm: 4px;
    --border-radius-md: 6px;
    --border-radius-lg: 8px;
    --border-radius-xl: 12px;

    /* 阴影 */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.15);

    /* 过渡 */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* ========================================
   全局基础样式
======================================== */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-weight: var(--font-weight-normal);
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--light-gray);
    margin: 0;
    padding: 0;
}

/* 平滑滚动 */
html {
    scroll-behavior: smooth;
}

/* 选中文本样式 */
::selection {
    background-color: var(--primary-color);
    color: var(--white);
}

/* ========================================
   导航栏样式
======================================== */
.navbar {
    box-shadow: var(--shadow-md);
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    transition: var(--transition-normal);
}

.navbar-brand {
    font-weight: var(--font-weight-semibold);
    font-size: 1.25rem;
}

.nav-link {
    font-weight: var(--font-weight-medium);
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    transform: translateY(-1px);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background-color: var(--white);
    border-radius: 1px;
}

/* ========================================
   卡片样式
======================================== */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-lg);
    transition: var(--transition-normal);
    background-color: var(--white);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--white);
    border-bottom: 1px solid var(--border-color);
    border-top-left-radius: var(--border-radius-lg);
    border-top-right-radius: var(--border-radius-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    font-weight: var(--font-weight-semibold);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    background-color: var(--light-gray);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-md) var(--spacing-lg);
}

/* 特殊卡片样式 */
.card-stats {
    text-align: center;
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--white) 0%, #fafbfc 100%);
}

.card-stats .card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    font-size: 1.5rem;
}

.card-stats h3 {
    font-size: 2rem;
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-sm);
}

/* ========================================
   按钮样式
======================================== */
.btn {
    border-radius: var(--border-radius-md);
    font-weight: var(--font-weight-medium);
    padding: 0.5rem 1rem;
    transition: var(--transition-fast);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: var(--shadow-md);
}

.btn:active {
    transform: translateY(0) !important;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 107, 0, 0.25);
    transform: none !important;
}

/* 确保波纹效果不会影响按钮本身 */
.btn .ripple {
    transform: scale(0) !important;
    animation: ripple-animation 0.6s linear !important;
}

button:active,
.btn:active,
input[type="button"]:active,
input[type="submit"]:active,
input[type="reset"]:active {
    transform: translateY(0) !important;
}

button:focus,
.btn:focus,
input[type="button"]:focus,
input[type="submit"]:focus,
input[type="reset"]:focus {
    transform: none !important;
}

/* 强制禁用所有可能的缩放和变换效果 */
* {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

/* 允许文本选择 */
input, textarea, [contenteditable] {
    -webkit-user-select: text;
    -khtml-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* 完全禁用按钮的任何变换效果 */
button, .btn, input[type="button"], input[type="submit"], input[type="reset"] {
    -webkit-transform: none !important;
    -moz-transform: none !important;
    -ms-transform: none !important;
    -o-transform: none !important;
    transform: none !important;
    -webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
    -moz-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
    -ms-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
    -o-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
    touch-action: manipulation;
}

/* 禁用双击缩放 */
button, .btn {
    touch-action: manipulation;
    -ms-touch-action: manipulation;
}

/* 按钮专用样式重置 - 防止点击放大效果 */
button,
.btn,
input[type="button"],
input[type="submit"],
input[type="reset"] {
    transform: none !important;
    -webkit-transform: none !important;
    -moz-transform: none !important;
    -ms-transform: none !important;
    -o-transform: none !important;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
    touch-action: manipulation;
}

/* 按钮悬停效果 - 只允许轻微的垂直移动 */
button:hover,
.btn:hover,
input[type="button"]:hover,
input[type="submit"]:hover,
input[type="reset"]:hover {
    transform: translateY(-1px) !important;
}

/* 按钮激活状态 - 重置为原位置 */
button:active,
.btn:active,
input[type="button"]:active,
input[type="submit"]:active,
input[type="reset"]:active {
    transform: translateY(0) !important;
}

/* 卡片悬停效果 */
.card:hover,
.stats-card-enhanced:hover {
    transform: translateY(-4px) !important;
}

.btn:hover {
    -webkit-transform: translateY(-1px) !important;
    -moz-transform: translateY(-1px) !important;
    -ms-transform: translateY(-1px) !important;
    -o-transform: translateY(-1px) !important;
    transform: translateY(-1px) !important;
}

.btn:active {
    -webkit-transform: translateY(0) !important;
    -moz-transform: translateY(0) !important;
    -ms-transform: translateY(0) !important;
    -o-transform: translateY(0) !important;
    transform: translateY(0) !important;
}

/* 波纹效果 */
.ripple {
    -webkit-transform: scale(0) !important;
    -moz-transform: scale(0) !important;
    -ms-transform: scale(0) !important;
    -o-transform: scale(0) !important;
    transform: scale(0) !important;
}



/* 按钮尺寸 */
.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
}

/* 按钮颜色变体 - 纯色设计 */
.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    box-shadow: 0 2px 8px rgba(255, 107, 0, 0.3);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    color: var(--white);
    box-shadow: 0 4px 12px rgba(255, 107, 0, 0.4);
    transform: translateY(-2px) !important;
}

.btn-success {
    background-color: var(--success-color);
    color: var(--white);
    border: none;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
    background-color: #1e7e34;
    color: var(--white);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
    transform: translateY(-2px) !important;
}

.btn-warning {
    background-color: var(--warning-color);
    color: var(--dark-color);
    border: none;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
    font-weight: var(--font-weight-semibold);
}

.btn-warning:hover {
    background-color: #e0a800;
    color: var(--dark-color);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
    transform: translateY(-2px) !important;
}

.btn-danger {
    background-color: var(--danger-color);
    color: var(--white);
    border: none;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.btn-danger:hover {
    background-color: #c82333;
    color: var(--white);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
    transform: translateY(-2px) !important;
}

.btn-info {
    background-color: var(--info-color);
    color: var(--white);
    border: none;
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

.btn-info:hover {
    background-color: #138496;
    color: var(--white);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4);
    transform: translateY(-2px) !important;
}

/* 轮廓按钮 - 纯色设计 */
.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background-color: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(255, 107, 0, 0.3);
    transform: translateY(-2px) !important;
}

.btn-outline-secondary {
    border: 2px solid var(--gray-color);
    color: var(--gray-color);
    background-color: transparent;
}

.btn-outline-secondary:hover {
    background-color: var(--gray-color);
    color: var(--white);
    border-color: var(--gray-color);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
    transform: translateY(-2px) !important;
}

.btn-outline-success {
    border: 2px solid var(--success-color);
    color: var(--success-color);
    background-color: transparent;
}

.btn-outline-success:hover {
    background-color: var(--success-color);
    color: var(--white);
    border-color: var(--success-color);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    transform: translateY(-2px) !important;
}

.btn-outline-warning {
    border: 2px solid var(--warning-color);
    color: #856404;
    background-color: transparent;
    font-weight: var(--font-weight-semibold);
}

.btn-outline-warning:hover {
    background-color: var(--warning-color);
    color: var(--dark-color);
    border-color: var(--warning-color);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
    transform: translateY(-2px) !important;
}

.btn-outline-danger {
    border: 2px solid var(--danger-color);
    color: var(--danger-color);
    background-color: transparent;
}

.btn-outline-danger:hover {
    background-color: var(--danger-color);
    color: var(--white);
    border-color: var(--danger-color);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
    transform: translateY(-2px) !important;
}

.btn-outline-info {
    border: 2px solid var(--info-color);
    color: var(--info-color);
    background-color: transparent;
}

.btn-outline-info:hover {
    background-color: var(--info-color);
    color: var(--white);
    border-color: var(--info-color);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
    transform: translateY(-2px) !important;
}

/* 按钮组增强样式 */
.btn-group .btn {
    border-radius: 0;
    margin-right: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: var(--border-radius-md);
    border-bottom-left-radius: var(--border-radius-md);
}

.btn-group .btn:last-child {
    border-top-right-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md);
}

.btn-group .btn:not(:first-child) {
    margin-left: -1px;
}

/* 图标按钮样式 */
.btn i {
    font-size: 0.875em;
}

.btn-icon-only {
    width: 40px;
    height: 40px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.btn-icon-only i {
    font-size: 1em;
}

/* 特殊按钮样式 */
.btn-gradient-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    border: none;
    box-shadow: 0 4px 15px rgba(255, 107, 0, 0.3);
}

.btn-gradient-primary:hover {
    background: linear-gradient(45deg, var(--primary-dark), var(--primary-color));
    box-shadow: 0 6px 20px rgba(255, 107, 0, 0.4);
    transform: translateY(-3px) !important;
}

.btn-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--dark-color);
}

.btn-glass:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px) !important;
}

/* 按钮尺寸增强 */
.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: var(--border-radius-sm);
}

.btn-xl {
    padding: 1rem 2rem;
    font-size: 1.25rem;
    border-radius: var(--border-radius-lg);
}

/* ========================================
   表单样式
======================================== */
.form-control, .form-select {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    transition: var(--transition-fast);
    background-color: var(--white);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 107, 0, 0.25);
    outline: none;
}

.form-control:hover, .form-select:hover {
    border-color: var(--primary-light);
}

.form-label {
    font-weight: var(--font-weight-medium);
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
}

.form-text {
    color: var(--gray-color);
    font-size: 0.75rem;
}

/* 输入组样式 */
.input-group {
    position: relative;
}

.input-group-text {
    background-color: var(--light-gray);
    border: 1px solid var(--border-color);
    color: var(--gray-color);
    padding: 0.5rem 0.75rem;
}

/* 表单验证样式 */
.is-valid {
    border-color: var(--success-color);
}

.is-invalid {
    border-color: var(--danger-color);
}

.valid-feedback {
    color: var(--success-color);
    font-size: 0.75rem;
}

.invalid-feedback {
    color: var(--danger-color);
    font-size: 0.75rem;
}

/* ========================================
   表格样式
======================================== */
.table {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table th {
    background-color: var(--light-gray);
    border-top: none;
    border-bottom: 1px solid var(--border-color);
    font-weight: var(--font-weight-semibold);
    color: var(--dark-color);
    padding: var(--spacing-md);
    font-size: 0.875rem;
}

.table td {
    padding: var(--spacing-md);
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
}

.table tbody tr {
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* 表格响应式 */
.table-responsive {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

/* ========================================
   徽章和标签样式
======================================== */
.badge {
    font-weight: var(--font-weight-medium);
    padding: 0.35em 0.65em;
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
}

.badge-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.badge-success {
    background-color: var(--success-color);
    color: var(--white);
}

.badge-warning {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.badge-danger {
    background-color: var(--danger-color);
    color: var(--white);
}

.badge-info {
    background-color: var(--info-color);
    color: var(--white);
}

.badge-light {
    background-color: var(--light-gray);
    color: var(--dark-color);
}

.badge-dark {
    background-color: var(--dark-color);
    color: var(--white);
}

/* ========================================
   进度条样式
======================================== */
.progress {
    height: 0.75rem;
    background-color: var(--light-gray);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-md);
}

.progress-bar {
    background-color: var(--primary-color);
    transition: width 0.6s ease;
    border-radius: var(--border-radius-sm);
}

.progress-bar-success {
    background-color: var(--success-color);
}

.progress-bar-warning {
    background-color: var(--warning-color);
}

.progress-bar-danger {
    background-color: var(--danger-color);
}

.progress-bar-info {
    background-color: var(--info-color);
}

/* ========================================
   警告框样式
======================================== */
.alert {
    border: none;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border-left: 4px solid;
}

.alert-primary {
    background-color: rgba(255, 107, 0, 0.1);
    border-left-color: var(--primary-color);
    color: var(--primary-dark);
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-left-color: var(--success-color);
    color: #155724;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-left-color: var(--warning-color);
    color: #856404;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-left-color: var(--danger-color);
    color: #721c24;
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    border-left-color: var(--info-color);
    color: #0c5460;
}

/* ========================================
   模态框样式
======================================== */
.modal-content {
    border: none;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-lg);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-lg);
}

/* ========================================
   工具类样式
======================================== */
/* 间距工具类 */
.m-0 { margin: 0 !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mt-4 { margin-top: var(--spacing-lg) !important; }
.mt-5 { margin-top: var(--spacing-xl) !important; }

.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }
.mb-5 { margin-bottom: var(--spacing-xl) !important; }

.p-0 { padding: 0 !important; }
.pt-1 { padding-top: var(--spacing-xs) !important; }
.pt-2 { padding-top: var(--spacing-sm) !important; }
.pt-3 { padding-top: var(--spacing-md) !important; }
.pt-4 { padding-top: var(--spacing-lg) !important; }
.pt-5 { padding-top: var(--spacing-xl) !important; }

.pb-1 { padding-bottom: var(--spacing-xs) !important; }
.pb-2 { padding-bottom: var(--spacing-sm) !important; }
.pb-3 { padding-bottom: var(--spacing-md) !important; }
.pb-4 { padding-bottom: var(--spacing-lg) !important; }
.pb-5 { padding-bottom: var(--spacing-xl) !important; }

/* 文本工具类 */
.text-primary { color: var(--primary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-muted { color: var(--gray-color) !important; }

.fw-normal { font-weight: var(--font-weight-normal) !important; }
.fw-medium { font-weight: var(--font-weight-medium) !important; }
.fw-semibold { font-weight: var(--font-weight-semibold) !important; }

/* 背景工具类 */
.bg-primary { background-color: var(--primary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-info { background-color: var(--info-color) !important; }
.bg-light { background-color: var(--light-gray) !important; }

/* 边框工具类 */
.border-primary { border-color: var(--primary-color) !important; }
.border-success { border-color: var(--success-color) !important; }
.border-warning { border-color: var(--warning-color) !important; }
.border-danger { border-color: var(--danger-color) !important; }
.border-info { border-color: var(--info-color) !important; }

/* 阴影工具类 */
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }

/* 圆角工具类 */
.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded { border-radius: var(--border-radius-md) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-xl { border-radius: var(--border-radius-xl) !important; }

/* ========================================
   特殊组件样式
======================================== */
/* 页面标题区域 */
.page-header {
    background: linear-gradient(135deg, var(--white) 0%, #fafbfc 100%);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    /* 禁用整个 page-header 区域的变换效果 */
    -webkit-transform: none !important;
    -moz-transform: none !important;
    -ms-transform: none !important;
    -o-transform: none !important;
    transform: none !important;
}

/* 页面头部按钮样式 - 禁用变换效果 */
.page-header .btn,
.page-header button,
.page-header .btn-group .btn,
.page-header .btn-group button {
    transform: none !important;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
    touch-action: manipulation !important;
}

.page-header .btn:hover,
.page-header button:hover,
.page-header .btn-group .btn:hover,
.page-header .btn-group button:hover,
.page-header .btn:active,
.page-header button:active,
.page-header .btn-group .btn:active,
.page-header .btn-group button:active,
.page-header .btn:focus,
.page-header button:focus,
.page-header .btn-group .btn:focus,
.page-header .btn-group button:focus {
    transform: none !important;
}



.page-title {
    font-size: 1.75rem;
    font-weight: var(--font-weight-semibold);
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
}

.page-subtitle {
    color: var(--gray-color);
    font-size: 0.875rem;
    margin-bottom: 0;
}

/* 统计卡片 */
.stats-card {
    background: linear-gradient(135deg, var(--white) 0%, #fafbfc 100%);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    transition: var(--transition-normal);
    border: 1px solid transparent;
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: var(--font-weight-semibold);
    line-height: 1;
    margin-bottom: var(--spacing-sm);
}

.stats-label {
    color: var(--gray-color);
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
}

/* 紧凑型统计卡片 - 一行显示，内容居中 */
.stats-card-compact {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 1.25rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-align: center;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.stats-card-compact:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stats-card-compact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.stats-card-compact .stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.75rem auto;
    font-size: 1.25rem;
    color: var(--white);
}

.stats-card-compact .stats-content {
    width: 100%;
}

.stats-card-compact .stats-value {
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
    color: var(--dark-color);
    margin-bottom: 0.25rem;
    line-height: 1;
}

.stats-card-compact .stats-label {
    font-size: 0.875rem;
    color: var(--gray-color);
    font-weight: var(--font-weight-medium);
    margin-bottom: 0;
}

/* 统计图标样式 */
.stats-icon-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.stats-icon-success {
    background: linear-gradient(135deg, var(--success-color), #20c997);
}

.stats-icon-warning {
    background: linear-gradient(135deg, var(--warning-color), #ffeb3b);
}

.stats-icon-info {
    background: linear-gradient(135deg, var(--info-color), #20c997);
}

/* 面包屑导航 */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: var(--spacing-md);
}

.breadcrumb-item {
    font-size: 0.875rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: var(--gray-color);
}

.breadcrumb-item.active {
    color: var(--gray-color);
}

/* 标签页 */
.nav-tabs {
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--spacing-lg);
}

.nav-tabs .nav-link {
    border: none;
    border-bottom: 2px solid transparent;
    color: var(--gray-color);
    font-weight: var(--font-weight-medium);
    padding: var(--spacing-md) var(--spacing-lg);
    transition: var(--transition-fast);
}

.nav-tabs .nav-link:hover {
    border-bottom-color: var(--primary-light);
    color: var(--primary-color);
}

.nav-tabs .nav-link.active {
    border-bottom-color: var(--primary-color);
    color: var(--primary-color);
    background-color: transparent;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}



/* 空状态 */
.empty-state {
    text-align: center;
    padding: var(--spacing-xxl);
    color: var(--gray-color);
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.empty-state-text {
    font-size: 1.125rem;
    margin-bottom: var(--spacing-md);
}

/* ========================================
   响应式设计
======================================== */

/* 超大屏幕 (≥1400px) */
@media (min-width: 1400px) {
    .container-fluid {
        max-width: 1320px;
        margin: 0 auto;
    }

    .stats-grid {
        grid-template-columns: repeat(6, 1fr);
    }
}

/* 大屏幕 (≥1200px) */
@media (max-width: 1199.98px) {
    .container-fluid {
        padding-left: var(--spacing-md);
        padding-right: var(--spacing-md);
    }

    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* 中等屏幕 (≥992px) */
@media (max-width: 991.98px) {
    .page-header {
        padding: var(--spacing-lg);
    }

    .page-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
        gap: var(--spacing-md);
    }

    .page-header .text-end {
        text-align: left !important;
        width: 100%;
    }

    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .stats-value {
        font-size: 2rem;
    }

    .card-body {
        padding: var(--spacing-md);
    }

    .navbar-nav {
        text-align: center;
    }

    .navbar-nav .nav-link {
        padding: var(--spacing-sm) var(--spacing-md);
    }
}

/* 小屏幕 (≥768px) */
@media (max-width: 767.98px) {
    .page-title {
        font-size: 1.5rem;
    }

    .page-header {
        padding: var(--spacing-md);
        text-align: center;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }

    .stats-card-enhanced {
        padding: var(--spacing-md);
    }

    .stats-card-compact {
        padding: 1rem;
        min-height: 100px;
    }

    .stats-card-compact .stats-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    .stats-card-compact .stats-value {
        font-size: 1.75rem;
    }

    .stats-value {
        font-size: 1.75rem;
    }

    .filter-row {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .filter-group {
        min-width: auto;
    }

    .filter-actions {
        flex-direction: column;
        width: 100%;
    }

    .filter-actions .btn {
        width: 100%;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        width: 100%;
        margin-bottom: var(--spacing-sm);
        border-radius: var(--border-radius-md) !important;
    }

    .table-responsive {
        font-size: 0.875rem;
        border-radius: var(--border-radius-md);
    }

    .table th,
    .table td {
        padding: var(--spacing-sm);
    }

    .modal-dialog {
        margin: var(--spacing-md);
    }

    .navbar-collapse {
        margin-top: var(--spacing-md);
    }

    .dropdown-menu {
        position: static !important;
        transform: none !important;
        width: 100%;
        margin-top: var(--spacing-sm);
        box-shadow: none;
        border: 1px solid var(--border-color);
    }
}

/* 超小屏幕 (≤576px) */
@media (max-width: 575.98px) {
    .container-fluid {
        padding-left: var(--spacing-sm);
        padding-right: var(--spacing-sm);
    }

    .page-header {
        padding: var(--spacing-sm);
    }

    .page-title {
        font-size: 1.25rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stats-card-enhanced {
        padding: var(--spacing-sm);
        text-align: center;
    }

    .stats-card-compact {
        padding: 0.75rem;
        min-height: 90px;
    }

    .stats-card-compact .stats-icon {
        width: 35px;
        height: 35px;
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }

    .stats-card-compact .stats-value {
        font-size: 1.5rem;
    }

    .stats-card-compact .stats-label {
        font-size: 0.75rem;
    }

    .stats-value {
        font-size: 1.5rem;
    }

    .card-body {
        padding: var(--spacing-sm);
    }

    .btn {
        font-size: 0.875rem;
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .form-control,
    .form-select {
        font-size: 0.875rem;
    }

    .table {
        font-size: 0.75rem;
    }

    .table th,
    .table td {
        padding: 0.25rem;
    }

    .navbar-brand {
        font-size: 1rem;
    }

    .nav-link {
        font-size: 0.875rem;
    }

    /* 隐藏一些不重要的列 */
    .table .d-none-mobile {
        display: none !important;
    }

    /* 简化操作按钮 */
    .action-buttons {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .action-btn {
        width: 100%;
        height: auto;
        padding: var(--spacing-xs);
    }
}

/* 横屏手机优化 */
@media (max-height: 500px) and (orientation: landscape) {
    .page-header {
        padding: var(--spacing-sm);
    }

    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .navbar {
        padding: var(--spacing-xs) 0;
    }

    .navbar-brand {
        font-size: 0.875rem;
    }

    .nav-link {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.75rem;
    }
}

/* 打印优化 */
@media print {
    .navbar,
    .btn,
    .modal,
    .alert,
    .filter-panel,
    .action-buttons {
        display: none !important;
    }

    .page-header {
        border-bottom: 2px solid var(--dark-color);
        margin-bottom: var(--spacing-lg);
    }

    .card {
        box-shadow: none !important;
        border: 1px solid var(--border-color) !important;
        break-inside: avoid;
    }

    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    body {
        background-color: var(--white) !important;
        font-size: 12pt;
        line-height: 1.4;
    }

    .table {
        font-size: 10pt;
    }

    .page-title {
        font-size: 18pt;
        color: var(--dark-color) !important;
    }
}

/* ========================================
   打印样式
======================================== */
@media print {
    .navbar,
    .btn,
    .modal,
    .alert {
        display: none !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid var(--border-color) !important;
    }

    body {
        background-color: var(--white) !important;
    }
}

/* ========================================
   动画效果
======================================== */
/* 按钮波纹效果 */
.btn {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* 页面加载动画 */
body {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

body.loaded {
    opacity: 1;
}

/* 卡片悬停动画 */
.card,
.stats-card-enhanced {
    transition: var(--transition-normal);
}

.card:hover,
.stats-card-enhanced:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

/* 表格行悬停效果 */
.table tbody tr {
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background-color: rgba(255, 107, 0, 0.05);
    /* 移除了 transform: scale 效果，避免影响表格中的按钮 */
}

.table tbody tr.table-active {
    background-color: rgba(255, 107, 0, 0.1);
    border-left: 4px solid var(--primary-color);
}

/* 输入框焦点动画 */
.form-control,
.form-select {
    transition: var(--transition-normal);
}

.form-control:focus,
.form-select:focus {
    /* 移除了 transform: scale 效果，避免影响按钮 */
}

.focused {
    /* 移除了 transform: scale 效果 */
}

/* 加载动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

/* 淡入动画 */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 滑入动画 */
.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* 脉冲动画 */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* 摇摆动画 */
.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 通知动画 */
.notification-enter {
    animation: notificationEnter 0.3s ease-out;
}

@keyframes notificationEnter {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ========================================
   深色模式支持（预留）
======================================== */
@media (prefers-color-scheme: dark) {
    /* 深色模式样式可以在这里添加 */
}