"""
线索权限控制优化模块
提供细粒度的权限控制和权限检查功能
"""

from functools import wraps
from flask import abort, current_app
from flask_login import current_user
from models import Lead, User, LeadPool
import logging

logger = logging.getLogger(__name__)

class LeadPermissionManager:
    """线索权限管理器"""
    
    # 权限级别定义
    PERMISSION_LEVELS = {
        'READ': 1,
        'EDIT': 2,
        'DELETE': 3,
        'ASSIGN': 4,
        'ADMIN': 5
    }
    
    # 角色权限映射
    ROLE_PERMISSIONS = {
        'super_admin': ['READ', 'EDIT', 'DELETE', 'ASSIGN', 'ADMIN'],
        'company_admin': ['READ', 'EDIT', 'DELETE', 'ASSIGN'],
        'department_admin': ['READ', 'EDIT', 'ASSIGN'],
        'sales': ['READ', 'EDIT']
    }
    
    @classmethod
    def has_permission(cls, user, lead, permission_type):
        """检查用户是否有指定权限"""
        try:
            logger.info(f"权限检查: user_id={user.id}, user_role={user.role.code}, lead_id={lead.id}, permission={permission_type}")

            # 超级管理员拥有所有权限
            if user.role.code == 'super_admin':
                logger.info("超级管理员权限通过")
                return True

            # 检查角色基础权限
            role_permissions = cls.ROLE_PERMISSIONS.get(user.role.code, [])
            logger.info(f"角色 {user.role.code} 的权限列表: {role_permissions}")

            if permission_type not in role_permissions:
                logger.info(f"权限 {permission_type} 不在角色权限列表中")
                return False

            # 具体权限检查
            if permission_type == 'READ':
                result = cls._can_read(user, lead)
                logger.info(f"READ权限检查结果: {result}")
                return result
            elif permission_type == 'EDIT':
                result = cls._can_edit(user, lead)
                logger.info(f"EDIT权限检查结果: {result}")
                return result
            elif permission_type == 'DELETE':
                result = cls._can_delete(user, lead)
                logger.info(f"DELETE权限检查结果: {result}")
                return result
            elif permission_type == 'ASSIGN':
                result = cls._can_assign(user, lead)
                logger.info(f"ASSIGN权限检查结果: {result}")
                return result
            elif permission_type == 'ADMIN':
                result = cls._can_admin(user, lead)
                logger.info(f"ADMIN权限检查结果: {result}")
                return result

            logger.info(f"未知权限类型: {permission_type}")
            return False

        except Exception as e:
            logger.error(f"权限检查失败: {e}", exc_info=True)
            return False
    
    @classmethod
    def _can_read(cls, user, lead):
        """检查读取权限"""
        # 线索负责人可以查看
        if lead.owner_id == user.id:
            return True
        
        # 公司管理员可以查看本公司线索
        if user.role.code == 'company_admin' and lead.company_id == user.company_id:
            return True
        
        # 部门管理员可以查看本部门线索
        if user.role.code == 'department_admin':
            if lead.owner_id:
                lead_owner = User.query.get(lead.owner_id)
                if lead_owner and lead_owner.department_id == user.department_id:
                    return True
        
        # 异地线索的特殊权限
        if cls._is_cross_location_lead(lead):
            # 原始公司的员工可以查看
            if lead.company_id == user.company_id:
                return True
            # 当前处理公司的员工可以查看
            if lead.current_processing_company_id == user.company_id:
                return True
        
        # 公海线索的查看权限
        if lead.is_in_public_sea and lead.company_id == user.company_id:
            return True
        
        return False
    
    @classmethod
    def _can_edit(cls, user, lead):
        """检查编辑权限"""
        # 线索负责人可以编辑
        if lead.owner_id == user.id:
            # 检查是否为异地线索的特殊限制
            if cls._is_cross_location_lead(lead):
                # B公司员工编辑异地线索有字段限制
                if lead.current_processing_company_id == user.company_id and lead.company_id != user.company_id:
                    return 'limited'  # 受限编辑
            return True
        
        # 管理员权限
        if user.role.code in ['company_admin', 'department_admin']:
            if cls._can_read(user, lead):
                # A公司员工不能编辑已推送的异地线索
                if cls._is_cross_location_lead(lead) and lead.company_id == user.company_id and lead.current_processing_company_id != user.company_id:
                    return False
                return True
        
        return False
    
    @classmethod
    def _can_delete(cls, user, lead):
        """检查删除权限"""
        # 只有管理员可以删除
        if user.role.code not in ['super_admin', 'company_admin', 'department_admin']:
            return False
        
        # 异地线索的删除限制
        if cls._is_cross_location_lead(lead):
            # B公司不能删除异地线索
            if lead.current_processing_company_id == user.company_id and lead.company_id != user.company_id:
                return False
        
        return cls._can_read(user, lead)
    
    @classmethod
    def _can_assign(cls, user, lead):
        """检查分配权限"""
        # 只有管理员可以分配
        if user.role.code not in ['super_admin', 'company_admin', 'department_admin']:
            return False
        
        return cls._can_read(user, lead)
    
    @classmethod
    def _can_admin(cls, user, lead):
        """检查管理权限"""
        return user.role.code in ['super_admin', 'company_admin']
    
    @classmethod
    def _is_cross_location_lead(cls, lead):
        """判断是否为异地线索"""
        return (lead.current_processing_company_id is not None and 
                lead.current_processing_company_id != lead.company_id)
    
    @classmethod
    def get_editable_fields(cls, user, lead):
        """获取用户可编辑的字段列表"""
        if not cls.has_permission(user, lead, 'EDIT'):
            return []
        
        # 基础可编辑字段
        base_fields = ['name', 'company_name', 'email', 'phone', 'notes']
        status_fields = [
            'is_called', 'is_connected', 'is_valid_call', 'is_wechat_added',
            'is_intentional', 'is_compliant', 'is_visited', 'is_car_selected', 'is_deal_done'
        ]
        
        # 异地线索的字段限制
        if cls._is_cross_location_lead(lead):
            if lead.current_processing_company_id == user.company_id and lead.company_id != user.company_id:
                # B公司员工只能编辑状态和备注
                return status_fields + ['notes']
        
        # 管理员可以编辑所有字段
        if user.role.code in ['super_admin', 'company_admin', 'department_admin']:
            return base_fields + status_fields + ['owner_id']
        
        # 普通用户可以编辑基础字段和状态
        return base_fields + status_fields

class LeadPoolPermissionManager:
    """线索池权限管理器"""
    
    @classmethod
    def can_access_pool(cls, user, pool):
        """检查是否可以访问线索池"""
        # 超级管理员可以访问所有池
        if user.role.code == 'super_admin':
            return True
        
        # 公司管理员可以访问本公司的池
        if user.role.code == 'company_admin' and pool.company_id == user.company_id:
            return True
        
        # 异地到店池的特殊访问权限
        if pool.pool_type == 'CROSS_LOCATION_PENDING' and pool.company_id == user.company_id:
            return True
        
        # 公海池的访问权限
        if pool.is_public_sea and pool.company_id == user.company_id:
            return True
        
        return False
    
    @classmethod
    def can_manage_pool(cls, user, pool):
        """检查是否可以管理线索池"""
        if user.role.code == 'super_admin':
            return True
        
        if user.role.code == 'company_admin' and pool.company_id == user.company_id:
            return True
        
        return False

def require_lead_permission(permission_type):
    """装饰器：要求特定的线索权限"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 从参数中获取lead_id
            lead_id = kwargs.get('lead_id') or (args[0] if args else None)
            
            if not lead_id:
                abort(400, "缺少线索ID")
            
            lead = Lead.query.get_or_404(lead_id)
            
            if not LeadPermissionManager.has_permission(current_user, lead, permission_type):
                abort(403, "权限不足")
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_pool_permission(permission_type='access'):
    """装饰器：要求特定的线索池权限"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            pool_id = kwargs.get('pool_id') or (args[0] if args else None)
            
            if not pool_id:
                abort(400, "缺少线索池ID")
            
            pool = LeadPool.query.get_or_404(pool_id)
            
            if permission_type == 'access':
                has_permission = LeadPoolPermissionManager.can_access_pool(current_user, pool)
            elif permission_type == 'manage':
                has_permission = LeadPoolPermissionManager.can_manage_pool(current_user, pool)
            else:
                has_permission = False
            
            if not has_permission:
                abort(403, "权限不足")
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# 权限检查函数（向后兼容）
def has_permission_to_edit(lead, user):
    """检查编辑权限（向后兼容）"""
    return LeadPermissionManager.has_permission(user, lead, 'EDIT')

def has_permission_to_delete(lead, user):
    """检查删除权限（向后兼容）"""
    return LeadPermissionManager.has_permission(user, lead, 'DELETE')

def has_permission_to_assign(lead, user):
    """检查分配权限（向后兼容）"""
    return LeadPermissionManager.has_permission(user, lead, 'ASSIGN')

def has_permission_to_release(lead, user):
    """检查释放权限"""
    # 线索负责人可以释放
    if lead.owner_id == user.id:
        return True
    
    # 管理员可以释放本公司/部门的线索
    if user.role.code in ['super_admin', 'company_admin', 'department_admin']:
        return LeadPermissionManager.has_permission(user, lead, 'ASSIGN')
    
    return False

# 全局权限管理器实例
lead_permission_manager = LeadPermissionManager()
pool_permission_manager = LeadPoolPermissionManager()
