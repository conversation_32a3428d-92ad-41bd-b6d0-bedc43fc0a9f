#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的用户数据和登录逻辑
"""

import os
import sys
from werkzeug.security import check_password_hash

def check_database():
    """检查数据库中的用户数据"""
    try:
        # 确保在正确的目录
        if not os.path.isfile('app.py'):
            print("❌ 错误: 请在项目根目录下运行此脚本！")
            return False

        # 导入应用和模型
        from app import create_app
        from config import Config
        from models import db, User, Role
        
        app = create_app(Config)
        
        with app.app_context():
            print("🔍 CRM系统数据库检查")
            print("="*60)
            
            # 1. 检查数据库连接
            try:
                user_count = User.query.count()
                role_count = Role.query.count()
                print(f"✅ 数据库连接正常")
                print(f"   用户总数: {user_count}")
                print(f"   角色总数: {role_count}")
            except Exception as e:
                print(f"❌ 数据库连接失败: {e}")
                return False
            
            print("\n" + "="*60)
            print("📋 所有用户列表:")
            print("="*60)
            
            # 2. 列出所有用户
            users = User.query.all()
            for user in users:
                print(f"ID: {user.id}")
                print(f"   用户名: {user.username}")
                print(f"   邮箱: {user.email}")
                print(f"   姓名: {user.name}")
                print(f"   状态: {user.status}")
                print(f"   角色: {user.role.name if user.role else '无角色'}")
                print(f"   角色代码: {user.role.code if user.role else '无'}")
                print(f"   密码哈希: {user.password_hash[:50]}..." if user.password_hash else "   密码哈希: 无")
                print(f"   公司ID: {user.company_id}")
                print(f"   部门ID: {user.department_id}")
                print("-" * 40)
            
            print("\n" + "="*60)
            print("🔑 角色列表:")
            print("="*60)
            
            # 3. 列出所有角色
            roles = Role.query.all()
            for role in roles:
                print(f"ID: {role.id}")
                print(f"   名称: {role.name}")
                print(f"   代码: {role.code}")
                print(f"   描述: {role.description}")
                print(f"   系统角色: {role.is_system}")
                print("-" * 40)
            
            print("\n" + "="*60)
            print("🧪 密码验证测试:")
            print("="*60)
            
            # 4. 测试admin用户的密码
            admin_user = User.query.filter_by(username='admin').first()
            if admin_user:
                print(f"找到admin用户: {admin_user.username}")
                print(f"状态: {admin_user.status}")
                print(f"角色: {admin_user.role.name if admin_user.role else '无角色'}")
                
                # 测试不同密码
                test_passwords = ['admin123', 'admin', '123456', 'password']
                for pwd in test_passwords:
                    try:
                        result = admin_user.check_password(pwd)
                        print(f"   密码 '{pwd}': {'✅ 正确' if result else '❌ 错误'}")
                    except Exception as e:
                        print(f"   密码 '{pwd}': ❌ 验证失败 - {e}")
            else:
                print("❌ 未找到admin用户")
            
            print("\n" + "="*60)
            print("🔍 登录逻辑测试:")
            print("="*60)
            
            # 5. 模拟登录逻辑
            test_username = 'admin'
            test_password = 'admin123'
            
            print(f"测试登录: {test_username} / {test_password}")
            
            # 查找用户
            user = User.query.filter_by(username=test_username).first()
            if user:
                print(f"✅ 找到用户: {user.username}")
                print(f"   用户状态: {user.status}")
                print(f"   is_active: {user.is_active}")
                
                # 检查密码
                if user.check_password(test_password):
                    print("✅ 密码验证通过")
                    
                    # 检查状态
                    if user.status == 'active':
                        print("✅ 用户状态正常")
                        print("🎉 登录应该成功！")
                    else:
                        print(f"❌ 用户状态异常: {user.status}")
                else:
                    print("❌ 密码验证失败")
            else:
                print(f"❌ 未找到用户: {test_username}")
            
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    check_database()
