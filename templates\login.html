<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM系统 - 登录</title>

    <!-- 应用系统主题 -->
    <script>
        (function() {
            const systemTheme = '{{ system_theme }}' || 'orange';
            document.documentElement.setAttribute('data-theme', systemTheme);
        })();
    </script>

    <style>
        /* 主题颜色变量 */
        :root {
            --login-primary: #ff6b00;
            --login-primary-light: #ff8533;
            --login-primary-dark: #cc5500;
            --login-primary-rgba: 255, 107, 0;
        }

        /* 橙色主题 */
        [data-theme="orange"] {
            --login-primary: #ff6b00;
            --login-primary-light: #ff8533;
            --login-primary-dark: #cc5500;
            --login-primary-rgba: 255, 107, 0;
        }

        /* 蓝色主题 */
        [data-theme="blue"] {
            --login-primary: #0d6efd;
            --login-primary-light: #3d8bfd;
            --login-primary-dark: #0a58ca;
            --login-primary-rgba: 13, 110, 253;
        }

        /* 绿色主题 */
        [data-theme="green"] {
            --login-primary: #198754;
            --login-primary-light: #20c997;
            --login-primary-dark: #146c43;
            --login-primary-rgba: 25, 135, 84;
        }

        /* 紫色主题 */
        [data-theme="purple"] {
            --login-primary: #6f42c1;
            --login-primary-light: #8a63d2;
            --login-primary-dark: #59359a;
            --login-primary-rgba: 111, 66, 193;
        }

        /* 深色主题 */
        [data-theme="dark"] {
            --login-primary: #212529;
            --login-primary-light: #495057;
            --login-primary-dark: #000000;
            --login-primary-rgba: 33, 37, 41;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--login-primary) 0%, var(--login-primary-light) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s ease;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
            width: 100%;
            max-width: 400px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h1 {
            color: #343a40;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .login-header p {
            color: #6c757d;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #343a40;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #dee2e6;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--login-primary);
            box-shadow: 0 0 0 0.2rem rgba(var(--login-primary-rgba), 0.25);
        }

        .form-group input:hover {
            border-color: var(--login-primary-light);
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, var(--login-primary) 0%, var(--login-primary-light) 100%);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(var(--login-primary-rgba), 0.3);
        }

        .login-btn:hover {
            background: linear-gradient(135deg, var(--login-primary-dark) 0%, var(--login-primary) 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(var(--login-primary-rgba), 0.4);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .error-message {
            background: rgba(220, 53, 69, 0.1);
            color: #721c24;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid rgba(220, 53, 69, 0.2);
            border-left: 4px solid #dc3545;
        }

        .success-message {
            background: rgba(40, 167, 69, 0.1);
            color: #155724;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid rgba(40, 167, 69, 0.2);
            border-left: 4px solid #28a745;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 10px;
            color: #6c757d;
            font-size: 14px;
        }

        .loading.show {
            display: block;
        }



        /* 添加一些微妙的动画效果 */
        .login-container {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 主题切换动画 */
        html {
            transition: all 0.3s ease;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .login-container {
                margin: 20px;
                padding: 30px;
            }
        }
    </style>
</head>
<body>


    <div class="login-container">
        <div class="login-header">
            <h1>CRM系统</h1>
            <p>请登录您的账户</p>
        </div>

        <!-- 显示错误消息 -->
        {% if error_message %}
        <div class="error-message">
            {{ error_message }}
        </div>
        {% endif %}

        <!-- 显示成功消息 -->
        {% if success_message %}
        <div class="success-message">
            {{ success_message }}
        </div>
        {% endif %}

        <form method="POST" action="{{ url_for('login') }}" id="loginForm" data-skip-validation="true">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required
                       value="{{ request.form.username if request.form.username else '' }}">
            </div>

            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit" class="login-btn" id="loginBtn">登录</button>

            <div class="loading" id="loading">
                <p>正在登录...</p>
            </div>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('loginForm');
            const btn = document.getElementById('loginBtn');
            const loading = document.getElementById('loading');

            form.addEventListener('submit', function(e) {
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value.trim();

                if (!username || !password) {
                    e.preventDefault();
                    alert('请输入用户名和密码');
                    return false;
                }

                // 显示加载状态
                btn.disabled = true;
                btn.textContent = '登录中...';
                loading.classList.add('show');

                // 5秒后如果还没响应，恢复按钮
                setTimeout(function() {
                    if (btn.disabled) {
                        btn.disabled = false;
                        btn.textContent = '登录';
                        loading.classList.remove('show');
                    }
                }, 5000);
            });
        });
    </script>
</body>
</html>

