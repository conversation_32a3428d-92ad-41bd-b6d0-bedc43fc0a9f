from flask import Blueprint, render_template, redirect, url_for, flash
from flask_login import login_required, current_user
from models import db, Company, LeadPool, User
from forms import CompanyForm
from datetime import datetime

# 创建蓝图
company_bp = Blueprint('company', __name__)

@company_bp.route('/companies')
@login_required
def list_companies():
    """显示公司列表"""
    # 检查权限：只有超级管理员可以查看所有公司
    if not current_user.has_permission('view_all_companies'):
        flash('您没有权限查看所有公司', 'danger')
        return redirect(url_for('index'))
    
    companies = Company.query.all()
    return render_template('organization/companies.html', companies=companies)

@company_bp.route('/companies/add', methods=['GET', 'POST'])
@login_required
def add_company():
    """添加新公司"""
    # 检查权限：只有超级管理员可以添加公司
    if not current_user.has_permission('manage_companies'):
        flash('您没有权限添加公司', 'danger')
        return redirect(url_for('index'))

    form = CompanyForm()
    # 设置上级公司选项
    headquarters = Company.query.filter_by(type='headquarters').all()
    form.parent_id.choices = [(0, '无')] + [(c.id, c.name) for c in headquarters]

    if form.validate_on_submit():
        # 检查编码是否已存在
        existing_company = Company.query.filter_by(code=form.code.data).first()
        if existing_company:
            flash('公司编码已存在', 'danger')
            return render_template('organization/company_form.html', form=form)

        # 创建新公司
        new_company = Company(
            name=form.name.data,
            code=form.code.data,
            type=form.type.data,
            parent_id=form.parent_id.data if form.parent_id.data != 0 else None,
            is_default=form.is_default.data
        )

        # 如果设置为默认公司，需要将其他公司的默认标志取消
        if form.is_default.data:
            Company.query.filter_by(is_default=True).update({'is_default': False})

        db.session.add(new_company)
        db.session.flush()  # 获取ID

        # 创建公司默认线索池
        company_pool = LeadPool(
            name=f'{new_company.name}线索池',
            description=f'{new_company.name}的默认线索池',
            company_id=new_company.id,
            is_public_sea=False
        )
        db.session.add(company_pool)

        db.session.commit()
        flash('公司创建成功', 'success')
        return redirect(url_for('organization.companies'))

    return render_template('organization/company_form.html', form=form)

@company_bp.route('/companies/<int:company_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_company(company_id):
    """编辑公司信息"""
    # 检查权限：只有超级管理员可以编辑公司
    if not current_user.has_permission('manage_companies'):
        flash('您没有权限编辑公司', 'danger')
        return redirect(url_for('index'))

    company = Company.query.get_or_404(company_id)

    # 创建表单并预填充数据
    form = CompanyForm(obj=company)

    # 设置上级公司选项（排除自己）
    headquarters = Company.query.filter(Company.id!=company_id, Company.type=='headquarters').all()
    form.parent_id.choices = [(0, '无')] + [(c.id, c.name) for c in headquarters]

    if form.validate_on_submit():
        # 检查编码是否已被其他公司使用
        existing_company = Company.query.filter(Company.code==form.code.data, Company.id!=company_id).first()
        if existing_company:
            flash('公司编码已被其他公司使用', 'danger')
            return render_template('organization/company_form.html', form=form, company=company)

        # 更新公司信息
        company.name = form.name.data
        company.code = form.code.data
        company.type = form.type.data
        company.parent_id = form.parent_id.data if form.parent_id.data != 0 else None
        company.is_default = form.is_default.data
        company.updated_at = datetime.utcnow()

        # 如果设置为默认公司，需要将其他公司的默认标志取消
        if form.is_default.data:
            Company.query.filter(Company.id != company_id, Company.is_default == True).update({'is_default': False})

        db.session.commit()
        flash('公司信息更新成功', 'success')
        return redirect(url_for('organization.companies'))

    return render_template('organization/company_form.html', form=form, company=company)

@company_bp.route('/companies/<int:company_id>/delete', methods=['POST'])
@login_required
def delete_company(company_id):
    """删除公司"""
    # 检查权限：只有超级管理员可以删除公司
    if not current_user.has_permission('manage_companies'):
        flash('您没有权限删除公司', 'danger')
        return redirect(url_for('index'))
    
    company = Company.query.get_or_404(company_id)
    
    # 检查是否为默认公司
    if company.is_default:
        flash('不能删除默认公司', 'danger')
        return redirect(url_for('company.list_companies'))
    
    # 检查是否有子公司
    if len(company.subsidiaries) > 0:
        flash('该公司有子公司，不能删除', 'danger')
        return redirect(url_for('company.list_companies'))
    
    # 检查是否有用户
    if User.query.filter_by(company_id=company_id).count() > 0:
        flash('该公司有用户，不能删除', 'danger')
        return redirect(url_for('company.list_companies'))
    
    # 删除公司的线索池
    LeadPool.query.filter_by(company_id=company_id).delete()
    
    # 删除公司
    db.session.delete(company)
    db.session.commit()
    
    flash('公司删除成功', 'success')
    return redirect(url_for('company.list_companies')) 