#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试导入功能的脚本
"""

import os
import sys
import csv
import io
from datetime import datetime

def debug_import():
    """调试导入功能"""
    try:
        # 确保在正确的目录
        if not os.path.isfile('app.py'):
            print("❌ 错误: 请在项目根目录下运行此脚本！")
            return False

        # 导入应用和模型
        from app import create_app
        from config import Config
        from models import db, Lead, User, Company
        from utils.lead_utils import _process_lead_import_data
        
        app = create_app(Config)
        
        with app.app_context():
            print("🔍 调试导入功能")
            print("="*60)
            
            # 1. 检查基础数据
            companies = Company.query.all()
            users = User.query.filter_by(status='active').all()
            
            test_company = companies[0]
            test_user = users[0]
            
            print(f"✅ 使用测试公司: {test_company.name}")
            print(f"✅ 使用测试用户: {test_user.name}")
            
            # 2. 检查现有线索
            existing_leads = Lead.query.filter_by(company_id=test_company.id).all()
            print(f"✅ 现有线索数量: {len(existing_leads)}")
            
            # 显示前5个现有线索的电话号码
            print("   现有线索电话号码示例:")
            for i, lead in enumerate(existing_leads[:5]):
                print(f"     {i+1}. {lead.name}: {lead.phone}")
            
            # 3. 创建测试CSV数据（使用全新的电话号码）
            print("\n🔧 创建测试CSV数据...")
            timestamp = int(datetime.now().timestamp())
            test_data = [
                ['姓名', '电话', '省份', '城市', '备注'],
                [f'调试客户1', f'1{timestamp:010d}1', '广东省', '深圳市', '调试导入1'],
                [f'调试客户2', f'1{timestamp:010d}2', '北京市', '北京市', '调试导入2'],
            ]
            
            # 创建CSV字符串
            csv_content = io.StringIO()
            writer = csv.writer(csv_content)
            for row in test_data:
                writer.writerow(row)
            csv_content.seek(0)
            
            print(f"   创建了包含 {len(test_data)-1} 行数据的测试CSV")
            for i, row in enumerate(test_data[1:], 1):
                print(f"     {i}. {row[0]}: {row[1]}")
            
            # 4. 测试导入功能
            print("\n🧪 测试导入处理...")
            
            # 记录导入前的线索数量
            before_count = Lead.query.filter_by(company_id=test_company.id).count()
            print(f"   导入前线索数量: {before_count}")
            
            # 执行导入
            success_count, error_count, error_messages = _process_lead_import_data(
                csv_content, 
                test_company.id, 
                test_user.id
            )
            
            # 检查结果
            after_count = Lead.query.filter_by(company_id=test_company.id).count()
            print(f"   导入后线索数量: {after_count}")
            print(f"   成功导入: {success_count}")
            print(f"   失败数量: {error_count}")
            
            if error_messages:
                print("   错误信息:")
                for msg in error_messages:
                    print(f"     - {msg}")
            
            # 5. 验证导入结果
            print("\n✅ 验证导入结果...")
            
            # 检查成功导入的线索
            imported_leads = Lead.query.filter(
                Lead.company_id == test_company.id,
                Lead.name.like('调试客户%')
            ).all()
            
            print(f"   找到 {len(imported_leads)} 条导入的调试线索:")
            for lead in imported_leads:
                print(f"     - {lead.name}: {lead.phone} (创建时间: {lead.created_at})")
            
            # 6. 清理测试数据
            print("\n🧹 清理测试数据...")
            cleanup_count = 0
            for lead in imported_leads:
                db.session.delete(lead)
                cleanup_count += 1
            
            db.session.commit()
            print(f"   清理了 {cleanup_count} 条测试线索")
            
            # 7. 总结
            print("\n" + "="*60)
            print("📊 调试总结:")
            print("="*60)
            
            if success_count > 0:
                print("✅ 导入功能正常工作")
            else:
                print("❌ 导入功能可能有问题")
                print("   可能的原因:")
                print("   1. 重复数据检查过于严格")
                print("   2. 数据验证失败")
                print("   3. 数据库约束问题")
            
            return True
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    debug_import()
