# CRM客户关系管理系统

一个基于Flask的现代化CRM系统，支持线索管理、组织架构管理、权限控制等功能。

## 🚀 快速部署

### 方法一：一键部署（推荐）

1. **打包系统**
```bash
python package.py
```

2. **上传到服务器**
```bash
# 将生成的 crm_deploy_*.tar.gz 文件上传到服务器
scp crm_deploy_*.tar.gz root@your-server:/tmp/
```

3. **服务器端部署**
```bash
# 解压
cd /tmp
tar -xzf crm_deploy_*.tar.gz

# 一键部署
sudo python deploy.py
```

### 方法二：手动部署

1. **环境准备**
```bash
# 安装Python 3.7+
sudo apt update
sudo apt install python3 python3-pip python3-venv

# 安装MySQL
sudo apt install mysql-server

# 安装Nginx
sudo apt install nginx

# 安装Redis
sudo apt install redis-server
```

2. **创建数据库**
```sql
CREATE DATABASE crm_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'crm_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON crm_db.* TO 'crm_user'@'localhost';
FLUSH PRIVILEGES;
```

3. **部署应用**
```bash
# 创建应用目录
sudo mkdir -p /www/wwwroot/crm.jeyue.net
cd /www/wwwroot/crm.jeyue.net

# 解压应用文件
tar -xzf /path/to/crm_deploy_*.tar.gz

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.prod .env
# 编辑 .env 文件，修改数据库连接等配置

# 运行数据库迁移
export FLASK_APP=app.py
flask db upgrade

# 启动应用
uwsgi --ini uwsgi.ini
```

## 📋 系统功能

### 核心功能
- **线索管理**：线索录入、分配、跟进、转化
- **组织管理**：公司、部门、员工三级架构
- **权限控制**：基于角色的权限管理
- **数据统计**：销售漏斗、业绩统计
- **跨公司协作**：线索导入导出、跨地区协作

### 技术特性
- **响应式设计**：支持PC和移动端
- **数据安全**：手机号脱敏、权限隔离
- **高性能**：数据库优化、缓存机制
- **易扩展**：模块化设计、插件架构

## 🔧 配置说明

### 环境变量配置

编辑 `.env` 文件：

```bash
# 基本配置
FLASK_ENV=production
FLASK_DEBUG=0
SECRET_KEY=your-secret-key-here

# 数据库配置
DATABASE_URL=mysql+pymysql://username:password@localhost/database_name

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 文件上传
UPLOAD_FOLDER=/www/wwwroot/crm.jeyue.net/uploads
MAX_CONTENT_LENGTH=16777216

# 安全配置（HTTPS环境设为True）
SESSION_COOKIE_SECURE=False
REMEMBER_COOKIE_SECURE=False
```

### Nginx配置

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /static {
        alias /www/wwwroot/crm.jeyue.net/static;
        expires 30d;
    }
    
    location /uploads {
        alias /www/wwwroot/crm.jeyue.net/uploads;
    }
}
```

## 👤 默认账号

部署完成后，系统会自动创建默认管理员账号：

- **用户名**：admin
- **密码**：admin123
- **角色**：超级管理员

⚠️ **安全提醒**：首次登录后请立即修改默认密码！

## 🛠 维护命令

### 服务管理
```bash
# 启动服务
sudo systemctl start crm-uwsgi
sudo systemctl start nginx

# 停止服务
sudo systemctl stop crm-uwsgi

# 重启服务
sudo systemctl restart crm-uwsgi
sudo systemctl restart nginx

# 查看状态
sudo systemctl status crm-uwsgi
```

### 日志查看
```bash
# 应用日志
tail -f /www/wwwroot/crm.jeyue.net/logs/app.log

# uWSGI日志
tail -f /www/wwwroot/crm.jeyue.net/logs/uwsgi.log

# Nginx日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

### 数据库维护
```bash
cd /www/wwwroot/crm.jeyue.net
source venv/bin/activate

# 数据库迁移
export FLASK_APP=app.py
flask db upgrade

# 创建新迁移
flask db migrate -m "描述信息"
```

## 🔍 故障排除

### 常见问题

1. **无法登录**
   - 检查数据库连接
   - 确认用户数据是否存在
   - 查看应用日志

2. **静态文件无法加载**
   - 检查Nginx配置
   - 确认文件权限
   - 查看Nginx日志

3. **数据库连接失败**
   - 检查数据库服务状态
   - 确认连接配置
   - 检查防火墙设置

### 获取帮助

如遇到问题，请提供以下信息：
- 错误日志内容
- 系统环境信息
- 操作步骤描述

## 📄 许可证

本项目采用 MIT 许可证。
