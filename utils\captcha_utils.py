# -*- coding:utf-8 -*-
from PIL import Image, ImageDraw, ImageFont
import random, string, io

def gen_text(len=4):  # 生成随机字符串
    return ''.join(random.sample(string.ascii_uppercase, len))  # 只使用大写字母

def gen_color(s=1, e=255):  # 生成随机颜色
    return random.randint(s, e), random.randint(s, e), random.randint(s, e)

def generate_captcha():  # 生成验证码图片
    # 先创建一个小尺寸的图片
    base_w, base_h = 80, 30
    base_img = Image.new('RGB', (base_w, base_h), (255, 255, 255))
    draw = ImageDraw.Draw(base_img)
    
    # 生成验证码文字
    code = gen_text()
    
    # 在小图上绘制文字
    font = ImageFont.load_default()
    char_w = base_w // 5  # 字符宽度（包括间距）
    base_y = base_h // 4  # 文字y坐标
    
    # 绘制每个字符
    for i, char in enumerate(code):
        x = char_w * (i + 0.5)  # 居中绘制
        y = base_y
        # 绘制主字符
        draw.text((x, y), char, font=font, fill=(0, 0, 0))
    
    # 放大图片
    scale = 5  # 增加放大倍数
    w, h = base_w * scale, base_h * scale
    img = base_img.resize((w, h), Image.Resampling.LANCZOS)
    draw = ImageDraw.Draw(img)
    
    # 添加背景干扰线（细线）
    for i in range(8):  # 增加背景细线
        x1, y1 = random.randint(0, w), random.randint(0, h)
        x2, y2 = random.randint(0, w), random.randint(0, h)
        draw.line((x1, y1, x2, y2), fill=gen_color(150, 200), width=3)  # 颜色调整到150-200
    
    # 添加前景干扰线（粗线）
    for i in range(3):  # 添加粗线
        x1, y1 = random.randint(0, w), random.randint(0, h)
        x2, y2 = random.randint(0, w), random.randint(0, h)
        # 绘制半透明的粗线
        for j in range(3):  # 增加重叠次数
            draw.line((x1+j, y1+j, x2+j, y2+j), fill=gen_color(150, 200), width=4)  # 颜色调整到150-200
    
    # 添加弧线
    for i in range(2):  # 添加弧线
        x1, y1 = random.randint(0, w//4), random.randint(0, h)
        x2, y2 = random.randint(3*w//4, w), random.randint(0, h)
        # 控制点，使线条弯曲
        cx = random.randint(w//4, 3*w//4)
        cy = random.randint(0, h)
        # 绘制贝塞尔曲线的近似
        points = [(x1, y1)]
        steps = 10
        for t in range(1, steps):
            t = t / steps
            # 二次贝塞尔曲线
            x = (1-t)**2 * x1 + 2*(1-t)*t*cx + t**2*x2
            y = (1-t)**2 * y1 + 2*(1-t)*t*cy + t**2*y2
            points.append((x, y))
        points.append((x2, y2))
        # 绘制曲线
        draw.line(points, fill=gen_color(150, 200), width=2)  # 颜色调整到150-200
    
    # 转换为二进制流
    out = io.BytesIO()
    img.save(out, 'png')
    out.seek(0)
    
    return out.getvalue(), code 