from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, timezone
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from utils.timezone_compat import get_shanghai_now

db = SQLAlchemy()

# 导入其他模型
from .rbac import Role, Permission
from .organization import Company, Department, LeadPool
from .setting import Setting

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    phone = db.Column(db.String(20), unique=True)
    password_hash = db.Column(db.String(128))
    name = db.Column(db.String(80))
    status = db.Column(db.String(20), default='active')  # 用户状态：active, inactive, suspended
    company_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON>('company.id'))
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'))
    role_id = db.Column(db.Integer, db.ForeignKey('role.id'))
    password_changed_at = db.Column(db.DateTime, default=get_shanghai_now)
    created_at = db.Column(db.DateTime, default=get_shanghai_now)
    updated_at = db.Column(db.DateTime, default=get_shanghai_now, onupdate=get_shanghai_now)

    company = db.relationship('Company', foreign_keys=[company_id], back_populates='users')
    department = db.relationship('Department', foreign_keys=[department_id], back_populates='users')
    role = db.relationship('Role', back_populates='users')
    leads = db.relationship('Lead', foreign_keys='Lead.owner_id', back_populates='owner')
    activities = db.relationship('Activity', back_populates='user')

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def has_permission(self, permission_code):
        """检查用户是否有指定权限"""
        if self.role and self.role.code == 'super_admin':
            return True
        if not self.role:
            return False
        return any(p.code == permission_code for p in self.role.permissions)

    @property
    def is_active(self):
        """检查用户是否处于活动状态"""
        return self.status == 'active'

    @property
    def is_manager(self):
        """检查用户是否为管理员（公司管理员或部门管理员）"""
        if not self.role:
            return False
        return self.role.code in ['company_admin', 'department_admin', 'super_admin']

    @property
    def should_exclude_from_ranking(self):
        """检查用户是否应该排除在业绩排名之外"""
        # 管理员不参与业绩排名
        return self.is_manager

class Lead(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # 姓名
    company_name = db.Column(db.String(100), nullable=False)  # 省份 (实际存储省份信息)
    email = db.Column(db.String(120))  # 城市 (实际存储城市信息)
    phone = db.Column(db.String(20))  # 电话
    notes = db.Column(db.Text)  # 备注
    source = db.Column(db.String(50))  # 线索来源
    
    # 新的状态跟踪字段
    is_called = db.Column(db.Boolean, default=False)  # 是否拨打
    is_connected = db.Column(db.Boolean, default=False)  # 是否接通
    is_valid_call = db.Column(db.Boolean, default=False)  # 是否有效通话
    is_wechat_added = db.Column(db.Boolean, default=False)  # 是否加微
    is_intentional = db.Column(db.Boolean, default=False)  # 是否意向客户
    is_compliant = db.Column(db.Boolean, default=False)  # 是否合规
    is_visited = db.Column(db.Boolean, default=False)  # 是否到面
    is_car_selected = db.Column(db.Boolean, default=False)  # 是否提车
    is_deal_done = db.Column(db.Boolean, default=False)  # 是否签约
    
    status = db.Column(db.String(20), default='new')
    owner_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    company_id = db.Column(db.Integer, db.ForeignKey('company.id'))  # 当前所属公司
    original_company_id = db.Column(db.Integer, db.ForeignKey('company.id'))  # 原始录入公司
    current_processing_company_id = db.Column(db.Integer, db.ForeignKey('company.id'), nullable=True)  # 当前处理线索的公司ID
    pool_id = db.Column(db.Integer, db.ForeignKey('lead_pool.id'))  # 所属线索池
    is_in_public_sea = db.Column(db.Boolean, default=False)  # 是否在公海中
    public_sea_time = db.Column(db.DateTime)  # 进入公海时间
    protection_end_time = db.Column(db.DateTime)  # 保护期结束时间
    is_self_created = db.Column(db.Boolean, default=True)  # 是否为自录入
    last_owner_id = db.Column(db.Integer, db.ForeignKey('user.id'))  # 上一个负责人
    deal_status = db.Column(db.String(20), default='pending')  # 签约状态：pending, won, lost
    deal_feedback_time = db.Column(db.DateTime)  # 签约反馈时间
    last_cross_location_push_time = db.Column(db.DateTime, nullable=True)  # 最近一次推送到异地池的时间
    created_at = db.Column(db.DateTime, default=get_shanghai_now)
    updated_at = db.Column(db.DateTime, default=get_shanghai_now, onupdate=get_shanghai_now)

    owner = db.relationship('User', foreign_keys=[owner_id], back_populates='leads')
    last_owner = db.relationship('User', foreign_keys=[last_owner_id])
    company = db.relationship('Company', foreign_keys=[company_id])
    original_company = db.relationship('Company', foreign_keys=[original_company_id])
    current_processing_company = db.relationship('Company', foreign_keys=[current_processing_company_id])
    pool = db.relationship('LeadPool', back_populates='leads')
    activities = db.relationship('Activity', back_populates='lead')
    transfer_histories = db.relationship('LeadTransferHistory', back_populates='lead')
    
    # 新增与LeasePlan的关联
    lease_plan = db.relationship('LeasePlan', back_populates='lead', uselist=False, cascade="all, delete-orphan")

    @property
    def current_stage(self):
        # 根据状态字段确定当前最高阶段，按照业务流程顺序
        if self.is_car_selected:  # 提车是最终阶段
            return "已提车"
        if self.is_deal_done:     # 签约
            return "已签约"
        if self.is_compliant:     # 信息合规
            return "信息合规"
        if self.is_visited:       # 已到面
            return "已到面"
        if self.is_intentional:   # 意向客户
            return "意向客户"
        if self.is_wechat_added:  # 已加微信
            return "已加微信"
        if self.is_valid_call:    # 有效通话
            return "有效通话"
        if self.is_connected:     # 已接通
            return "已接通"
        if self.is_called:        # 已拨电话
            return "已拨电话"
        return "新线索"           # 初始状态

class Activity(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    lead_id = db.Column(db.Integer, db.ForeignKey('lead.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    description = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=get_shanghai_now)
    
    lead = db.relationship('Lead', back_populates='activities')
    user = db.relationship('User', back_populates='activities')

# 新增线索流转历史模型
class LeadTransferHistory(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    lead_id = db.Column(db.Integer, db.ForeignKey('lead.id'))
    from_company_id = db.Column(db.Integer, db.ForeignKey('company.id'))
    to_company_id = db.Column(db.Integer, db.ForeignKey('company.id'))
    target_company_id_for_processing = db.Column(db.Integer, db.ForeignKey('company.id'), nullable=True)
    from_pool_id = db.Column(db.Integer, db.ForeignKey('lead_pool.id'))
    to_pool_id = db.Column(db.Integer, db.ForeignKey('lead_pool.id'))
    from_user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    to_user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    transfer_type = db.Column(db.String(20))  # 流转类型：assign, transfer, release, fetch
    notes = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=get_shanghai_now)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    
    lead = db.relationship('Lead', back_populates='transfer_histories')
    from_company = db.relationship('Company', foreign_keys=[from_company_id])
    to_company = db.relationship('Company', foreign_keys=[to_company_id])
    from_pool = db.relationship('LeadPool', foreign_keys=[from_pool_id])
    to_pool = db.relationship('LeadPool', foreign_keys=[to_pool_id])
    from_user = db.relationship('User', foreign_keys=[from_user_id])
    to_user = db.relationship('User', foreign_keys=[to_user_id])
    creator = db.relationship('User', foreign_keys=[created_by])

# 新增状态历史记录模型
class LeadStatusHistory(db.Model):
    """线索状态变更历史"""
    id = db.Column(db.Integer, primary_key=True)
    lead_id = db.Column(db.Integer, db.ForeignKey('lead.id'), nullable=False)
    status_field = db.Column(db.String(30), nullable=False)  # 状态字段名
    old_value = db.Column(db.Boolean)  # 原状态值
    new_value = db.Column(db.Boolean)  # 新状态值
    notes = db.Column(db.String(200))  # 备注
    created_at = db.Column(db.DateTime, default=get_shanghai_now)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    
    # 关系
    lead = db.relationship('Lead', backref='status_history')
    creator = db.relationship('User', foreign_keys=[created_by])

# 新增模型: LeadImportRequest (步骤 1 修正)
class LeadImportRequest(db.Model):
    __tablename__ = 'lead_import_requests'

    id = db.Column(db.Integer, primary_key=True)
    requester_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    requester_company_id = db.Column(db.Integer, db.ForeignKey('company.id'), nullable=False)
    target_company_id = db.Column(db.Integer, db.ForeignKey('company.id'), nullable=False)
    approver_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    file_path = db.Column(db.String(256), nullable=False)
    original_filename = db.Column(db.String(256), nullable=True)
    status = db.Column(db.String(20), default='pending', nullable=False, index=True) # 'pending', 'approved', 'rejected', 'processing', 'completed', 'failed'
    requested_at = db.Column(db.DateTime, default=get_shanghai_now)
    processed_at = db.Column(db.DateTime, nullable=True)
    notes = db.Column(db.Text, nullable=True)
    approver_notes = db.Column(db.Text, nullable=True)
    total_rows = db.Column(db.Integer, nullable=True)
    imported_count = db.Column(db.Integer, nullable=True)
    failed_count = db.Column(db.Integer, nullable=True)
    failure_reason = db.Column(db.Text, nullable=True)

    # Relationships (确保外键指向正确的小写表名)
    requester_user = db.relationship('User', foreign_keys=[requester_user_id], backref='sent_import_requests')
    requester_company = db.relationship('Company', foreign_keys=[requester_company_id], back_populates='sent_import_requests')
    target_company = db.relationship('Company', foreign_keys=[target_company_id], back_populates='received_import_requests')
    approver_user = db.relationship('User', foreign_keys=[approver_user_id], backref='processed_import_requests')

# 新增模型: Notification (步骤 1 修正)
class Notification(db.Model):
    __tablename__ = 'notifications'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, index=True)
    message = db.Column(db.Text, nullable=False)
    related_url = db.Column(db.String(256), nullable=True)
    is_read = db.Column(db.Boolean, default=False, nullable=False, index=True)
    created_at = db.Column(db.DateTime, default=get_shanghai_now, index=True)

    # Relationship
    user = db.relationship('User', backref=db.backref('notifications', lazy='dynamic'))

# 新增 LeasePlan 模型定义
class LeasePlan(db.Model):
    __tablename__ = 'lease_plan'
    id = db.Column(db.Integer, primary_key=True)
    lead_id = db.Column(db.Integer, db.ForeignKey('lead.id'), nullable=False, unique=True, index=True)
    
    term_months = db.Column(db.Integer, nullable=True)
    free_rent_days = db.Column(db.Integer, default=0, nullable=True)
    custom_notes = db.Column(db.Text, nullable=True)

    created_at = db.Column(db.DateTime, default=get_shanghai_now)
    updated_at = db.Column(db.DateTime, default=get_shanghai_now, onupdate=get_shanghai_now)

    lead = db.relationship('Lead', back_populates='lease_plan')

    def __repr__(self):
        return f'<LeasePlan {self.id} for Lead {self.lead_id}>'

# 步骤 2 修正: 更新 __all__
__all__ = ['db', 'Company', 'Department', 'LeadPool', 'User', 'Lead', 'Activity', 'LeadTransferHistory', 'LeadStatusHistory', 'Role', 'Permission', 'LeadImportRequest', 'Notification', 'Setting', 'LeasePlan', 'get_shanghai_now']