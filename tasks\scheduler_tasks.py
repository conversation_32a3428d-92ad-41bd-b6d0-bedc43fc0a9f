from datetime import datetime, timedelta, timezone
from sqlalchemy import or_
from models import db, Lead, LeadPool, Activity
from utils.timezone_compat import get_shanghai_now, to_shanghai_time

def move_leads_to_public_sea(app):
    """将超过保护期的线索移动到公海池"""
    try:
        with app.app_context():
            # 获取公海池
            public_sea = LeadPool.query.filter_by(is_public_sea=True).first()
            if not public_sea:
                app.logger.error('公海池不存在')
                return
            
            # 查找需要移动到公海的线索
            now = get_shanghai_now()
            leads_to_move = Lead.query.filter(
                or_(
                    # 已过保护期的线索
                    Lead.protection_end_time < now,
                    # 超过30天未跟进的线索
                    Lead.last_followup_time < now - timedelta(days=30)
                ),
                Lead.owner_id.isnot(None),  # 有负责人的线索
                Lead.is_in_public_sea == False,  # 不在公海中的线索
                Lead.deal_status == 'pending'  # 未签约的线索
            ).all()
            
            for lead in leads_to_move:
                # 记录原负责人
                lead.last_owner_id = lead.owner_id
                # 移除负责人
                lead.owner_id = None
                # 更新公海状态
                lead.is_in_public_sea = True
                lead.public_sea_time = now
                lead.pool_id = public_sea.id
                
                # 添加操作记录
                activity = Activity(
                    lead_id=lead.id,
                    description='系统自动将线索移入公海池'
                )
                db.session.add(activity)
            
            db.session.commit()
            app.logger.info(f'成功将{len(leads_to_move)}条线索移动到公海池')
    
    except Exception as e:
        app.logger.error(f'移动线索到公海池失败：{str(e)}', exc_info=True)

def check_protection_expiry(app):
    """检查线索保护期即将到期并发送提醒"""
    try:
        with app.app_context():
            now = get_shanghai_now()
            expiry_warning_time = now + timedelta(days=7)  # 提前7天提醒
            
            leads_expiring = Lead.query.filter(
                Lead.protection_end_time.between(now, expiry_warning_time),
                Lead.is_in_public_sea == False,
                Lead.owner_id.isnot(None),
                Lead.deal_status == 'pending'
            ).all()
            
            for lead in leads_expiring:
                # 添加提醒记录
                activity = Activity(
                    lead_id=lead.id,
                    description=f'线索保护期即将于{lead.protection_end_time.strftime("%Y-%m-%d")}到期'
                )
                db.session.add(activity)
            
            db.session.commit()
            app.logger.info(f'成功检查{len(leads_expiring)}条线索的保护期状态')
    
    except Exception as e:
        app.logger.error(f'检查线索保护期失败：{str(e)}', exc_info=True)