{% extends 'base.html' %}

{% block title %}{% if action == 'edit' %}编辑公司{% else %}添加公司{% endif %} - CRM系统{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-building"></i>
                            {% if action == 'edit' %}编辑公司{% else %}添加公司{% endif %}
                        </h4>
                        <a href="{{ url_for('organization_unified.companies') }}" class="btn btn-sm btn-light">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" id="companyForm" data-skip-validation="true">
                        {{ form.csrf_token }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">
                                        公司名称 <span class="text-danger">*</span>
                                    </label>
                                    {{ form.name(class="form-control", id="name", required=true, placeholder="请输入公司名称") }}
                                    {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.name.errors %}
                                        <i class="fas fa-exclamation-circle"></i> {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="code" class="form-label">
                                        公司代码 <span class="text-danger">*</span>
                                    </label>
                                    {{ form.code(class="form-control", id="code", required=true, placeholder="请输入公司代码") }}
                                    {% if form.code.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.code.errors %}
                                        <i class="fas fa-exclamation-circle"></i> {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <div class="form-text">
                                        <i class="fas fa-info-circle"></i> 
                                        唯一的公司标识代码，用于系统内部识别
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="type" class="form-label">
                                        公司类型 <span class="text-danger">*</span>
                                    </label>
                                    {{ form.type(class="form-select", id="type", required=true) }}
                                    {% if form.type.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.type.errors %}
                                        <i class="fas fa-exclamation-circle"></i> {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3" id="parent-company-group">
                                    <label for="parent_id" class="form-label">上级公司</label>
                                    {{ form.parent_id(class="form-select", id="parent_id") }}
                                    {% if form.parent_id.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.parent_id.errors %}
                                        <i class="fas fa-exclamation-circle"></i> {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <div class="form-text">
                                        <i class="fas fa-info-circle"></i> 
                                        仅子公司需要选择上级公司
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="manager_id" class="form-label">公司管理员</label>
                                    <div class="input-group">
                                        {{ form.manager_id(class="form-select", id="manager_id") }}
                                        {% if action == 'add' %}
                                        <button type="button" class="btn btn-outline-primary" id="addNewManagerBtn">
                                            <i class="fas fa-plus"></i> 新建员工
                                        </button>
                                        {% endif %}
                                    </div>
                                    {% if form.manager_id.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.manager_id.errors %}
                                        <i class="fas fa-exclamation-circle"></i> {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <div class="form-text">
                                        <i class="fas fa-info-circle"></i>
                                        选择负责管理此公司的管理员，或新建员工作为管理员
                                    </div>
                                </div>
                            </div>
                            {% if form.is_default %}
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        {{ form.is_default(class="form-check-input", id="is_default") }}
                                        <label class="form-check-label" for="is_default">
                                            设为默认公司
                                        </label>
                                        {% if form.is_default.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.is_default.errors %}
                                            <i class="fas fa-exclamation-circle"></i> {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                        <div class="form-text">
                                            <i class="fas fa-info-circle"></i>
                                            默认公司将作为新用户的默认归属公司
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>

                        <hr class="my-4">
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('organization_unified.companies') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 
                                {% if action == 'edit' %}更新公司{% else %}创建公司{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 新建员工模态框 -->
<div class="modal fade" id="newManagerModal" tabindex="-1" aria-labelledby="newManagerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newManagerModalLabel">
                    <i class="fas fa-user-plus"></i> 新建公司管理员
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="newManagerForm">
                    <div class="mb-3">
                        <label for="newManagerName" class="form-label">姓名 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="newManagerName" required>
                    </div>
                    <div class="mb-3">
                        <label for="newManagerUsername" class="form-label">用户名 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="newManagerUsername" required>
                        <div class="form-text">用于登录系统的用户名</div>
                    </div>
                    <div class="mb-3">
                        <label for="newManagerEmail" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="newManagerEmail">
                    </div>
                    <div class="mb-3">
                        <label for="newManagerPhone" class="form-label">手机号</label>
                        <input type="tel" class="form-control" id="newManagerPhone">
                    </div>
                    <div class="mb-3">
                        <label for="newManagerPassword" class="form-label">初始密码</label>
                        <input type="password" class="form-control" id="newManagerPassword" value="123456">
                        <div class="form-text">默认密码为123456，用户首次登录后可修改</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveNewManagerBtn">
                    <i class="fas fa-save"></i> 保存并选择
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 根据公司类型显示/隐藏上级公司选项
document.getElementById('type').addEventListener('change', function() {
    const parentGroup = document.getElementById('parent-company-group');
    if (this.value === 'subsidiary') {
        parentGroup.style.display = 'block';
    } else {
        parentGroup.style.display = 'none';
        document.getElementById('parent_id').value = '0';
    }
});

// 新建管理员按钮点击事件
document.getElementById('addNewManagerBtn').addEventListener('click', function() {
    const modal = new bootstrap.Modal(document.getElementById('newManagerModal'));
    modal.show();
});

// 保存新建管理员
document.getElementById('saveNewManagerBtn').addEventListener('click', function() {
    const form = document.getElementById('newManagerForm');

    // 获取表单数据
    const name = document.getElementById('newManagerName').value.trim();
    const username = document.getElementById('newManagerUsername').value.trim();
    const email = document.getElementById('newManagerEmail').value.trim();
    const phone = document.getElementById('newManagerPhone').value.trim();
    const password = document.getElementById('newManagerPassword').value || '123456';

    // 基本验证
    if (!name) {
        alert('请输入姓名');
        document.getElementById('newManagerName').focus();
        return;
    }

    if (!username) {
        alert('请输入用户名');
        document.getElementById('newManagerUsername').focus();
        return;
    }

    // 将新建管理员信息存储到隐藏字段中，等公司创建成功后再处理
    const managerData = {
        name: name,
        username: username,
        email: email,
        phone: phone,
        password: password,
        role_code: 'company_admin'
    };

    // 创建隐藏字段存储新建管理员数据
    let hiddenField = document.getElementById('new_manager_data');
    if (!hiddenField) {
        hiddenField = document.createElement('input');
        hiddenField.type = 'hidden';
        hiddenField.id = 'new_manager_data';
        hiddenField.name = 'new_manager_data';
        document.getElementById('companyForm').appendChild(hiddenField);
    }
    hiddenField.value = JSON.stringify(managerData);

    // 添加到管理员选择框（临时显示）
    const managerSelect = document.getElementById('manager_id');
    const option = document.createElement('option');
    option.value = 'NEW_MANAGER';  // 特殊标识
    option.textContent = `${name} (${username}) - 待创建`;
    option.selected = true;
    managerSelect.appendChild(option);

    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('newManagerModal'));
    modal.hide();

    // 清空表单
    form.reset();
    document.getElementById('newManagerPassword').value = '123456';

    alert('管理员信息已保存，将在公司创建成功后自动创建');
});

// 页面加载时触发一次
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('type').dispatchEvent(new Event('change'));
});

// 表单验证 - 只做提示，不阻止提交
document.getElementById('companyForm').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const code = document.getElementById('code').value.trim();

    // 基本验证，但不阻止提交（让服务器端处理）
    if (!name) {
        alert('请输入公司名称');
        document.getElementById('name').focus();
        // 不调用 e.preventDefault()，让表单正常提交
        return;
    }

    if (!code) {
        alert('请输入公司代码');
        document.getElementById('code').focus();
        // 不调用 e.preventDefault()，让表单正常提交
        return;
    }

    // 公司代码格式提示，但不阻止提交
    if (!/^[A-Z0-9_]+$/.test(code)) {
        // 只给出警告，不阻止提交
        console.warn('公司代码建议只包含大写字母、数字和下划线');
    }

    console.log('表单提交中...', {name, code});
});
</script>
{% endblock %}
