{% extends 'base.html' %}

{% block title %}部门管理 - CRM系统{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-1">
                                <i class="fas fa-users-cog"></i> 部门管理
                            </h1>
                            <p class="text-muted mb-0">管理您权限范围内的所有部门信息</p>
                        </div>
                        <div class="text-end">
                            {% if scope.can_manage_users %}
                            <a href="{{ url_for('organization_unified.employees') }}" class="btn btn-info me-2">
                                <i class="fas fa-users"></i> 员工管理
                            </a>
                            {% endif %}
                            {% if scope.can_manage_departments %}
                            <a href="{{ url_for('organization_unified.add_department') }}" class="btn btn-success me-2">
                                <i class="fas fa-plus"></i> 添加部门
                            </a>
                            {% endif %}
                            <a href="{{ url_for('organization_unified.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> 返回总览
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计信息 - 与线索管理样式完全一致 -->
    <div class="row mb-4">
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-diagram-3 text-success" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-success mt-2 mb-1">总部门数</h6>
                    <h4 class="mb-1">{{ departments|length }}</h4>
                    <small class="text-muted">全部部门</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-building text-info" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-info mt-2 mb-1">涉及公司</h6>
                    <h4 class="mb-1">{{ departments|map(attribute='company_name')|unique|list|length }}</h4>
                    <small class="text-muted">公司数量</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-git text-warning" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-warning mt-2 mb-1">一级部门</h6>
                    <h4 class="mb-1">{{ departments|selectattr('parent_name', 'equalto', '')|list|length }}</h4>
                    <small class="text-muted">顶级部门</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-people text-primary" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-primary mt-2 mb-1">总员工数</h6>
                    <h4 class="mb-1">{{ departments|sum(attribute='user_count') or 0 }}</h4>
                    <small class="text-muted">全部员工</small>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body py-2">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <div class="input-group input-group-sm">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="searchInput" 
                                       placeholder="搜索部门名称...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select form-select-sm" id="companyFilter">
                                <option value="">所有公司</option>
                                {% for company in scope.companies %}
                                <option value="{{ company.id }}" {% if selected_company_id == company.id %}selected{% endif %}>
                                    {{ company.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select form-select-sm" id="typeFilter">
                                <option value="">所有类型</option>
                                <option value="department">普通部门</option>
                                <option value="branch">分支机构</option>
                                <option value="office">办事处</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-outline-secondary btn-sm w-100" onclick="resetFilters()">
                                <i class="fas fa-undo"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 部门列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> 部门列表
                        </h5>
                        <div class="text-muted">
                            共 <span id="totalCount">{{ departments|length }}</span> 个部门
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="departmentsTable">
                            <thead class="table-light">
                                <tr>
                                    <th>部门信息</th>
                                    <th>所属公司</th>
                                    <th>上级部门</th>
                                    <th>部门负责人</th>
                                    <th>员工数量</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for department in departments %}
                                <tr data-company-id="{{ department.company_id }}" 
                                    data-department-name="{{ department.name|lower }}"
                                    data-department-type="{{ department.type or 'department' }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                {% if department.type == 'branch' %}
                                                <i class="fas fa-code-branch text-warning fa-lg"></i>
                                                {% elif department.type == 'office' %}
                                                <i class="fas fa-building text-info fa-lg"></i>
                                                {% else %}
                                                <i class="fas fa-users-cog text-success fa-lg"></i>
                                                {% endif %}
                                            </div>
                                            <div>
                                                <strong>{{ department.name }}</strong>
                                                {% if department.code %}
                                                <br><small class="text-muted">编码: {{ department.code }}</small>
                                                {% endif %}
                                                {% if department.description %}
                                                <br><small class="text-muted">{{ department.description }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <i class="fas fa-building text-primary me-1"></i>
                                        {{ department.company.name if department.company else '-' }}
                                    </td>
                                    <td>
                                        {% if department.parent %}
                                        <i class="fas fa-arrow-up text-muted me-1"></i>
                                        {{ department.parent.name }}
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if department.manager %}
                                        <i class="fas fa-user-tie text-info me-1"></i>
                                        {{ department.manager.name or department.manager.username }}
                                        {% else %}
                                        <span class="text-muted">未设置</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">{{ department.user_count or 0 }}</span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ department.created_at.strftime('%Y-%m-%d') if department.created_at else '-' }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            {% if scope.can_manage_departments %}
                                            <a href="{{ url_for('organization_unified.edit_department', department_id=department.id) }}"
                                               class="btn btn-outline-primary" title="编辑部门">
                                                <i class="fas fa-edit"></i> 编辑
                                            </a>
                                            {% endif %}
                                            <a href="{{ url_for('organization_unified.employees', department_id=department.id) }}"
                                               class="btn btn-outline-success" title="管理员工">
                                                <i class="fas fa-users"></i> 员工
                                            </a>
                                            {% if scope.can_manage_departments and (department.user_count or 0) == 0 %}
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="deleteDepartment({{ department.id }}, '{{ department.name }}')" title="删除部门">
                                                <i class="fas fa-trash"></i> 删除
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteDepartmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning"></i> 确认删除
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>您确定要删除部门 <strong id="deleteDepartmentName"></strong> 吗？</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>警告：</strong>删除操作不可恢复，请谨慎操作！
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let deleteDepartmentId = null;

// 搜索和筛选功能
document.getElementById('searchInput').addEventListener('input', filterTable);
document.getElementById('companyFilter').addEventListener('change', filterTable);
document.getElementById('typeFilter').addEventListener('change', filterTable);

function filterTable() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const companyFilter = document.getElementById('companyFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;
    const rows = document.querySelectorAll('#departmentsTable tbody tr');
    
    let visibleCount = 0;
    
    rows.forEach(row => {
        const name = row.dataset.departmentName;
        const companyId = row.dataset.companyId;
        const type = row.dataset.departmentType;
        
        const matchesSearch = name.includes(searchTerm);
        const matchesCompany = !companyFilter || companyId === companyFilter;
        const matchesType = !typeFilter || type === typeFilter;
        
        const isVisible = matchesSearch && matchesCompany && matchesType;
        row.style.display = isVisible ? '' : 'none';
        
        if (isVisible) visibleCount++;
    });
    
    document.getElementById('totalCount').textContent = visibleCount;
}

function resetFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('companyFilter').value = '';
    document.getElementById('typeFilter').value = '';
    filterTable();
}

// 删除部门
function deleteDepartment(departmentId, departmentName) {
    deleteDepartmentId = departmentId;
    document.getElementById('deleteDepartmentName').textContent = departmentName;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteDepartmentModal'));
    modal.show();
}

// 确认删除
document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
    if (!deleteDepartmentId) return;

    // 发送删除请求
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/organization/departments/${deleteDepartmentId}/delete`;

    // 添加CSRF token
    const csrfToken = document.querySelector('meta[name=csrf-token]');
    if (csrfToken) {
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = csrfToken.getAttribute('content');
        form.appendChild(csrfInput);
    }

    document.body.appendChild(form);
    form.submit();
});
</script>
{% endblock %}
