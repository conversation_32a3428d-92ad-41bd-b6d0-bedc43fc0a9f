{% extends 'base.html' %}

{% block title %}组织管理 - 统一入口{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题区域 -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">
                    <i class="fas fa-sitemap me-2"></i>组织管理
                </h1>
                <p class="page-subtitle">统一管理公司、部门和员工信息，构建完整的组织架构</p>
            </div>
            <div class="text-end">
                <div class="btn-group-theme">
                    {% if scope and scope.role == 'super_admin' %}
                    <a href="{{ url_for('organization_unified.companies') }}" class="btn btn-theme-primary">
                        <i class="fas fa-building me-1"></i>公司管理
                    </a>
                    <a href="{{ url_for('organization_unified.departments') }}" class="btn btn-theme-success">
                        <i class="fas fa-sitemap me-1"></i>部门管理
                    </a>
                    <a href="{{ url_for('organization_unified.employees') }}" class="btn btn-theme-info">
                        <i class="fas fa-users me-1"></i>员工管理
                    </a>
                    {% elif scope and scope.can_manage_users %}
                    <a href="{{ url_for('organization_unified.employees') }}" class="btn btn-theme-info">
                        <i class="fas fa-users me-1"></i>员工管理
                    </a>
                    {% endif %}
                </div>
                <div class="mt-2">
                    <small class="text-muted">权限范围：{{ scope.role | translate_role if scope else '未知' }}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计概览 - 与线索管理样式完全一致 -->
    <div class="row mb-4">
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-building text-primary" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-primary mt-2 mb-1">公司</h6>
                    <h4 class="mb-1" id="totalCompanies">{{ org_tree.total_companies if org_tree else 0 }}</h4>
                    <small class="text-muted">总数量</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-diagram-3 text-success" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-success mt-2 mb-1">部门</h6>
                    <h4 class="mb-1" id="totalDepartments">{{ org_tree.total_departments if org_tree else 0 }}</h4>
                    <small class="text-muted">总数量</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-people text-info" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-info mt-2 mb-1">员工</h6>
                    <h4 class="mb-1" id="totalUsers">{{ org_tree.total_users if org_tree else 0 }}</h4>
                    <small class="text-muted">总数量</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-shield-check text-warning" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-warning mt-2 mb-1">权限级别</h6>
                    <h4 class="mb-1">{{ scope.role | translate_role if scope else '未知' }}</h4>
                    <small class="text-muted">当前权限</small>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="row">
        <!-- 左侧组织树 -->
        <div class="col-lg-4 col-md-5">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-sitemap"></i> 组织架构
                        </h6>
                        <div class="input-group input-group-sm" style="width: 200px;">
                            <input type="text" class="form-control" placeholder="搜索..." id="searchInput">
                            <button class="btn btn-outline-secondary" type="button" onclick="searchTree()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0" style="max-height: 600px; overflow-y: auto;">
                    <div id="organizationTree" class="tree-container">
                        <!-- 组织树将通过JavaScript动态加载 -->
                        <div class="text-center p-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2 text-muted">正在加载组织架构...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧详情面板 -->
        <div class="col-lg-8 col-md-7">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle"></i> 详情信息
                        </h6>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0" id="breadcrumb">
                                <li class="breadcrumb-item text-muted">请选择左侧节点</li>
                            </ol>
                        </nav>
                    </div>
                </div>
                <div class="card-body" style="min-height: 600px;">
                    <div id="detailPanel">
                        <!-- 默认显示 -->
                        <div class="text-center mt-5" id="defaultView">
                            <div class="mb-4">
                                <i class="fas fa-mouse-pointer fa-3x text-muted"></i>
                            </div>
                            <h5 class="text-muted">请在左侧选择要查看的节点</h5>
                            <p class="text-muted">
                                点击公司、部门或员工节点查看详细信息<br>
                                右键点击节点可进行相关操作
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 右键菜单 -->
<div id="contextMenu" class="context-menu" style="display: none;">
    <div class="context-menu-item" data-action="view">
        <i class="fas fa-eye"></i> 查看详情
    </div>
    <div class="context-menu-item" data-action="edit">
        <i class="fas fa-edit"></i> 编辑
    </div>
    <div class="context-menu-item" data-action="delete">
        <i class="fas fa-trash"></i> 删除
    </div>
    <div class="context-menu-divider"></div>
    <div class="context-menu-item" data-action="add_department">
        <i class="fas fa-plus"></i> 添加部门
    </div>
    <div class="context-menu-item" data-action="add_employee">
        <i class="fas fa-user-plus"></i> 添加员工
    </div>
</div>

<!-- 编辑模态框 -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑信息</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <div id="editFormFields">
                        <!-- 动态生成表单字段 -->
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-theme-primary" onclick="saveEdit()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加部门模态框 -->
<div class="modal fade" id="addDepartmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加部门</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addDepartmentForm">
                    <div class="mb-3">
                        <label class="form-label">部门名称 *</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">部门编码 *</label>
                        <input type="text" class="form-control" name="code" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">部门类型</label>
                        <select class="form-control" name="type">
                            <option value="department">普通部门</option>
                            <option value="branch">分支机构</option>
                            <option value="office">办事处</option>
                        </select>
                    </div>
                    <input type="hidden" name="company_id">
                    <input type="hidden" name="parent_id">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-theme-success" onclick="saveAddDepartment()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加员工模态框 -->
<div class="modal fade" id="addEmployeeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加员工</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addEmployeeForm">
                    <div class="mb-3">
                        <label class="form-label">用户名 *</label>
                        <input type="text" class="form-control" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">姓名 *</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">邮箱</label>
                        <input type="email" class="form-control" name="email">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">电话</label>
                        <input type="text" class="form-control" name="phone">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">角色 *</label>
                        <select class="form-control" name="role_id" id="addEmployeeForm-role" required>
                            <!-- 动态加载角色选项 -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">初始密码</label>
                        <input type="password" class="form-control" name="password" value="123456">
                    </div>
                    <input type="hidden" name="company_id">
                    <input type="hidden" name="department_id">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-theme-info" onclick="saveAddEmployee()">保存</button>
            </div>
        </div>
    </div>
</div>

<style>
.organization-container {
    height: calc(100vh - 120px);
}

.tree-container {
    padding: 10px;
}

.tree-node {
    padding: 8px 12px;
    margin: 2px 0;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s;
    user-select: none;
    position: relative;
    line-height: 1.5;
}

.tree-node:hover {
    background-color: #f8f9fa;
}



/* 平台级别节点样式 */
.tree-node.platform {
    border-left: 4px solid #007bff;
    font-weight: 600;
}

.tree-node.company {
    border-left: 4px solid #007bff;
    font-weight: 500;
}

.tree-node.department {
    border-left: 4px solid #28a745;
    margin-left: 20px;
}

.tree-node.user {
    border-left: 4px solid #17a2b8;
    margin-left: 40px;
}

/* 平台级别用户的特殊样式 */
.tree-node.user.platform-level {
    border-left: 4px solid #17a2b8;
    margin-left: 20px;
}

/* 公司级别用户的特殊样式 */
.tree-node.user.company-level {
    border-left: 4px solid #17a2b8;
    margin-left: 20px;
}

.tree-node .toggle-icon {
    float: right;
    transition: transform 0.3s;
}

.tree-node .toggle-icon.rotated {
    transform: rotate(90deg);
}

.tree-node .node-stats {
    font-size: 0.85em;
    opacity: 0.7;
    margin-left: 10px;
}

.tree-children {
    margin-left: 0;
}

.tree-children.collapsed {
    display: none;
}

/* 右键菜单样式 */
.context-menu {
    position: fixed;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    min-width: 150px;
}

.context-menu-item {
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.context-menu-item:hover {
    background-color: #f8f9fa;
}

.context-menu-divider {
    height: 1px;
    background-color: #dee2e6;
    margin: 4px 0;
}

/* 详情面板样式 */
.detail-section {
    margin-bottom: 20px;
}

.detail-section h6 {
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 5px;
    margin-bottom: 10px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid #f8f9fa;
}

.info-label {
    font-weight: 500;
    color: #6c757d;
}

.info-value {
    color: #495057;
}

.action-buttons {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
}
</style>

<script>
// 全局变量
let orgTreeData = null;
let selectedNode = null;
let userPermissions = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadOrganizationTree();
    
    // 点击其他地方隐藏右键菜单
    document.addEventListener('click', function() {
        hideContextMenu();
    });
});

// 加载组织架构树
async function loadOrganizationTree() {
    try {
        const response = await fetch('/organization/api/tree');
        const data = await response.json();
        
        orgTreeData = data;
        userPermissions = data.permissions;
        renderTree(data.companies);
        
    } catch (error) {
        console.error('加载组织架构失败:', error);
        document.getElementById('organizationTree').innerHTML = 
            '<div class="text-center p-4 text-danger">加载失败，请刷新重试</div>';
    }
}

// 渲染组织树
function renderTree(companies) {
    const container = document.getElementById('organizationTree');
    container.innerHTML = '';
    
    companies.forEach(company => {
        const companyElement = createTreeNode(company);
        container.appendChild(companyElement);
    });
}

// 创建树节点
function createTreeNode(node) {
    const nodeDiv = document.createElement('div');
    let className = `tree-node ${node.node_type}`;

    // 为不同级别的用户添加特殊样式类
    if (node.node_type === 'user' && node.stats) {
        if (node.stats.level === 'platform') {
            className += ' platform-level';
        } else if (node.stats.level === 'company') {
            className += ' company-level';
        }
    }

    nodeDiv.className = className;
    nodeDiv.dataset.nodeId = node.id;
    nodeDiv.dataset.nodeType = node.node_type;
    
    // 节点内容
    const hasChildren = node.children && node.children.length > 0;
    const toggleIcon = hasChildren ? '<i class="fas fa-chevron-right toggle-icon"></i>' : '';
    
    let statsText = '';
    if (node.node_type === 'platform') {
        statsText = `(${node.stats.companies}公司, ${node.stats.total_users}用户)`;
    } else if (node.node_type === 'company') {
        statsText = `(${node.stats.departments}部门, ${node.stats.employees}员工)`;
    } else if (node.node_type === 'department') {
        statsText = `(${node.stats.direct_employees}员工)`;
    } else if (node.node_type === 'user') {
        // 角色翻译
        const roleTranslations = {
            'super_admin': '超级管理员',
            'company_admin': '公司管理员',
            'department_admin': '部门管理员',
            'employee': '员工',
            'sales': '销售'
        };
        const translatedRole = roleTranslations[node.stats.role] || node.stats.role;

        if (node.stats.level === 'platform') {
            statsText = `(${translatedRole} - 平台级)`;
        } else if (node.stats.level === 'company') {
            statsText = `(${translatedRole} - 公司级)`;
        } else {
            statsText = `(${translatedRole})`;
        }
    }
    
    nodeDiv.innerHTML = `
        <i class="${node.icon}"></i>
        <span class="node-name">${node.name}</span>
        <span class="node-stats">${statsText}</span>
        ${toggleIcon}
    `;
    
    // 添加事件监听
    nodeDiv.addEventListener('click', function(e) {
        e.stopPropagation();
        selectNode(node);
        
        // 切换子节点显示
        if (hasChildren) {
            toggleChildren(nodeDiv);
        }
    });
    
    nodeDiv.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        showContextMenu(e, node);
    });
    
    // 添加子节点
    if (hasChildren) {
        const childrenDiv = document.createElement('div');
        childrenDiv.className = 'tree-children collapsed';
        
        node.children.forEach(child => {
            const childElement = createTreeNode(child);
            childrenDiv.appendChild(childElement);
        });
        
        nodeDiv.appendChild(childrenDiv);
    }
    
    return nodeDiv;
}

// 选中节点
function selectNode(node) {
    selectedNode = node;
    updateBreadcrumb(node);
    loadNodeDetails(node);
}

// 更新面包屑导航
function updateBreadcrumb(node) {
    const breadcrumb = document.getElementById('breadcrumb');
    // 这里可以实现面包屑逻辑
    breadcrumb.innerHTML = `<li class="breadcrumb-item active">${node.name}</li>`;
}

// 加载节点详情
async function loadNodeDetails(node) {
    const detailPanel = document.getElementById('detailPanel');
    detailPanel.innerHTML = '<div class="text-center"><div class="spinner-border"></div></div>';

    try {
        // 特殊处理平台节点
        if (node.node_type === 'platform') {
            renderPlatformDetails(node);
            return;
        }

        const response = await fetch(`/organization/api/node/${node.node_type}/${node.id}`);
        const details = await response.json();

        renderNodeDetails(details);

    } catch (error) {
        console.error('加载节点详情失败:', error);
        detailPanel.innerHTML = '<div class="text-center text-danger">加载失败</div>';
    }
}

// 渲染节点详情
function renderNodeDetails(details) {
    const detailPanel = document.getElementById('detailPanel');

    if (details.type === 'company') {
        detailPanel.innerHTML = renderCompanyDetails(details);
    } else if (details.type === 'department') {
        detailPanel.innerHTML = renderDepartmentDetails(details);
    } else if (details.type === 'user') {
        detailPanel.innerHTML = renderUserDetails(details);
    }
}

// 渲染平台详情
function renderPlatformDetails(node) {
    const detailPanel = document.getElementById('detailPanel');

    detailPanel.innerHTML = `
        <div class="detail-section">
            <h6><i class="fas fa-globe"></i> 平台信息</h6>
            <div class="info-row">
                <span class="info-label">平台名称:</span>
                <span class="info-value">CRM管理平台</span>
            </div>
            <div class="info-row">
                <span class="info-label">管理范围:</span>
                <span class="info-value">全平台组织架构</span>
            </div>
            <div class="info-row">
                <span class="info-label">管理员级别:</span>
                <span class="info-value"><span class="badge bg-primary">超级管理员</span></span>
            </div>
        </div>

        <div class="detail-section">
            <h6><i class="fas fa-chart-bar"></i> 平台统计</h6>
            <div class="info-row">
                <span class="info-label">公司数量:</span>
                <span class="info-value">${node.stats?.companies || 0}</span>
            </div>
            <div class="info-row">
                <span class="info-label">用户总数:</span>
                <span class="info-value">${node.stats?.total_users || 0}</span>
            </div>
            <div class="info-row">
                <span class="info-label">管理层级:</span>
                <span class="info-value">平台 → 公司 → 部门 → 员工</span>
            </div>
        </div>

        <div class="detail-section">
            <h6><i class="fas fa-shield-alt"></i> 权限说明</h6>
            <div class="info-row">
                <span class="info-label">管理权限:</span>
                <span class="info-value">全平台数据管理</span>
            </div>
            <div class="info-row">
                <span class="info-label">操作范围:</span>
                <span class="info-value">所有公司、部门、用户</span>
            </div>
            <div class="info-row">
                <span class="info-label">特殊权限:</span>
                <span class="info-value">系统配置、角色管理</span>
            </div>
        </div>
    `;
}

// 渲染公司详情
function renderCompanyDetails(details) {
    return `
        <div class="detail-section">
            <h6><i class="fas fa-building"></i> 公司信息</h6>
            <div class="info-row">
                <span class="info-label">公司名称:</span>
                <span class="info-value">${details.basic_info.name}</span>
            </div>
            <div class="info-row">
                <span class="info-label">公司编码:</span>
                <span class="info-value">${details.basic_info.code}</span>
            </div>
            <div class="info-row">
                <span class="info-label">公司类型:</span>
                <span class="info-value">${details.basic_info.type}</span>
            </div>
            <div class="info-row">
                <span class="info-label">创建时间:</span>
                <span class="info-value">${details.basic_info.created_at}</span>
            </div>
        </div>

        <div class="detail-section">
            <h6><i class="fas fa-chart-bar"></i> 统计信息</h6>
            <div class="info-row">
                <span class="info-label">部门数量:</span>
                <span class="info-value">${details.statistics.departments}</span>
            </div>
            <div class="info-row">
                <span class="info-label">员工总数:</span>
                <span class="info-value">${details.statistics.total_employees}</span>
            </div>
            <div class="info-row">
                <span class="info-label">在职员工:</span>
                <span class="info-value">${details.statistics.active_employees}</span>
            </div>
        </div>

        <div class="action-buttons">
            ${renderActionButtons(details.permissions, 'company')}
        </div>
    `;
}

// 渲染部门详情
function renderDepartmentDetails(details) {
    return `
        <div class="detail-section">
            <h6><i class="fas fa-users-cog"></i> 部门信息</h6>
            <div class="info-row">
                <span class="info-label">部门名称:</span>
                <span class="info-value">${details.basic_info.name}</span>
            </div>
            <div class="info-row">
                <span class="info-label">部门编码:</span>
                <span class="info-value">${details.basic_info.code}</span>
            </div>
            <div class="info-row">
                <span class="info-label">所属公司:</span>
                <span class="info-value">${details.basic_info.company_name}</span>
            </div>
            <div class="info-row">
                <span class="info-label">上级部门:</span>
                <span class="info-value">${details.basic_info.parent_name || '无'}</span>
            </div>
        </div>

        <div class="detail-section">
            <h6><i class="fas fa-chart-bar"></i> 统计信息</h6>
            <div class="info-row">
                <span class="info-label">直属员工:</span>
                <span class="info-value">${details.statistics.direct_employees}</span>
            </div>
            <div class="info-row">
                <span class="info-label">在职员工:</span>
                <span class="info-value">${details.statistics.active_employees}</span>
            </div>
            <div class="info-row">
                <span class="info-label">子部门数:</span>
                <span class="info-value">${details.statistics.subdepartments}</span>
            </div>
        </div>

        <div class="detail-section">
            <h6><i class="fas fa-users"></i> 员工列表</h6>
            ${details.employee_list.map(emp => `
                <div class="info-row">
                    <span class="info-label">${emp.name}:</span>
                    <span class="info-value">${emp.role} (${emp.status})</span>
                </div>
            `).join('')}
        </div>

        <div class="action-buttons">
            ${renderActionButtons(details.permissions, 'department')}
        </div>
    `;
}

// 渲染用户详情
function renderUserDetails(details) {
    return `
        <div class="detail-section">
            <h6><i class="fas fa-user"></i> 员工信息</h6>
            <div class="info-row">
                <span class="info-label">姓名:</span>
                <span class="info-value">${details.basic_info.name}</span>
            </div>
            <div class="info-row">
                <span class="info-label">用户名:</span>
                <span class="info-value">${details.basic_info.username}</span>
            </div>
            <div class="info-row">
                <span class="info-label">邮箱:</span>
                <span class="info-value">${details.basic_info.email || '未设置'}</span>
            </div>
            <div class="info-row">
                <span class="info-label">电话:</span>
                <span class="info-value">${details.basic_info.phone || '未设置'}</span>
            </div>
            <div class="info-row">
                <span class="info-label">状态:</span>
                <span class="info-value">${details.basic_info.status}</span>
            </div>
        </div>

        <div class="detail-section">
            <h6><i class="fas fa-building"></i> 组织关系</h6>
            <div class="info-row">
                <span class="info-label">所属公司:</span>
                <span class="info-value">${details.organization.company_name}</span>
            </div>
            <div class="info-row">
                <span class="info-label">所属部门:</span>
                <span class="info-value">${details.organization.department_name || '<span class="text-primary">公司级别</span>'}</span>
            </div>
            <div class="info-row">
                <span class="info-label">角色:</span>
                <span class="info-value">${details.organization.role_name}</span>
            </div>
            ${!details.organization.department_name ? `
            <div class="info-row">
                <span class="info-label">级别:</span>
                <span class="info-value"><span class="badge bg-primary">公司管理员</span></span>
            </div>
            ` : ''}
        </div>

        <div class="detail-section">
            <h6><i class="fas fa-chart-line"></i> 工作统计</h6>
            <div class="info-row">
                <span class="info-label">线索数量:</span>
                <span class="info-value">${details.work_stats.leads_count}</span>
            </div>
            <div class="info-row">
                <span class="info-label">成交数量:</span>
                <span class="info-value">${details.work_stats.deals_count}</span>
            </div>
        </div>

        <div class="action-buttons">
            ${renderActionButtons(details.permissions, 'user')}
        </div>
    `;
}

// 渲染操作按钮
function renderActionButtons(permissions, nodeType) {
    let buttons = '';

    if (permissions.includes('edit')) {
        buttons += '<button class="btn btn-theme-primary btn-sm me-2" onclick="editNode()"><i class="fas fa-edit"></i> 编辑</button>';
    }

    if (permissions.includes('delete')) {
        buttons += '<button class="btn btn-theme-danger btn-sm me-2" onclick="deleteNode()"><i class="fas fa-trash"></i> 删除</button>';
    }

    if (permissions.includes('add_department')) {
        buttons += '<button class="btn btn-theme-success btn-sm me-2" onclick="addDepartment()"><i class="fas fa-plus"></i> 添加部门</button>';
    }

    if (permissions.includes('add_employee')) {
        buttons += '<button class="btn btn-theme-info btn-sm me-2" onclick="addEmployee()"><i class="fas fa-user-plus"></i> 添加员工</button>';
    }

    return buttons;
}

// 工具函数已移除 - 简化界面，移除不必要的操作按钮

function toggleChildren(nodeElement) {
    const childrenDiv = nodeElement.querySelector('.tree-children');
    const toggleIcon = nodeElement.querySelector('.toggle-icon');
    
    if (childrenDiv) {
        childrenDiv.classList.toggle('collapsed');
        toggleIcon.classList.toggle('rotated');
    }
}

function showContextMenu(event, node) {
    const menu = document.getElementById('contextMenu');
    menu.style.display = 'block';
    menu.style.left = event.pageX + 'px';
    menu.style.top = event.pageY + 'px';
    
    // 根据权限显示/隐藏菜单项
    // TODO: 实现权限控制逻辑
}

function hideContextMenu() {
    document.getElementById('contextMenu').style.display = 'none';
}

// 操作函数
function editNode() {
    if (!selectedNode) {
        alert('请先选择一个节点');
        return;
    }

    const nodeType = selectedNode.node_type;
    const nodeId = selectedNode.id;

    // 显示编辑模态框
    showEditModal(nodeType, nodeId);
}

function deleteNode() {
    if (!selectedNode) {
        alert('请先选择一个节点');
        return;
    }

    const nodeType = selectedNode.node_type;
    const nodeId = selectedNode.id;
    const nodeName = selectedNode.name;

    if (confirm(`确定要删除 ${nodeName} 吗？此操作不可恢复。`)) {
        deleteNodeAPI(nodeType, nodeId);
    }
}

function addDepartment() {
    if (!selectedNode) {
        alert('请先选择一个节点');
        return;
    }

    // 显示添加部门模态框
    showAddDepartmentModal(selectedNode);
}

function addEmployee() {
    if (!selectedNode) {
        alert('请先选择一个节点');
        return;
    }

    // 显示添加员工模态框
    showAddEmployeeModal(selectedNode);
}

// API调用函数
function deleteNodeAPI(nodeType, nodeId) {
    fetch(`/organization/api/node/${nodeType}/${nodeId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            loadOrganizationTree(); // 重新加载树
            document.getElementById('detailPanel').innerHTML = '<p class="text-muted">请选择一个节点查看详情</p>';
        } else {
            alert('删除失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('删除失败:', error);
        alert('删除失败，请稍后重试');
    });
}

function updateNodeAPI(nodeType, nodeId, data) {
    fetch(`/organization/api/node/${nodeType}/${nodeId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            loadOrganizationTree(); // 重新加载树
            loadNodeDetails(selectedNode); // 重新加载详情
        } else {
            alert('更新失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('更新失败:', error);
        alert('更新失败，请稍后重试');
    });
}

function createNodeAPI(nodeType, data) {
    fetch(`/organization/api/node/${nodeType}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showFriendlyMessage(data.message, 'success');
            loadOrganizationTree(); // 重新加载树
        } else {
            showFriendlyMessage(data.error, 'error', data.field);
        }
    })
    .catch(error => {
        console.error('创建失败:', error);
        showFriendlyMessage('网络错误，请稍后重试', 'error');
    });
}

// 获取CSRF Token
function getCsrfToken() {
    const token = document.querySelector('meta[name=csrf-token]');
    return token ? token.getAttribute('content') : '';
}

// 显示友好的消息提示
function showFriendlyMessage(message, type = 'info', field = null) {
    // 创建消息容器
    const messageContainer = document.createElement('div');
    messageContainer.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;

    // 设置样式，确保背景不透明
    const bgColor = type === 'success' ? '#d1edff' : '#f8d7da';
    const borderColor = type === 'success' ? '#0ea5e9' : '#dc3545';
    const textColor = type === 'success' ? '#0f5132' : '#721c24';

    messageContainer.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        max-width: 500px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        background-color: ${bgColor} !important;
        border: 1px solid ${borderColor} !important;
        border-left: 4px solid ${borderColor} !important;
        color: ${textColor} !important;
        opacity: 1 !important;
    `;

    // 设置消息内容
    let icon = type === 'success' ? '✅' : '❌';
    messageContainer.innerHTML = `
        <div class="d-flex align-items-center">
            <span class="me-2" style="font-size: 1.2em;">${icon}</span>
            <div class="flex-grow-1">
                <strong>${type === 'success' ? '成功' : '错误'}</strong>
                <div>${message}</div>
                ${field ? `<small style="color: ${textColor}; opacity: 0.8;">相关字段: ${getFieldDisplayName(field)}</small>` : ''}
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" style="filter: none;"></button>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(messageContainer);

    // 自动隐藏（成功消息3秒，错误消息5秒）
    setTimeout(() => {
        if (messageContainer.parentNode) {
            messageContainer.remove();
        }
    }, type === 'success' ? 3000 : 5000);
}

// 获取字段显示名称
function getFieldDisplayName(field) {
    const fieldNames = {
        'username': '用户名',
        'name': '姓名',
        'email': '邮箱',
        'phone': '手机号',
        'company_id': '公司',
        'department_id': '部门',
        'role_id': '角色'
    };
    return fieldNames[field] || field;
}

// 模态框函数
function showEditModal(nodeType, nodeId) {
    // 根据节点类型生成编辑表单
    let formFields = '';

    if (nodeType === 'company') {
        formFields = `
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">公司名称 *</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">公司编码 *</label>
                        <input type="text" class="form-control" name="code" required>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">公司类型</label>
                        <select class="form-control" name="type">
                            <option value="headquarters">总部</option>
                            <option value="subsidiary">子公司</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">上级公司</label>
                        <select class="form-control" name="parent_id" id="edit-parent-company">
                            <option value="">无</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label class="form-label">公司管理员</label>
                <select class="form-control" name="manager_id" id="edit-company-manager">
                    <option value="">请选择管理员</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">描述</label>
                <textarea class="form-control" name="description" rows="3"></textarea>
            </div>
        `;
    } else if (nodeType === 'department') {
        formFields = `
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">部门名称 *</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">部门编码 *</label>
                        <input type="text" class="form-control" name="code" required>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">所属公司 *</label>
                        <select class="form-control" name="company_id" id="edit-dept-company" required>
                            <option value="">请选择公司</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">上级部门</label>
                        <select class="form-control" name="parent_id" id="edit-parent-dept">
                            <option value="">无</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">部门类型</label>
                        <select class="form-control" name="type">
                            <option value="department">普通部门</option>
                            <option value="branch">分支机构</option>
                            <option value="office">办事处</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">部门管理员</label>
                        <select class="form-control" name="manager_id" id="edit-dept-manager">
                            <option value="">请选择管理员</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label class="form-label">描述</label>
                <textarea class="form-control" name="description" rows="3"></textarea>
            </div>
        `;
    } else if (nodeType === 'user') {
        formFields = `
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">姓名 *</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">用户名 *</label>
                        <input type="text" class="form-control" name="username" required readonly>
                        <small class="form-text text-muted">用户名不可修改</small>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">邮箱 *</label>
                        <input type="email" class="form-control" name="email" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">电话</label>
                        <input type="text" class="form-control" name="phone">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">所属公司 *</label>
                        <select class="form-control" name="company_id" id="edit-user-company" required>
                            <option value="">请选择公司</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">所属部门 *</label>
                        <select class="form-control" name="department_id" id="edit-user-dept" required>
                            <option value="">请选择部门</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">角色 *</label>
                        <select class="form-control" name="role_id" id="edit-user-role" required>
                            <option value="">请选择角色</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">状态</label>
                        <select class="form-control" name="status">
                            <option value="active">在职</option>
                            <option value="inactive">离职</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">新密码</label>
                        <input type="password" class="form-control" name="password" placeholder="留空则不修改密码">
                        <small class="form-text text-muted">如需重置密码请填写新密码</small>
                    </div>
                </div>
            </div>
        `;
    }

    document.getElementById('editFormFields').innerHTML = formFields;

    // 加载当前数据和选项
    loadNodeDataForEdit(nodeType, nodeId);

    // 显示模态框
    new bootstrap.Modal(document.getElementById('editModal')).show();
}

function loadNodeDataForEdit(nodeType, nodeId) {
    // 先加载选项数据
    loadEditFormOptions(nodeType).then(() => {
        // 再加载节点数据
        fetch(`/organization/api/node/${nodeType}/${nodeId}`)
        .then(response => response.json())
        .then(data => {
            if (data.basic_info) {
                const form = document.getElementById('editForm');
                const basicInfo = data.basic_info;

                // 填充基本信息
                Object.keys(basicInfo).forEach(key => {
                    const input = form.querySelector(`[name="${key}"]`);
                    if (input) {
                        input.value = basicInfo[key] || '';
                    }
                });

                // 填充组织关系信息（用户）
                if (nodeType === 'user' && data.organization) {
                    const org = data.organization;
                    console.log('🔍 填充用户组织关系信息:', org);

                    // 设置公司
                    const companySelect = form.querySelector('[name="company_id"]');
                    console.log('📋 公司选择框:', companySelect, '公司ID:', org.company_id);

                    if (companySelect) {
                        if (org.company_id) {
                            companySelect.value = org.company_id;
                            console.log('✅ 已设置公司值:', companySelect.value);

                            // 先加载部门选项，然后设置值
                            console.log('🔄 开始加载部门选项...');
                            loadDepartmentOptions('edit-user-dept', org.company_id).then(() => {
                                console.log('✅ 部门选项加载完成，开始设置部门值');
                                // 设置部门值
                                const deptSelect = form.querySelector('[name="department_id"]');
                                console.log('📋 部门选择框:', deptSelect, '部门ID:', org.department_id);
                                if (deptSelect && org.department_id) {
                                    deptSelect.value = org.department_id;
                                    console.log('✅ 已设置部门值:', deptSelect.value);

                                    // 验证设置是否成功
                                    setTimeout(() => {
                                        console.log('🔍 验证部门设置结果:', deptSelect.value, '期望值:', org.department_id);
                                    }, 100);
                                } else {
                                    console.log('ℹ️ 用户无部门或部门选择框无效（可能是平台级用户）');
                                }
                            }).catch(error => {
                                console.error('❌ 加载部门选项失败:', error);
                            });
                        } else {
                            console.log('ℹ️ 用户无公司（平台级用户），跳过部门加载');
                            // 对于平台级用户，清空部门选项
                            const deptSelect = form.querySelector('[name="department_id"]');
                            if (deptSelect) {
                                deptSelect.innerHTML = '<option value="">平台级用户无部门</option>';
                            }
                        }
                    }

                    // 设置角色
                    const roleSelect = form.querySelector('[name="role_id"]');
                    console.log('📋 角色选择框:', roleSelect, '角色ID:', org.role_id);
                    if (roleSelect && org.role_id) {
                        // 延迟设置角色，确保选项已加载
                        setTimeout(() => {
                            roleSelect.value = org.role_id;
                            console.log('✅ 已设置角色值:', roleSelect.value);
                        }, 200);
                    }
                }

                // 填充管理员信息
                if (data.management) {
                    const managerSelect = form.querySelector('[name="manager_id"]');
                    if (managerSelect && data.management.manager_id) {
                        managerSelect.value = data.management.manager_id;
                    }
                }

                // 填充父级关系
                if (data.hierarchy) {
                    const parentSelect = form.querySelector('[name="parent_id"]');
                    if (parentSelect && data.hierarchy.parent_id) {
                        parentSelect.value = data.hierarchy.parent_id;
                    }
                }
            }
        })
        .catch(error => {
            console.error('加载数据失败:', error);
        });
    });
}

async function loadEditFormOptions(nodeType) {
    const form = document.getElementById('editForm');

    if (nodeType === 'company') {
        // 加载上级公司选项
        await loadParentCompanyOptions();
        // 加载管理员选项
        await loadCompanyManagerOptions();

    } else if (nodeType === 'department') {
        // 加载公司选项
        await loadCompanyOptions('edit-dept-company');
        // 加载上级部门选项
        await loadParentDepartmentOptions();
        // 加载管理员选项
        await loadDepartmentManagerOptions();

        // 监听公司变更事件
        const companySelect = form.querySelector('#edit-dept-company');
        if (companySelect) {
            companySelect.addEventListener('change', function() {
                loadParentDepartmentOptions(this.value);
                loadDepartmentManagerOptions(this.value);
            });
        }

    } else if (nodeType === 'user') {
        // 加载公司选项
        await loadCompanyOptions('edit-user-company');
        // 加载角色选项
        await loadRoleOptions('edit-user-role');

        // 确保角色选项加载完成后再继续
        setTimeout(() => {
            const roleSelect = document.getElementById('edit-user-role');
            if (roleSelect && roleSelect.options.length <= 1) {
                console.log('角色选项未正确加载，使用备用方案');
                roleSelect.innerHTML = `
                    <option value="">请选择角色</option>
                    <option value="1">超级管理员</option>
                    <option value="2">公司管理员</option>
                    <option value="3">部门管理员</option>
                    <option value="4">普通员工</option>
                `;
            }
        }, 500);

        // 监听公司变更事件
        const companySelect = form.querySelector('#edit-user-company');
        if (companySelect) {
            companySelect.addEventListener('change', function() {
                loadDepartmentOptions('edit-user-dept', this.value);
            });
        }
    }
}

// 加载选项数据的辅助函数
async function loadCompanyOptions(selectId) {
    try {
        const response = await fetch('/organization_api/companies');
        const companies = await response.json();

        const select = document.getElementById(selectId);
        if (select) {
            select.innerHTML = '<option value="">请选择公司</option>';
            companies.forEach(company => {
                select.innerHTML += `<option value="${company.id}">${company.name}</option>`;
            });
        }
    } catch (error) {
        console.error('加载公司选项失败:', error);
    }
}

async function loadDepartmentOptions(selectId, companyId) {
    console.log('开始加载部门选项，selectId:', selectId, 'companyId:', companyId);

    if (!companyId) {
        const select = document.getElementById(selectId);
        if (select) {
            select.innerHTML = '<option value="">请先选择公司</option>';
        }
        return Promise.resolve();
    }

    try {
        const response = await fetch(`/organization_api/companies/${companyId}/departments`);
        const departments = await response.json();
        console.log('获取到的部门数据:', departments);

        const select = document.getElementById(selectId);
        if (select) {
            select.innerHTML = '<option value="">请选择部门</option>';
            departments.forEach(dept => {
                select.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
            });
            console.log('部门选项加载完成，选项数量:', departments.length);
        }
        return Promise.resolve();
    } catch (error) {
        console.error('加载部门选项失败:', error);
        return Promise.reject(error);
    }
}

function loadRoleOptions(selectId) {
    console.log('开始加载角色选项，selectId:', selectId);

    const select = document.getElementById(selectId);
    if (!select) {
        console.error('未找到角色选择框元素:', selectId);
        return;
    }

    // 先设置加载状态
    select.innerHTML = '<option value="">加载中...</option>';

    fetch('/organization_api/roles')
        .then(response => {
            console.log('角色API响应状态:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(roles => {
            console.log('获取到的角色数据:', roles);

            // 清空并重新填充选项
            select.innerHTML = '<option value="">请选择角色</option>';

            if (Array.isArray(roles) && roles.length > 0) {
                roles.forEach(role => {
                    const option = document.createElement('option');
                    option.value = role.id;
                    option.textContent = role.name;
                    select.appendChild(option);
                });
                console.log('角色选项加载完成，选项数量:', roles.length);
            } else {
                console.warn('角色数据为空或格式不正确');
                select.innerHTML = '<option value="">暂无可用角色</option>';
            }
        })
        .catch(error => {
            console.error('加载角色选项失败:', error);

            // 如果API失败，使用默认角色选项
            select.innerHTML = `
                <option value="">请选择角色</option>
                <option value="1">超级管理员</option>
                <option value="2">公司管理员</option>
                <option value="3">部门管理员</option>
                <option value="4">普通员工</option>
            `;
            console.log('使用默认角色选项');
        });
}

async function loadParentCompanyOptions() {
    try {
        const response = await fetch('/organization_api/companies');
        const companies = await response.json();

        const select = document.getElementById('edit-parent-company');
        if (select) {
            select.innerHTML = '<option value="">无</option>';
            companies.filter(c => c.type === 'headquarters').forEach(company => {
                select.innerHTML += `<option value="${company.id}">${company.name}</option>`;
            });
        }
    } catch (error) {
        console.error('加载上级公司选项失败:', error);
    }
}

async function loadParentDepartmentOptions(companyId) {
    if (!companyId) return;

    try {
        const response = await fetch(`/organization_api/companies/${companyId}/departments`);
        const departments = await response.json();

        const select = document.getElementById('edit-parent-dept');
        if (select) {
            select.innerHTML = '<option value="">无</option>';
            departments.forEach(dept => {
                select.innerHTML += `<option value="${dept.id}">${dept.name}</option>`;
            });
        }
    } catch (error) {
        console.error('加载上级部门选项失败:', error);
    }
}

async function loadCompanyManagerOptions() {
    // 这里可以加载所有用户作为管理员候选
    const select = document.getElementById('edit-company-manager');
    if (select) {
        select.innerHTML = '<option value="">请选择管理员</option>';
        // TODO: 实现加载用户列表
    }
}

async function loadDepartmentManagerOptions(companyId) {
    // 这里可以加载指定公司的用户作为管理员候选
    const select = document.getElementById('edit-dept-manager');
    if (select) {
        select.innerHTML = '<option value="">请选择管理员</option>';
        // TODO: 实现加载公司用户列表
    }
}

function saveEdit() {
    const form = document.getElementById('editForm');
    const formData = new FormData(form);
    const data = {};

    for (let [key, value] of formData.entries()) {
        if (value.trim() !== '') {  // 只提交非空值
            data[key] = value;
        }
    }

    const nodeType = selectedNode.node_type;
    const nodeId = selectedNode.id;

    updateNodeAPI(nodeType, nodeId, data);

    // 关闭模态框
    bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
}

function showAddDepartmentModal(parentNode) {
    const form = document.getElementById('addDepartmentForm');

    // 设置公司ID和父部门ID
    if (parentNode.node_type === 'company') {
        form.querySelector('[name="company_id"]').value = parentNode.id;
        form.querySelector('[name="parent_id"]').value = '';
    } else if (parentNode.node_type === 'department') {
        form.querySelector('[name="company_id"]').value = parentNode.company_id || '';
        form.querySelector('[name="parent_id"]').value = parentNode.id;
    }

    // 显示模态框
    new bootstrap.Modal(document.getElementById('addDepartmentModal')).show();
}

function saveAddDepartment() {
    const form = document.getElementById('addDepartmentForm');
    const formData = new FormData(form);
    const data = {};

    for (let [key, value] of formData.entries()) {
        if (value) {
            data[key] = value;
        }
    }

    createNodeAPI('department', data);

    // 关闭模态框
    bootstrap.Modal.getInstance(document.getElementById('addDepartmentModal')).hide();

    // 清空表单
    form.reset();
}

function showAddEmployeeModal(parentNode) {
    console.log('显示添加员工模态框，父节点信息:', parentNode);

    const form = document.getElementById('addEmployeeForm');
    const companyIdInput = form.querySelector('[name="company_id"]');
    const departmentIdInput = form.querySelector('[name="department_id"]');

    // 确保找到了表单元素
    if (!companyIdInput || !departmentIdInput) {
        console.error('未找到公司ID或部门ID输入框');
        alert('表单初始化失败，请刷新页面重试');
        return;
    }

    // 设置公司ID和部门ID
    if (parentNode.node_type === 'company') {
        console.log('在公司节点下添加员工，公司ID:', parentNode.id);
        companyIdInput.value = String(parentNode.id);
        departmentIdInput.value = '';
    } else if (parentNode.node_type === 'department') {
        console.log('在部门节点下添加员工，公司ID:', parentNode.company_id, '部门ID:', parentNode.id);
        companyIdInput.value = String(parentNode.company_id || '');
        departmentIdInput.value = String(parentNode.id);
    } else {
        console.warn('未知的节点类型:', parentNode.node_type);
        // 尝试从当前选中节点获取信息
        if (selectedNode) {
            if (selectedNode.node_type === 'company') {
                companyIdInput.value = String(selectedNode.id);
                departmentIdInput.value = '';
            } else if (selectedNode.node_type === 'department') {
                companyIdInput.value = String(selectedNode.company_id || '');
                departmentIdInput.value = String(selectedNode.id);
            }
        }
    }

    console.log('设置后的公司ID:', companyIdInput.value);
    console.log('设置后的部门ID:', departmentIdInput.value);

    // 验证公司ID是否设置成功
    if (!companyIdInput.value) {
        console.error('公司ID设置失败');
        alert('无法确定公司信息，请选择一个公司或部门节点后再添加员工');
        return;
    }

    // 加载角色选项
    loadRoleOptions('addEmployeeForm-role');

    // 显示模态框
    new bootstrap.Modal(document.getElementById('addEmployeeModal')).show();
}

function saveAddEmployee() {
    const form = document.getElementById('addEmployeeForm');
    const formData = new FormData(form);
    const data = {};

    // 收集所有表单数据，包括空值
    for (let [key, value] of formData.entries()) {
        data[key] = value || '';
    }

    console.log('收集到的表单数据:', data);

    // 验证必要字段
    const validationErrors = [];

    if (!data.username || data.username.trim() === '') {
        validationErrors.push('用户名');
    }
    if (!data.name || data.name.trim() === '') {
        validationErrors.push('姓名');
    }
    if (!data.company_id || data.company_id.trim() === '') {
        validationErrors.push('公司');
        console.log('公司ID验证失败，当前值:', data.company_id);
        console.log('当前选中节点:', selectedNode);

        // 尝试从选中节点重新获取公司ID
        if (selectedNode) {
            if (selectedNode.node_type === 'company') {
                console.log('尝试从选中的公司节点获取ID:', selectedNode.id);
                data.company_id = selectedNode.id;
                form.querySelector('[name="company_id"]').value = selectedNode.id;
            } else if (selectedNode.node_type === 'department' && selectedNode.company_id) {
                console.log('尝试从选中的部门节点获取公司ID:', selectedNode.company_id);
                data.company_id = selectedNode.company_id;
                form.querySelector('[name="company_id"]').value = selectedNode.company_id;
            }

            // 重新验证公司ID
            if (data.company_id && data.company_id.trim() !== '') {
                validationErrors.pop(); // 移除公司验证错误
                console.log('公司ID已修复:', data.company_id);
            }
        }
    }
    if (!data.role_id || data.role_id.trim() === '') {
        validationErrors.push('角色');
    }

    if (validationErrors.length > 0) {
        alert(`请填写以下必填项：${validationErrors.join('、')}`);
        console.log('验证失败，缺少字段:', validationErrors);
        console.log('最终表单数据:', data);
        return;
    }

    console.log('验证通过，发送用户数据:', data);
    createNodeAPI('user', data);

    // 关闭模态框
    bootstrap.Modal.getInstance(document.getElementById('addEmployeeModal')).hide();

    // 清空表单
    form.reset();
}
</script>
{% endblock %}
