{% extends 'base.html' %}

{% block title %}仪表盘 - CRM系统{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-3">
    <!-- 顶部统计卡片 -->
    <div class="row g-3 mb-4">
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <h6 class="text-muted mb-1">总线索数</h6>
                    <h2 class="mb-0">{{ total_leads }}</h2>
                    <div class="small text-success mt-2">
                        <i class="bi bi-graph-up"></i> 
                        较上月 {{ (total_leads_growth * 100)|round|int }}%
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <h6 class="text-muted mb-1">签约线索</h6>
                    <h2 class="mb-0">{{ deal_done_leads }}</h2>
                    <div class="small text-success mt-2">
                        <i class="bi bi-graph-up"></i> 
                        签约率 {{ (deal_rate * 100)|round|int if total_leads > 0 else 0 }}%
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <h6 class="text-muted mb-1">已放出线索统计</h6>
                    <h2 class="mb-0">{{ released_leads.total }}</h2>
                    <div class="small mt-2">
                        <span class="badge bg-primary me-1">被认领: {{ released_leads.claimed }}</span>
                        <a href="{{ url_for('leads.view_deal_done_released_leads') }}" class="text-decoration-none">
                            <span class="badge bg-success">签约: {{ released_leads.deal_done }}</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 第二行统计卡片 -->
    <div class="row g-3 mb-4">
        <div class="col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <h6 class="text-muted mb-1">本月新增</h6>
                    <h2 class="mb-0">{{ new_leads_this_month }}</h2>
                    <div class="small text-{{ 'success' if new_leads_growth >= 0 else 'danger' }} mt-2">
                        <i class="bi bi-graph-{{ 'up' if new_leads_growth >= 0 else 'down' }}"></i> 
                        较上月 {{ (new_leads_growth * 100)|round|int|abs }}%
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <h6 class="text-muted mb-1">平均转化时间</h6>
                    <h2 class="mb-0">{{ avg_conversion_days|round|int }}天</h2>
                    <div class="small text-{{ 'success' if avg_days_improved else 'danger' }} mt-2">
                        <i class="bi bi-{{ 'arrow-down' if avg_days_improved else 'arrow-up' }}"></i> 
                        较上月 {{ avg_days_change|round|int|abs }}天
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选器 -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form method="get" action="{{ url_for('dashboard') }}" class="row g-3">
                <div class="col-md-2">
                    <label class="form-label">公司</label>
                    <select name="company_id" class="form-select">
                        <option value="">全部公司</option>
                        {% for company in companies %}
                        <option value="{{ company.id }}" {% if request.args.get('company_id')|int == company.id %}selected{% endif %}>{{ company.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">部门</label>
                    <select name="department_id" class="form-select">
                        <option value="">全部部门</option>
                        {% for dept in departments %}
                        <option value="{{ dept.id }}" {% if request.args.get('department_id')|int == dept.id %}selected{% endif %}>{{ dept.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">用户</label>
                    <select name="user_id" class="form-select">
                        <option value="">全部用户</option>
                        {% for user in users %}
                        <option value="{{ user.id }}" {% if request.args.get('user_id')|int == user.id %}selected{% endif %}>{{ user.username }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">开始日期</label>
                    <input type="date" name="start_date" class="form-control" value="{{ selected_start_date }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">结束日期</label>
                    <input type="date" name="end_date" class="form-control" value="{{ selected_end_date }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">阶段</label>
                    <select name="stage" class="form-select">
                        <option value="">全部阶段</option>
                        <option value="is_called" {% if request.args.get('stage') == 'is_called' %}selected{% endif %}>已拨电话</option>
                        <option value="is_valid_call" {% if request.args.get('stage') == 'is_valid_call' %}selected{% endif %}>有效通话</option>
                        <option value="is_wechat_added" {% if request.args.get('stage') == 'is_wechat_added' %}selected{% endif %}>添加微信</option>
                        <option value="is_intentional" {% if request.args.get('stage') == 'is_intentional' %}selected{% endif %}>意向客户</option>
                        <option value="is_visited" {% if request.args.get('stage') == 'is_visited' %}selected{% endif %}>确认到面</option>
                        <option value="is_compliant" {% if request.args.get('stage') == 'is_compliant' %}selected{% endif %}>信息合规</option>
                        <option value="is_deal_done" {% if request.args.get('stage') == 'is_deal_done' %}selected{% endif %}>完成签约</option>
                        <option value="is_car_selected" {% if request.args.get('stage') == 'is_car_selected' %}selected{% endif %}>完成提车</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">应用筛选</button>
                </div>
            </form>
        </div>
    </div>

    <div class="row g-4">
        <!-- 销售漏斗 -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">销售漏斗</h5>
                </div>
                <div class="card-body">
                    <canvas id="funnelChart" height="300"></canvas>
                </div>
            </div>
        </div>
        
        <!-- 每日新增线索 -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">每日新增线索</h5>
                </div>
                <div class="card-body">
                    <canvas id="dailyLeadsChart" height="300"></canvas>
                </div>
            </div>
        </div>
        
        <!-- 线索来源分布 -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">线索来源分布</h5>
                </div>
                <div class="card-body">
                    <canvas id="sourceChart" height="300"></canvas>
                </div>
            </div>
        </div>
        
        <!-- 转化率对比 -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">各阶段转化率</h5>
                </div>
                <div class="card-body">
                    <canvas id="conversionRateChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 公司部门统计表格 -->
    <div class="card border-0 shadow-sm mt-4">
        <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
            <h5 class="mb-0">业绩统计排名</h5>
            <div class="d-flex">
                <div class="btn-group me-3">
                    <button type="button" class="btn btn-sm btn-{{ 'primary' if view_mode == 'company' or not view_mode else 'outline-secondary' }}" id="viewCompany">公司排名</button>
                    <button type="button" class="btn btn-sm btn-{{ 'primary' if view_mode == 'department' else 'outline-secondary' }}" id="viewDepartment">部门排名</button>
                    <button type="button" class="btn btn-sm btn-{{ 'primary' if view_mode == 'user' else 'outline-secondary' }}" id="viewUser">个人排名</button>
                </div>
                
                <div class="input-group input-group-sm me-3" style="width: 200px;">
                    <span class="input-group-text">筛选公司</span>
                    <select class="form-select" id="filterCompany">
                        <option value="">全部公司</option>
                        {% for company in companies %}
                        <option value="{{ company.id }}" {% if selected_company_id == company.id %}selected{% endif %}>{{ company.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="input-group input-group-sm me-3" style="width: 200px;" {% if not selected_company_id %}style="display:none;"{% endif %} id="departmentFilterGroup">
                    <span class="input-group-text">筛选部门</span>
                    <select class="form-select" id="filterDepartment">
                        <option value="">全部部门</option>
                        {% for dept in departments %}
                        <option value="{{ dept.id }}" {% if selected_department_id == dept.id %}selected{% endif %}>{{ dept.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-{{ 'primary' if sort_by == 'total' else 'outline-secondary' }}" id="sortByTotal">按总数排序</button>
                    <button type="button" class="btn btn-sm btn-{{ 'primary' if sort_by == 'deal_done' else 'outline-secondary' }}" id="sortByDealDone">按签约排序</button>
                    <button type="button" class="btn btn-sm btn-{{ 'primary' if sort_by == 'conversion_rate' else 'outline-secondary' }}" id="sortByRate">按转化率排序</button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <!-- 公司排名表格 -->
                <table class="table table-hover mb-0" {% if view_mode != 'company' %}style="display: none;"{% endif %}>
                    <thead class="table-light">
                        <tr>
                            <th>排名</th>
                            <th>公司</th>
                            <th class="text-center">总线索数</th>
                            <th class="text-center">已拨电话</th>
                            <th class="text-center">有效通话</th>
                            <th class="text-center">添加微信</th>
                            <th class="text-center">意向客户</th>
                            <th class="text-center">确认到面</th>
                            <th class="text-center">信息合规</th>
                            <th class="text-center">完成签约</th>
                            <th class="text-center">完成提车</th>
                            <th class="text-center">签约率</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for company in company_stats %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ company.name }}</td>
                            <td class="text-center">{{ company.total }}</td>
                            <td class="text-center">{{ company.called }}</td>
                            <td class="text-center">{{ company.valid_call }}</td>
                            <td class="text-center">{{ company.wechat_added }}</td>
                            <td class="text-center">{{ company.intentional }}</td>
                            <td class="text-center">{{ company.compliant }}</td>
                            <td class="text-center">{{ company.visited }}</td>
                            <td class="text-center">{{ company.deal_done }}</td>
                            <td class="text-center">{{ company.car_selected }}</td>
                            <td class="text-center">{{ "%.1f"|format(company.conversion_rate) }}%</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                
                <!-- 部门排名表格 -->
                <table class="table table-hover mb-0" {% if view_mode != 'department' %}style="display: none;"{% endif %}>
                    <thead class="table-light">
                        <tr>
                            <th>排名</th>
                            <th>部门</th>
                            <th class="text-center">总线索数</th>
                            <th class="text-center">已拨电话</th>
                            <th class="text-center">有效通话</th>
                            <th class="text-center">添加微信</th>
                            <th class="text-center">意向客户</th>
                            <th class="text-center">确认到面</th>
                            <th class="text-center">信息合规</th>
                            <th class="text-center">完成签约</th>
                            <th class="text-center">完成提车</th>
                            <th class="text-center">签约率</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for dept in department_stats %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ dept.name }}</td>
                            <td class="text-center">{{ dept.total }}</td>
                            <td class="text-center">{{ dept.called }}</td>
                            <td class="text-center">{{ dept.valid_call }}</td>
                            <td class="text-center">{{ dept.wechat_added }}</td>
                            <td class="text-center">{{ dept.intentional }}</td>
                            <td class="text-center">{{ dept.compliant }}</td>
                            <td class="text-center">{{ dept.visited }}</td>
                            <td class="text-center">{{ dept.deal_done }}</td>
                            <td class="text-center">{{ dept.car_selected }}</td>
                            <td class="text-center">{{ "%.1f"|format(dept.conversion_rate) }}%</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                
                <!-- 个人排名表格 -->
                <table class="table table-hover mb-0" {% if view_mode != 'user' %}style="display: none;"{% endif %}>
                    <thead class="table-light">
                        <tr>
                            <th>排名</th>
                            <th>姓名</th>
                            <th>部门</th>
                            <th class="text-center">总线索数</th>
                            <th class="text-center">已拨电话</th>
                            <th class="text-center">有效通话</th>
                            <th class="text-center">添加微信</th>
                            <th class="text-center">意向客户</th>
                            <th class="text-center">确认到面</th>
                            <th class="text-center">信息合规</th>
                            <th class="text-center">完成签约</th>
                            <th class="text-center">完成提车</th>
                            <th class="text-center">签约率</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in user_stats %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ user.name }}</td>
                            <td>{{ user.department }}</td>
                            <td class="text-center">{{ user.total }}</td>
                            <td class="text-center">{{ user.called }}</td>
                            <td class="text-center">{{ user.valid_call }}</td>
                            <td class="text-center">{{ user.wechat_added }}</td>
                            <td class="text-center">{{ user.intentional }}</td>
                            <td class="text-center">{{ user.compliant }}</td>
                            <td class="text-center">{{ user.visited }}</td>
                            <td class="text-center">{{ user.deal_done }}</td>
                            <td class="text-center">{{ user.car_selected }}</td>
                            <td class="text-center">{{ "%.1f"|format(user.conversion_rate) }}%</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- 分页 -->
    {% if department_stats|length > 0 and pages > 1 %}
    <nav class="mt-4">
        <ul class="pagination justify-content-center">
            <li class="page-item {{ 'disabled' if page == 1 else '' }}">
                <a class="page-link" href="{{ url_for('dashboard', page=page-1, **request.args) if page > 1 else '#' }}">上一页</a>
            </li>
            
            {% set window_size = 5 %}
            {% set window_start = [1, page - window_size//2]|max %}
            {% set window_end = [window_start + window_size - 1, pages]|min %}
            
            {% if window_start > 1 %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('dashboard', page=1, **request.args) }}">1</a>
                </li>
                {% if window_start > 2 %}
                    <li class="page-item disabled"><span class="page-link">...</span></li>
                {% endif %}
            {% endif %}
            
            {% for p in range(window_start, window_end + 1) %}
                <li class="page-item {{ 'active' if p == page else '' }}">
                    <a class="page-link" href="{{ url_for('dashboard', page=p, **request.args) }}">{{ p }}</a>
                </li>
            {% endfor %}
            
            {% if window_end < pages %}
                {% if window_end < pages - 1 %}
                    <li class="page-item disabled"><span class="page-link">...</span></li>
                {% endif %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('dashboard', page=pages, **request.args) }}">{{ pages }}</a>
                </li>
            {% endif %}
            
            <li class="page-item {{ 'disabled' if page == pages else '' }}">
                <a class="page-link" href="{{ url_for('dashboard', page=page+1, **request.args) if page < pages else '#' }}">下一页</a>
            </li>
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
<script>
// 注册数据标签插件
Chart.register(ChartDataLabels);

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 视图切换按钮事件绑定
    document.getElementById('viewCompany').addEventListener('click', function() {
        // 获取当前URL参数并保留
        let currentParams = new URLSearchParams(window.location.search);
        // 更新视图参数
        currentParams.set('view_mode', 'company');
        // 重定向
        window.location.href = "{{ url_for('dashboard') }}" + "?" + currentParams.toString();
    });
    
    document.getElementById('viewDepartment').addEventListener('click', function() {
        // 获取当前URL参数并保留
        let currentParams = new URLSearchParams(window.location.search);
        // 更新视图参数
        currentParams.set('view_mode', 'department');
        // 重定向
        window.location.href = "{{ url_for('dashboard') }}" + "?" + currentParams.toString();
    });
    
    document.getElementById('viewUser').addEventListener('click', function() {
        // 获取当前URL参数并保留
        let currentParams = new URLSearchParams(window.location.search);
        // 更新视图参数
        currentParams.set('view_mode', 'user');
        // 重定向
        window.location.href = "{{ url_for('dashboard') }}" + "?" + currentParams.toString();
    });
    
    // 排序按钮事件绑定
    document.getElementById('sortByTotal').addEventListener('click', function() {
        // 获取当前URL参数并保留
        let currentParams = new URLSearchParams(window.location.search);
        // 更新排序参数
        currentParams.set('sort_by', 'total');
        currentParams.set('sort_order', '{{ 'asc' if sort_by == 'total' and sort_order == 'desc' else 'desc' }}');
        // 重定向
        window.location.href = "{{ url_for('dashboard') }}" + "?" + currentParams.toString();
    });
    
    document.getElementById('sortByDealDone').addEventListener('click', function() {
        // 获取当前URL参数并保留
        let currentParams = new URLSearchParams(window.location.search);
        // 更新排序参数
        currentParams.set('sort_by', 'deal_done');
        currentParams.set('sort_order', '{{ 'asc' if sort_by == 'deal_done' and sort_order == 'desc' else 'desc' }}');
        // 重定向
        window.location.href = "{{ url_for('dashboard') }}" + "?" + currentParams.toString();
    });
    
    document.getElementById('sortByRate').addEventListener('click', function() {
        // 获取当前URL参数并保留
        let currentParams = new URLSearchParams(window.location.search);
        // 更新排序参数
        currentParams.set('sort_by', 'conversion_rate');
        currentParams.set('sort_order', '{{ 'asc' if sort_by == 'conversion_rate' and sort_order == 'desc' else 'desc' }}');
        // 重定向
        window.location.href = "{{ url_for('dashboard') }}" + "?" + currentParams.toString();
    });
    
    // 筛选公司下拉框变更事件
    document.getElementById('filterCompany').addEventListener('change', function() {
        // 获取当前URL参数并保留
        let currentParams = new URLSearchParams(window.location.search);
        // 更新公司筛选参数
        if (this.value) {
            currentParams.set('company_id', this.value);
            // 清除部门筛选
            currentParams.delete('department_id');
            // 显示部门筛选组
            document.getElementById('departmentFilterGroup').style.display = '';
        } else {
            // 清除公司和部门筛选
            currentParams.delete('company_id');
            currentParams.delete('department_id');
            // 隐藏部门筛选组
            document.getElementById('departmentFilterGroup').style.display = 'none';
        }
        // 重定向
        window.location.href = "{{ url_for('dashboard') }}" + "?" + currentParams.toString();
    });
    
    // 筛选部门下拉框变更事件
    document.getElementById('filterDepartment').addEventListener('change', function() {
        // 获取当前URL参数并保留
        let currentParams = new URLSearchParams(window.location.search);
        // 更新部门筛选参数
        if (this.value) {
            currentParams.set('department_id', this.value);
        } else {
            currentParams.delete('department_id');
        }
        // 重定向
        window.location.href = "{{ url_for('dashboard') }}" + "?" + currentParams.toString();
    });

    // 准备图表数据
    // 1. 销售漏斗数据
    var funnelLabels = ['总线索', '已拨电话', '有效通话', '添加微信', '意向客户', '信息合规', '确认到面', '完成签约', '完成提车'];
    var funnelValues = [
        {{ total_leads|default(0) }},
        {{ called_leads|default(0) }},
        {{ valid_call_leads|default(0) }},
        {{ wechat_added_leads|default(0) }},
        {{ intentional_leads|default(0) }},
        {{ compliant_leads|default(0) }},
        {{ visited_leads|default(0) }},
        {{ deal_done_leads|default(0) }},
        {{ car_selected_leads|default(0) }}
    ];
    
    // 2. 每日新增线索数据
    var dailyLabels = {{ daily_leads_labels|default('[]')|safe }};
    var dailyValues = {{ daily_leads_data|default('[]')|safe }};
    
    // 3. 线索来源分布数据
    var sourceLabels = {{ source_labels|default('[]')|safe }};
    var sourceValues = {{ source_counts|default('[]')|safe }};
    
    // 4. 转化率数据
    var conversionLabels = ['已拨电话', '有效通话', '添加微信', '意向客户', '信息合规', '确认到面', '完成签约', '完成提车'];
    var conversionValues = [
        {{ (called_rate * 100)|round|int if (called_rate is defined and total_leads > 0) else 0 }},
        {{ (valid_call_rate * 100)|round|int if (valid_call_rate is defined and total_leads > 0) else 0 }},
        {{ (wechat_added_rate * 100)|round|int if (wechat_added_rate is defined and total_leads > 0) else 0 }},
        {{ (intentional_rate * 100)|round|int if (intentional_rate is defined and total_leads > 0) else 0 }},
        {{ (compliant_rate * 100)|round|int if (compliant_rate is defined and total_leads > 0) else 0 }},
        {{ (visited_rate * 100)|round|int if (visited_rate is defined and total_leads > 0) else 0 }},
        {{ (deal_rate * 100)|round|int if (deal_rate is defined and total_leads > 0) else 0 }},
        {{ (car_selected_rate * 100)|round|int if (car_selected_rate is defined and total_leads > 0) else 0 }}
    ];
    
    // 渲染销售漏斗图表
    var funnelCtx = document.getElementById('funnelChart').getContext('2d');
    var funnelColors = [
        'rgba(54, 162, 235, 0.8)',
        'rgba(75, 192, 192, 0.8)',
        'rgba(153, 102, 255, 0.8)',
        'rgba(255, 159, 64, 0.8)',
        'rgba(255, 99, 132, 0.8)',
        'rgba(255, 205, 86, 0.8)',
        'rgba(201, 203, 207, 0.8)',
        'rgba(54, 162, 235, 0.8)',
        'rgba(75, 192, 192, 0.8)'
    ];
    
    var funnelChart = new Chart(funnelCtx, {
        type: 'bar',
        data: {
            labels: funnelLabels,
            datasets: [{
                data: funnelValues,
                backgroundColor: funnelColors,
                borderWidth: 1
            }]
        },
        options: {
            indexAxis: 'y',
            plugins: {
                legend: {display: false},
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return '数量: ' + context.raw;
                        }
                    }
                },
                datalabels: {
                    color: '#000',
                    anchor: 'end',
                    align: 'end',
                    formatter: function(value) {
                        return value;
                    }
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    grid: {display: false}
                },
                y: {
                    grid: {display: false}
                }
            }
        }
    });

    // 渲染每日新增线索图表
    var dailyCtx = document.getElementById('dailyLeadsChart').getContext('2d');
    var dailyChart = new Chart(dailyCtx, {
        type: 'line',
        data: {
            labels: dailyLabels,
            datasets: [{
                label: '新增线索数',
                data: dailyValues,
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1,
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            plugins: {
                legend: {display: false},
                datalabels: {
                    color: '#000',
                    anchor: 'end',
                    align: 'top',
                    formatter: function(value) {
                        return value;
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {display: true}
                },
                x: {
                    grid: {display: false}
                }
            }
        }
    });

    // 渲染线索来源分布图表
    var sourceCtx = document.getElementById('sourceChart').getContext('2d');
    var sourceColors = [
        'rgba(255, 99, 132, 0.8)',
        'rgba(54, 162, 235, 0.8)',
        'rgba(255, 205, 86, 0.8)',
        'rgba(75, 192, 192, 0.8)',
        'rgba(153, 102, 255, 0.8)',
        'rgba(255, 159, 64, 0.8)',
        'rgba(201, 203, 207, 0.8)'
    ];
    
    var sourceChart = new Chart(sourceCtx, {
        type: 'doughnut',
        data: {
            labels: sourceLabels,
            datasets: [{
                data: sourceValues,
                backgroundColor: sourceColors,
                borderWidth: 1
            }]
        },
        options: {
            plugins: {
                legend: {
                    position: 'right'
                },
                datalabels: {
                    color: '#fff',
                    formatter: function(value, context) {
                        return context.chart.data.labels[context.dataIndex] + ': ' + value;
                    }
                }
            }
        }
    });

    // 渲染转化率图表
    var conversionCtx = document.getElementById('conversionRateChart').getContext('2d');
    var conversionChart = new Chart(conversionCtx, {
        type: 'bar',
        data: {
            labels: conversionLabels,
            datasets: [{
                label: '转化率',
                data: conversionValues,
                backgroundColor: 'rgba(75, 192, 192, 0.5)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }]
        },
        options: {
            plugins: {
                legend: {display: false},
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.raw + '%';
                        }
                    }
                },
                datalabels: {
                    color: '#000',
                    anchor: 'end',
                    align: 'top',
                    formatter: function(value) {
                        return value + '%';
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            }
        }
    });
});
</script>
{% endblock %} 