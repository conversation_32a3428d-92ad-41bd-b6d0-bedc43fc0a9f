from flask_wtf.csrf import CSRFProtect  # 导入CSRF保护

def setup_csrf(app):  # 配置CSRF保护
    app.config['WTF_CSRF_ENABLED'] = True
    app.config['WTF_CSRF_SECRET_KEY'] = app.config['SECRET_KEY']
    app.config['WTF_CSRF_TIME_LIMIT'] = 3600
    app.config['WTF_CSRF_SSL_STRICT'] = False
    app.config['WTF_CSRF_CHECK_DEFAULT'] = True
    
    csrf = CSRFProtect()
    csrf.init_app(app)
    return csrf

def add_csrf_header(response, app):  # 确保请求中包含CSRF令牌
    if 'text/html' in response.content_type:
        csrf_token = app.jinja_env.globals['csrf_token']()
        response.set_cookie('X-CSRFToken', csrf_token)
    return response 