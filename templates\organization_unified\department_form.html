{% extends 'base.html' %}

{% block title %}{{ '编辑部门' if action == 'edit' else '添加部门' }} - CRM系统{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-1">
                                <i class="fas fa-{{ 'edit' if action == 'edit' else 'plus' }}"></i> 
                                {{ '编辑部门' if action == 'edit' else '添加部门' }}
                            </h1>
                            <p class="text-muted mb-0">{{ '修改部门信息' if action == 'edit' else '创建新的部门' }}</p>
                        </div>
                        <div class="text-end">
                            <a href="{{ url_for('organization_unified.departments') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> 返回部门列表
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 表单 -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-users-cog"></i> 部门信息
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" novalidate data-skip-validation="true">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <!-- 部门名称 -->
                            <div class="col-md-6 mb-3">
                                {{ form.name.label(class="form-label required") }}
                                {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                {% if form.name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.name.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">请输入部门名称，如：销售部、技术部等</div>
                            </div>

                            <!-- 部门编码 -->
                            <div class="col-md-6 mb-3">
                                {{ form.code.label(class="form-label") }}
                                {{ form.code(class="form-control" + (" is-invalid" if form.code.errors else "")) }}
                                {% if form.code.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.code.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">可选，部门的唯一编码</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- 所属公司 -->
                            <div class="col-md-6 mb-3">
                                {{ form.company_id.label(class="form-label required") }}
                                {{ form.company_id(class="form-select" + (" is-invalid" if form.company_id.errors else "")) }}
                                {% if form.company_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.company_id.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">选择部门所属的公司</div>
                            </div>

                            <!-- 部门类型 -->
                            <div class="col-md-6 mb-3">
                                {{ form.type.label(class="form-label") }}
                                {{ form.type(class="form-select" + (" is-invalid" if form.type.errors else "")) }}
                                {% if form.type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.type.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">选择部门类型</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- 上级部门 -->
                            <div class="col-md-6 mb-3">
                                {{ form.parent_id.label(class="form-label") }}
                                {{ form.parent_id(class="form-select" + (" is-invalid" if form.parent_id.errors else "")) }}
                                {% if form.parent_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.parent_id.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">可选，如果是子部门请选择上级部门</div>
                            </div>

                            <!-- 部门负责人 -->
                            <div class="col-md-6 mb-3">
                                {% if form.manager_id is defined %}
                                {{ form.manager_id.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.manager_id(class="form-select" + (" is-invalid" if form.manager_id.errors else "")) }}
                                    <button type="button" class="btn btn-outline-primary" id="addNewDeptManagerBtn">
                                        <i class="fas fa-plus"></i> 新建员工
                                    </button>
                                </div>
                                {% if form.manager_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.manager_id.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">可选，选择部门负责人或新建员工作为负责人</div>
                                {% else %}
                                <label class="form-label">部门负责人</label>
                                <div class="input-group">
                                    <select class="form-select" name="manager_id" id="manager_id">
                                        <option value="">请选择负责人</option>
                                        <!-- 动态加载员工选项 -->
                                    </select>
                                    <button type="button" class="btn btn-outline-primary" id="addNewDeptManagerBtn">
                                        <i class="fas fa-plus"></i> 新建员工
                                    </button>
                                </div>
                                <div class="form-text">可选，选择部门负责人或新建员工作为负责人</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- 部门描述 -->
                        <div class="mb-3">
                            {% if form.description is defined %}
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows="3") }}
                            {% if form.description.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.description.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            {% else %}
                            <label class="form-label">部门描述</label>
                            <textarea class="form-control" name="description" rows="3" placeholder="描述部门的职能和职责"></textarea>
                            {% endif %}
                            <div class="form-text">可选，描述部门的职能和职责</div>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ url_for('organization_unified.departments') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> {{ '更新部门' if action == 'edit' else '创建部门' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 新建部门负责人模态框 -->
<div class="modal fade" id="newDeptManagerModal" tabindex="-1" aria-labelledby="newDeptManagerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newDeptManagerModalLabel">
                    <i class="fas fa-user-plus"></i> 新建部门负责人
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="newDeptManagerForm">
                    <div class="mb-3">
                        <label for="newDeptManagerName" class="form-label">姓名 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="newDeptManagerName" required>
                    </div>
                    <div class="mb-3">
                        <label for="newDeptManagerUsername" class="form-label">用户名 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="newDeptManagerUsername" required>
                        <div class="form-text">用于登录系统的用户名</div>
                    </div>
                    <div class="mb-3">
                        <label for="newDeptManagerEmail" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="newDeptManagerEmail">
                    </div>
                    <div class="mb-3">
                        <label for="newDeptManagerPhone" class="form-label">手机号</label>
                        <input type="tel" class="form-control" id="newDeptManagerPhone">
                    </div>
                    <div class="mb-3">
                        <label for="newDeptManagerPassword" class="form-label">初始密码</label>
                        <input type="password" class="form-control" id="newDeptManagerPassword" value="123456">
                        <div class="form-text">默认密码为123456，用户首次登录后可修改</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveNewDeptManagerBtn">
                    <i class="fas fa-save"></i> 保存并选择
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 公司选择变化时更新上级部门选项
document.getElementById('company_id').addEventListener('change', function() {
    const companyId = this.value;
    const parentSelect = document.getElementById('parent_id');
    const managerSelect = document.querySelector('[name="manager_id"]') || document.getElementById('manager_id');

    // 清空选项
    parentSelect.innerHTML = '<option value="0">无</option>';
    if (managerSelect) {
        managerSelect.innerHTML = '<option value="0">请选择负责人</option>';
    }

    if (companyId) {
        // 通过AJAX加载对应公司的部门和员工
        loadDepartmentsByCompany(companyId);
        loadEmployeesByCompany(companyId);
    }
});

// 新建部门负责人按钮点击事件
document.getElementById('addNewDeptManagerBtn').addEventListener('click', function() {
    const companyId = document.getElementById('company_id').value;
    if (!companyId) {
        alert('请先选择所属公司');
        return;
    }

    const modal = new bootstrap.Modal(document.getElementById('newDeptManagerModal'));
    modal.show();
});

// 保存新建部门负责人
document.getElementById('saveNewDeptManagerBtn').addEventListener('click', function() {
    const form = document.getElementById('newDeptManagerForm');
    const companyId = document.getElementById('company_id').value;

    // 获取表单数据
    const name = document.getElementById('newDeptManagerName').value.trim();
    const username = document.getElementById('newDeptManagerUsername').value.trim();
    const email = document.getElementById('newDeptManagerEmail').value.trim();
    const phone = document.getElementById('newDeptManagerPhone').value.trim();
    const password = document.getElementById('newDeptManagerPassword').value || '123456';

    // 基本验证
    if (!name) {
        alert('请输入姓名');
        document.getElementById('newDeptManagerName').focus();
        return;
    }

    if (!username) {
        alert('请输入用户名');
        document.getElementById('newDeptManagerUsername').focus();
        return;
    }

    if (!companyId) {
        alert('请先选择所属公司');
        return;
    }

    // 将新建负责人信息存储到隐藏字段中，等部门创建成功后再处理
    const managerData = {
        name: name,
        username: username,
        email: email,
        phone: phone,
        password: password,
        company_id: companyId,
        role_code: 'department_admin'
    };

    // 创建隐藏字段存储新建负责人数据
    let hiddenField = document.getElementById('new_dept_manager_data');
    if (!hiddenField) {
        hiddenField = document.createElement('input');
        hiddenField.type = 'hidden';
        hiddenField.id = 'new_dept_manager_data';
        hiddenField.name = 'new_dept_manager_data';
        document.querySelector('form').appendChild(hiddenField);
    }
    hiddenField.value = JSON.stringify(managerData);

    // 添加到负责人选择框（临时显示）
    const managerSelect = document.querySelector('[name="manager_id"]') || document.getElementById('manager_id');
    const option = document.createElement('option');
    option.value = 'NEW_DEPT_MANAGER';  // 特殊标识
    option.textContent = `${name} (${username}) - 待创建`;
    option.selected = true;
    managerSelect.appendChild(option);

    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('newDeptManagerModal'));
    modal.hide();

    // 清空表单
    form.reset();
    document.getElementById('newDeptManagerPassword').value = '123456';

    alert('部门负责人信息已保存，将在部门创建成功后自动创建');
});

async function loadDepartmentsByCompany(companyId) {
    try {
        const response = await fetch(`/api/companies/${companyId}/departments`);
        const departments = await response.json();

        const parentSelect = document.getElementById('parent_id');
        departments.forEach(dept => {
            const option = document.createElement('option');
            option.value = dept.id;
            option.textContent = dept.name;
            parentSelect.appendChild(option);
        });
    } catch (error) {
        console.error('加载部门列表失败:', error);
    }
}

async function loadEmployeesByCompany(companyId) {
    try {
        const response = await fetch(`/api/companies/${companyId}/employees`);
        const employees = await response.json();

        const managerSelect = document.querySelector('[name="manager_id"]');
        if (managerSelect) {
            employees.forEach(emp => {
                const option = document.createElement('option');
                option.value = emp.id;
                option.textContent = emp.name || emp.username;
                managerSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载员工列表失败:', error);
    }
}

// 表单验证
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const companyId = document.getElementById('company_id').value;
    
    if (!name) {
        e.preventDefault();
        alert('请输入部门名称');
        document.getElementById('name').focus();
        return;
    }
    
    if (!companyId) {
        e.preventDefault();
        alert('请选择所属公司');
        document.getElementById('company_id').focus();
        return;
    }
});

// 页面加载时触发公司选择事件
document.addEventListener('DOMContentLoaded', function() {
    const companySelect = document.getElementById('company_id');
    if (companySelect.value) {
        companySelect.dispatchEvent(new Event('change'));
    }
});
</script>
{% endblock %}
