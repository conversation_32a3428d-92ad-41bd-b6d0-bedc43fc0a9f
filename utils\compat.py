# -*- coding: utf-8 -*-
import sys
import os
import io

# Python 2/3 兼容性
PY2 = sys.version_info[0] == 2

if PY2:
    string_types = (basestring,)
    text_type = unicode
    binary_type = str
else:
    string_types = (str,)
    text_type = str
    binary_type = bytes

def to_text(value, encoding='utf-8', errors='strict'):
    """将值转换为文本类型"""
    if isinstance(value, text_type):
        return value
    if isinstance(value, binary_type):
        return value.decode(encoding, errors)
    return text_type(value)

def to_binary(value, encoding='utf-8', errors='strict'):
    """将值转换为二进制类型"""
    if isinstance(value, binary_type):
        return value
    if isinstance(value, text_type):
        return value.encode(encoding, errors)
    return binary_type(value)

def safe_makedirs(path):
    """安全创建目录"""
    if not os.path.exists(path):
        os.makedirs(path)

def safe_open(filename, mode='r', encoding=None):
    """安全打开文件"""
    if PY2:
        return io.open(filename, mode=mode, encoding=encoding)
    return open(filename, mode=mode, encoding=encoding)

def safe_unicode(value):
    """安全转换为 unicode"""
    if PY2:
        if isinstance(value, str):
            return value.decode('utf-8')
        return unicode(value)
    return str(value)

def safe_str(value):
    """安全转换为 str"""
    if PY2:
        if isinstance(value, unicode):
            return value.encode('utf-8')
        return str(value)
    return str(value) 