#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一组织管理路由
整合公司、部门、员工管理功能
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import db, Company, Department, User, Role
from services.organization_service import OrganizationService
from forms import CompanyForm, DepartmentForm, UserForm
from werkzeug.security import generate_password_hash

bp = Blueprint('organization_unified', __name__, url_prefix='/organization')

# 权限装饰器
def require_permission(permission):
    """权限检查装饰器"""
    def decorator(f):
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                flash('请先登录', 'warning')
                return redirect(url_for('auth.login'))
            
            if not current_user.has_permission(permission):
                flash('您没有权限访问此功能', 'danger')
                return redirect(url_for('index'))
            
            return f(*args, **kwargs)
        decorated_function.__name__ = f.__name__
        return decorated_function
    return decorator

@bp.route('/')
@login_required
def index():
    """组织管理统一入口页面"""
    try:
        # 获取组织架构树
        org_tree = OrganizationService.get_organization_tree()

        # 获取用户权限范围
        scope = OrganizationService.get_user_scope()

        return render_template('organization_unified/unified_index.html',
                             org_tree=org_tree,
                             scope=scope)
    except Exception as e:
        flash(f'获取组织信息失败: {str(e)}', 'danger')
        return redirect(url_for('index'))

@bp.route('/unified')
@login_required
@require_permission('organization_view')
def unified_management():
    """统一组织管理页面"""
    try:
        scope = OrganizationService.get_user_scope()

        # 计算统计信息
        scope['managers_count'] = sum(1 for user in scope['users'] if user.is_manager)
        scope['active_users_count'] = sum(1 for user in scope['users'] if user.status == 'active')
        scope['performance_users_count'] = sum(1 for user in scope['users'] if not user.should_exclude_from_ranking)

        return render_template('organization_unified/unified_management.html', scope=scope)
    except Exception as e:
        flash(f'获取组织信息失败: {str(e)}', 'danger')
        return redirect(url_for('main.index'))

@bp.route('/legacy')
@login_required
def legacy_index():
    """原来的组织管理总览页面（保留作为备用）"""
    try:
        # 获取组织架构树
        org_tree = OrganizationService.get_organization_tree()

        # 获取统计信息
        company_stats = OrganizationService.get_company_statistics()
        department_stats = OrganizationService.get_department_statistics()
        user_stats = OrganizationService.get_user_statistics()

        # 获取用户权限范围
        scope = OrganizationService.get_user_scope()

        return render_template('organization_unified/index.html',
                             org_tree=org_tree,
                             company_stats=company_stats,
                             department_stats=department_stats,
                             user_stats=user_stats,
                             scope=scope)
    except Exception as e:
        flash(f'获取组织信息失败: {str(e)}', 'danger')
        return redirect(url_for('index'))

@bp.route('/api/tree')
@login_required
def organization_tree():
    """获取组织架构树数据（API）- 支持平台架构"""
    try:
        scope = OrganizationService.get_user_scope()
        tree_data = []

        # 如果是超级管理员，显示平台架构
        if current_user.role.code == 'super_admin':
            # 平台级别节点
            platform_node = {
                'id': 'platform',
                'name': '平台管理',
                'code': 'PLATFORM',
                'node_type': 'platform',
                'type': 'platform',
                'icon': 'fas fa-globe',
                'children': [],
                'stats': {
                    'companies': 0,
                    'total_users': 0
                }
            }

            # 添加平台级用户（super_admin等，company_id为None的用户）
            platform_users = [u for u in scope['users'] if u.company_id is None]
            for user in platform_users:
                user_node = {
                    'id': user.id,
                    'name': user.name,
                    'username': user.username,
                    'node_type': 'user',
                    'type': 'user',
                    'icon': 'fas fa-user-shield',  # 平台管理员使用盾牌图标
                    'children': [],
                    'stats': {
                        'role': user.role.name if user.role else '',
                        'email': user.email,
                        'phone': user.phone,
                        'status': user.status,
                        'level': 'platform'  # 标记为平台级别
                    }
                }
                platform_node['children'].append(user_node)
                platform_node['stats']['total_users'] += 1

            # 添加所有公司作为平台的子节点
            for company in scope['companies']:
                company_node = build_company_node(company, scope)
                platform_node['children'].append(company_node)
                platform_node['stats']['companies'] += 1
                platform_node['stats']['total_users'] += company_node['stats']['employees']

            tree_data.append(platform_node)

        else:
            # 非超级管理员，只显示其有权限的公司
            for company in scope['companies']:
                company_node = build_company_node(company, scope)
                tree_data.append(company_node)

        # 获取权限信息
        permissions = OrganizationService.get_user_tree_permissions()

        # 返回前端期望的格式
        response_data = {
            'companies': tree_data,  # 对于超级管理员，这里包含平台节点
            'permissions': permissions,
            'total_companies': len(scope['companies']),
            'total_departments': sum(len([d for d in scope['departments'] if d.company_id == c.id]) for c in scope['companies']),
            'total_users': len(scope['users'])
        }

        return jsonify(response_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def build_company_node(company, scope):
    """构建公司节点"""
    # 获取公司管理员（department_id为None的用户）
    company_managers = [u for u in scope['users'] if u.company_id == company.id and u.department_id is None]

    # 构建公司名称，包含管理员信息
    company_name = company.name
    if company_managers:
        manager_names = [f"{u.name}({u.role.name if u.role else ''})" for u in company_managers]
        company_name += f" - {', '.join(manager_names)}"

    company_node = {
        'id': company.id,
        'name': company_name,
        'code': company.code,
        'node_type': 'company',
        'type': 'company',
        'icon': 'fas fa-building',
        'children': [],
        'stats': {
            'departments': 0,
            'employees': len(company_managers),  # 先计算管理员数量
            'manager_count': len(company_managers)  # 只保存数量，不保存对象
        }
    }

    # 添加部门节点（不再单独添加公司管理员节点）
    company_departments = [d for d in scope['departments'] if d.company_id == company.id and d.parent_id is None]
    for dept in company_departments:
        dept_node = build_department_node(dept, scope)
        company_node['children'].append(dept_node)
        company_node['stats']['departments'] += 1
        company_node['stats']['employees'] += dept_node['stats']['total_employees']

    return company_node

def build_department_node(department, scope):
    """构建部门节点"""
    # 获取部门所有员工
    dept_users = [u for u in scope['users'] if u.department_id == department.id]

    # 分离部门管理员和普通员工
    dept_managers = [u for u in dept_users if u.role and u.role.code == 'department_admin']
    regular_employees = [u for u in dept_users if not u.role or u.role.code != 'department_admin']

    # 构建部门名称，包含管理员信息
    dept_name = department.name
    if dept_managers:
        manager_names = [f"{u.name}({u.role.name if u.role else ''})" for u in dept_managers]
        dept_name += f" - {', '.join(manager_names)}"

    dept_node = {
        'id': department.id,
        'name': dept_name,
        'code': department.code,
        'company_id': department.company_id,  # 添加公司ID字段
        'parent_id': department.parent_id,    # 添加父部门ID字段
        'node_type': 'department',
        'type': 'department',
        'icon': 'fas fa-users-cog',
        'children': [],
        'stats': {
            'direct_employees': len(dept_users),
            'total_employees': len(dept_users),
            'manager_count': len(dept_managers)  # 只保存数量，不保存对象
        }
    }

    # 只添加普通员工节点（不包括部门管理员）
    for user in regular_employees:
        user_node = {
            'id': user.id,
            'name': user.name,
            'username': user.username,
            'node_type': 'user',
            'type': 'user',
            'icon': 'fas fa-user',
            'children': [],
            'stats': {
                'role': user.role.name if user.role else '',
                'email': user.email,
                'phone': user.phone,
                'status': user.status
            }
        }
        dept_node['children'].append(user_node)

    # 添加子部门
    child_departments = [d for d in scope['departments'] if d.parent_id == department.id]
    for child_dept in child_departments:
        child_node = build_department_node(child_dept, scope)
        dept_node['children'].append(child_node)
        dept_node['stats']['total_employees'] += child_node['stats']['total_employees']

    return dept_node







@bp.route('/check-permissions')
@login_required
def check_permissions():
    """检查当前用户权限"""
    try:
        user_info = {
            'username': current_user.username,
            'role': current_user.role.name if current_user.role else 'None',
            'role_code': current_user.role.code if current_user.role else 'None',
            'company_id': current_user.company_id,
            'department_id': current_user.department_id,
        }

        permissions = []
        if hasattr(current_user, 'has_permission'):
            test_permissions = ['user_manage', 'company_manage', 'department_manage', 'lead_manage']
            for perm in test_permissions:
                has_perm = current_user.has_permission(perm)
                permissions.append(f"{perm}: {has_perm}")
        else:
            permissions.append("has_permission method not found")

        html = f"""
        <h1>用户权限检查</h1>
        <h2>用户信息</h2>
        <ul>
            <li>用户名: {user_info['username']}</li>
            <li>角色: {user_info['role']}</li>
            <li>角色代码: {user_info['role_code']}</li>
            <li>公司ID: {user_info['company_id']}</li>
            <li>部门ID: {user_info['department_id']}</li>
        </ul>

        <h2>权限检查</h2>
        <ul>
            {''.join([f'<li>{perm}</li>' for perm in permissions])}
        </ul>

        <p><a href="/organization/employees/add">测试添加员工页面</a></p>
        """

        return html

    except Exception as e:
        return f"<h1>错误</h1><p>{str(e)}</p>"



@bp.route('/test-api')
@login_required
def test_api():
    """测试API页面"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>API测试</title>
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    </head>
    <body>
        <h1>API测试页面</h1>

        <div>
            <label>选择公司:</label>
            <select id="companySelect">
                <option value="1">默认公司</option>
                <option value="2">广西海逸教育集团</option>
            </select>
            <button onclick="testAPI()">测试获取部门</button>
        </div>

        <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;"></div>

        <script>
        function testAPI() {
            const companyId = document.getElementById('companySelect').value;
            const resultDiv = document.getElementById('result');

            resultDiv.innerHTML = '正在请求...';

            $.ajax({
                url: `/organization_api/companies/${companyId}/departments`,
                method: 'GET',
                success: function(data) {
                    resultDiv.innerHTML = `
                        <h3>请求成功 (${data.length} 个部门)</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                },
                error: function(xhr, status, error) {
                    resultDiv.innerHTML = `
                        <h3>请求失败</h3>
                        <p>状态码: ${xhr.status}</p>
                        <p>错误信息: ${error}</p>
                        <p>响应内容: ${xhr.responseText}</p>
                    `;
                }
            });
        }

        // 页面加载时自动测试
        $(document).ready(function() {
            testAPI();
        });
        </script>
    </body>
    </html>
    """

@bp.route('/api/node/<node_type>/<int:node_id>')
@login_required
def get_node_details(node_type, node_id):
    """获取节点详细信息（API）"""
    try:
        details = OrganizationService.get_node_details(node_type, node_id)
        if not details:
            return jsonify({'error': '节点不存在或无权限访问'}), 404
        return jsonify(details)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@bp.route('/api/node/<node_type>/<int:node_id>', methods=['PUT'])
@login_required
def update_node(node_type, node_id):
    """更新节点信息（API）"""
    try:
        data = request.get_json()

        if node_type == 'company':
            return update_company_api(node_id, data)
        elif node_type == 'department':
            return update_department_api(node_id, data)
        elif node_type == 'user':
            return update_user_api(node_id, data)
        else:
            return jsonify({'error': '不支持的节点类型'}), 400

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@bp.route('/api/node/<node_type>/<int:node_id>', methods=['DELETE'])
@login_required
def delete_node(node_type, node_id):
    """删除节点（API）"""
    try:
        if node_type == 'company':
            return delete_company_api(node_id)
        elif node_type == 'department':
            return delete_department_api(node_id)
        elif node_type == 'user':
            return delete_user_api(node_id)
        else:
            return jsonify({'error': '不支持的节点类型'}), 400

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@bp.route('/api/node/<node_type>', methods=['POST'])
@login_required
def create_node(node_type):
    """创建新节点（API）"""
    try:
        data = request.get_json()

        if node_type == 'company':
            return create_company_api(data)
        elif node_type == 'department':
            return create_department_api(data)
        elif node_type == 'user':
            return create_user_api(data)
        else:
            return jsonify({'error': '不支持的节点类型'}), 400

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# ==================== 临时管理员创建API ====================

@bp.route('/api/users/create-temp-manager', methods=['POST'])
@login_required
def create_temp_manager():
    """创建临时管理员（用于公司/部门创建时）"""
    try:
        data = request.get_json()

        # 权限检查
        if not current_user.has_permission('user_manage'):
            return jsonify({'error': '您没有权限创建用户'}), 403

        # 检查必填字段
        if not data.get('name') or not data.get('username'):
            return jsonify({'error': '姓名和用户名为必填项'}), 400

        # 检查用户名唯一性
        existing_user = User.query.filter_by(username=data['username']).first()
        if existing_user:
            return jsonify({'error': '用户名已存在'}), 400

        # 检查邮箱唯一性（如果提供）
        if data.get('email'):
            existing_email = User.query.filter_by(email=data['email']).first()
            if existing_email:
                return jsonify({'error': '邮箱已存在'}), 400

        # 检查手机号唯一性（如果提供）
        if data.get('phone'):
            existing_phone = User.query.filter_by(phone=data['phone']).first()
            if existing_phone:
                return jsonify({'error': '手机号已存在'}), 400

        # 获取角色
        role_code = data.get('role_code', 'company_admin')
        role = Role.query.filter_by(code=role_code).first()
        if not role:
            return jsonify({'error': f'角色 {role_code} 不存在'}), 400

        # 创建用户
        user = User(
            username=data['username'],
            name=data['name'],
            email=data.get('email', ''),
            phone=data.get('phone', ''),
            company_id=data.get('company_id'),  # 可能为空，稍后设置
            department_id=data.get('department_id'),  # 可能为空，稍后设置
            role_id=role.id,
            password_hash=generate_password_hash(data.get('password', '123456')),
            status='active'
        )

        db.session.add(user)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '临时管理员创建成功',
            'user_id': user.id,
            'username': user.username,
            'name': user.name
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'创建临时管理员失败: {str(e)}'}), 500

# ==================== 公司管理 ====================

@bp.route('/companies')
@login_required
@require_permission('organization_view')
def companies():
    """公司管理页面"""
    try:
        companies = OrganizationService.get_company_statistics()
        scope = OrganizationService.get_user_scope()

        # 确保权限检查一致性
        if not scope.get('can_manage_companies', False) and current_user.role.code not in ['super_admin', 'company_admin']:
            flash('您没有权限访问公司管理', 'danger')
            return redirect(url_for('organization_unified.index'))

        return render_template('organization_unified/companies.html',
                             companies=companies,
                             scope=scope)
    except Exception as e:
        flash(f'获取公司列表失败: {str(e)}', 'danger')
        return redirect(url_for('organization_unified.index'))

@bp.route('/companies/add', methods=['GET', 'POST'])
@login_required
@require_permission('company_manage')
def add_company():
    """添加公司"""
    form = CompanyForm()

    # 设置上级公司选项
    headquarters = Company.query.filter_by(type='headquarters').all()
    form.parent_id.choices = [(0, '无')] + [(c.id, c.name) for c in headquarters]

    # 设置管理员选项
    from models import User
    # 获取可以作为公司管理员的用户（超级管理员和公司管理员）
    potential_managers = User.query.join(Role).filter(
        Role.code.in_(['super_admin', 'company_admin'])
    ).all()
    form.manager_id.choices = [(0, '请选择管理员')] + [(u.id, f"{u.name} ({u.username})") for u in potential_managers]

    # 添加调试信息
    if request.method == 'POST':
        print(f"=== 公司添加表单提交 ===")
        print(f"表单数据: {dict(request.form)}")

        # 如果是总部类型，确保parent_id设为0
        if request.form.get('type') == 'headquarters':
            form.parent_id.data = 0

        # 如果选择了新建管理员，临时修改表单数据以通过验证
        if request.form.get('manager_id') == 'NEW_MANAGER':
            # 创建一个可变的表单数据副本
            form_data = request.form.to_dict()
            form_data['manager_id'] = '0'  # 临时设置为0以通过验证

            # 重新创建表单对象
            from werkzeug.datastructures import MultiDict
            form = CompanyForm(MultiDict(form_data))

            # 重新设置选项
            headquarters = Company.query.filter_by(type='headquarters').all()
            form.parent_id.choices = [(0, '无')] + [(c.id, c.name) for c in headquarters]

            potential_managers = User.query.join(Role).filter(
                Role.code.in_(['super_admin', 'company_admin'])
            ).all()
            form.manager_id.choices = [(0, '请选择管理员')] + [(u.id, f"{u.name} ({u.username})") for u in potential_managers]

        print(f"表单验证结果: {form.validate()}")
        if not form.validate():
            print(f"表单验证错误: {form.errors}")

    if form.validate_on_submit():
        try:
            print(f"=== 开始创建公司 ===")
            print(f"公司名称: {form.name.data}")
            print(f"公司代码: {form.code.data}")

            # 检查编码唯一性
            existing = Company.query.filter_by(code=form.code.data).first()
            if existing:
                print(f"公司编码已存在: {existing.name}")
                flash('公司编码已存在', 'danger')
                return render_template('organization_unified/company_form.html', form=form, action='add')

            # 处理新建管理员数据
            new_manager_id = None
            new_manager_data = request.form.get('new_manager_data')
            original_manager_id = request.form.get('manager_id')
            if new_manager_data and original_manager_id == 'NEW_MANAGER':
                try:
                    import json
                    manager_info = json.loads(new_manager_data)
                    print(f"创建新管理员: {manager_info}")

                    # 检查用户名唯一性
                    existing_user = User.query.filter_by(username=manager_info['username']).first()
                    if existing_user:
                        flash(f"用户名 {manager_info['username']} 已存在", 'danger')
                        return render_template('organization_unified/company_form.html', form=form, action='add')

                    # 检查邮箱唯一性（如果提供）
                    if manager_info.get('email'):
                        existing_email = User.query.filter_by(email=manager_info['email']).first()
                        if existing_email:
                            flash(f"邮箱 {manager_info['email']} 已存在", 'danger')
                            return render_template('organization_unified/company_form.html', form=form, action='add')

                    # 检查手机号唯一性（如果提供）
                    if manager_info.get('phone'):
                        existing_phone = User.query.filter_by(phone=manager_info['phone']).first()
                        if existing_phone:
                            flash(f"手机号 {manager_info['phone']} 已存在", 'danger')
                            return render_template('organization_unified/company_form.html', form=form, action='add')

                    # 获取公司管理员角色
                    role = Role.query.filter_by(code='company_admin').first()
                    if not role:
                        flash('公司管理员角色不存在', 'danger')
                        return render_template('organization_unified/company_form.html', form=form, action='add')

                    # 创建新管理员（暂时不设置公司ID）
                    new_manager = User(
                        username=manager_info['username'],
                        name=manager_info['name'],
                        email=manager_info.get('email', ''),
                        phone=manager_info.get('phone', ''),
                        role_id=role.id,
                        password_hash=generate_password_hash(manager_info.get('password', '123456')),
                        status='active'
                    )

                    db.session.add(new_manager)
                    db.session.flush()  # 获取新管理员ID
                    new_manager_id = new_manager.id
                    print(f"新管理员创建成功，ID: {new_manager_id}")

                except Exception as e:
                    db.session.rollback()  # 回滚会话
                    print(f"创建新管理员失败: {str(e)}")
                    flash(f'创建新管理员失败: {str(e)}', 'danger')
                    return render_template('organization_unified/company_form.html', form=form, action='add')

            # 创建公司
            company = Company(
                name=form.name.data,
                code=form.code.data,
                type=form.type.data,
                parent_id=form.parent_id.data if form.parent_id.data != 0 else None,
                manager_id=new_manager_id or (form.manager_id.data if form.manager_id.data != 0 else None),
                is_default=form.is_default.data
            )

            # 如果设置为默认公司，取消其他公司的默认标志
            if form.is_default.data:
                Company.query.filter_by(is_default=True).update({'is_default': False})

            db.session.add(company)
            db.session.flush()  # 获取公司ID

            # 如果有新创建的管理员，设置其公司归属
            if new_manager_id:
                new_manager = User.query.get(new_manager_id)
                new_manager.company_id = company.id
                print(f"为新创建的管理员设置公司ID: {company.id}")

            db.session.commit()

            print(f"=== 公司创建成功 ===")
            print(f"公司ID: {company.id}")
            flash('公司创建成功', 'success')
            return redirect(url_for('organization_unified.companies'))

        except Exception as e:
            db.session.rollback()
            print(f"=== 公司创建失败 ===")
            print(f"错误信息: {str(e)}")
            import traceback
            traceback.print_exc()
            flash(f'创建公司失败: {str(e)}', 'danger')
    else:
        if request.method == 'POST':
            print(f"=== 表单验证失败，返回表单页面 ===")

    return render_template('organization_unified/company_form.html', form=form, action='add')

@bp.route('/companies/<int:company_id>/edit', methods=['GET', 'POST'])
@login_required
@require_permission('company_manage')
def edit_company(company_id):
    """编辑公司"""
    company = Company.query.get_or_404(company_id)
    
    # 权限检查
    scope = OrganizationService.get_user_scope()
    if company not in scope['companies'] and not scope['can_manage_companies']:
        flash('您没有权限编辑此公司', 'danger')
        return redirect(url_for('organization_unified.companies'))
    
    # 先设置选项，再初始化表单
    # 设置上级公司选项（排除自己）
    headquarters = Company.query.filter(
        Company.id != company_id,
        Company.type == 'headquarters'
    ).all()

    # 设置管理员选项 - 获取该公司的所有员工，优先显示管理员角色
    from sqlalchemy import case
    company_users = User.query.filter_by(company_id=company_id).join(Role).order_by(
        case(
            (Role.code == 'super_admin', 1),
             (Role.code == 'company_admin', 2),
             (Role.code == 'department_admin', 3),
            else_=4
        ),
        User.name
    ).all()

    # 初始化表单
    form = CompanyForm(obj=company)

    # 设置选项
    form.parent_id.choices = [(0, '无')] + [(c.id, c.name) for c in headquarters]
    form.manager_id.choices = [(0, '请选择管理员')] + [(u.id, f"{u.name} ({u.role.name})") for u in company_users]
    
    if form.validate_on_submit():
        try:
            # 验证操作合法性
            is_valid, error_msg = OrganizationService.validate_company_operation(
                'edit', company_id, code=form.code.data
            )
            if not is_valid:
                flash(error_msg, 'danger')
                return render_template('organization_unified/company_form.html', 
                                     form=form, action='edit', company=company)
            
            # 更新公司信息
            company.name = form.name.data
            company.code = form.code.data
            company.type = form.type.data
            company.parent_id = form.parent_id.data if form.parent_id.data != 0 else None
            
            # 处理默认公司标志
            if form.is_default.data and not company.is_default:
                Company.query.filter_by(is_default=True).update({'is_default': False})
                company.is_default = True
            elif not form.is_default.data:
                company.is_default = False
            
            db.session.commit()
            flash('公司信息更新成功', 'success')
            return redirect(url_for('organization_unified.companies'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'更新公司失败: {str(e)}', 'danger')
    
    return render_template('organization_unified/company_form.html', 
                         form=form, action='edit', company=company)

@bp.route('/companies/<int:company_id>/delete', methods=['POST'])
@login_required
@require_permission('company_manage')
def delete_company(company_id):
    """删除公司"""
    try:
        # 验证操作合法性
        is_valid, error_msg = OrganizationService.validate_company_operation('delete', company_id)
        if not is_valid:
            flash(error_msg, 'danger')
            return redirect(url_for('organization_unified.companies'))

        company = Company.query.get_or_404(company_id)

        # 权限检查
        scope = OrganizationService.get_user_scope()
        if company not in scope['companies'] and not scope['can_manage_companies']:
            flash('您没有权限删除此公司', 'danger')
            return redirect(url_for('organization_unified.companies'))

        # 删除公司相关的所有数据（按依赖关系顺序）
        from models import Lead, Activity, LeadTransferHistory, LeadStatusHistory, LeadPool, Department, User

        # 1. 删除线索相关数据
        # 获取该公司的所有线索ID
        lead_ids = [lead.id for lead in Lead.query.filter_by(company_id=company_id).all()]
        if lead_ids:
            # 删除线索的活动记录
            Activity.query.filter(Activity.lead_id.in_(lead_ids)).delete(synchronize_session=False)
            # 删除线索的状态历史
            LeadStatusHistory.query.filter(LeadStatusHistory.lead_id.in_(lead_ids)).delete(synchronize_session=False)
            # 删除线索的转移历史
            LeadTransferHistory.query.filter(LeadTransferHistory.lead_id.in_(lead_ids)).delete(synchronize_session=False)
            # 删除线索
            Lead.query.filter_by(company_id=company_id).delete()

        # 2. 删除线索池
        LeadPool.query.filter_by(company_id=company_id).delete()

        # 3. 删除部门
        Department.query.filter_by(company_id=company_id).delete()

        # 4. 删除用户
        User.query.filter_by(company_id=company_id).delete()

        # 5. 最后删除公司
        db.session.delete(company)
        db.session.commit()

        flash('公司删除成功', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'删除公司失败: {str(e)}', 'danger')

    return redirect(url_for('organization_unified.companies'))

# ==================== 部门管理 ====================

@bp.route('/departments')
@login_required
@require_permission('organization_view')
def departments():
    """部门管理页面"""
    try:
        company_id = request.args.get('company_id', type=int)
        departments = OrganizationService.get_department_statistics(company_id)
        scope = OrganizationService.get_user_scope()

        # 确保权限检查一致性
        if not scope.get('can_manage_departments', False) and current_user.role.code not in ['super_admin', 'company_admin', 'department_admin']:
            flash('您没有权限访问部门管理', 'danger')
            return redirect(url_for('organization_unified.index'))

        return render_template('organization_unified/departments.html',
                             departments=departments,
                             scope=scope,
                             selected_company_id=company_id)
    except Exception as e:
        flash(f'获取部门列表失败: {str(e)}', 'danger')
        return redirect(url_for('organization_unified.index'))

@bp.route('/departments/add', methods=['GET', 'POST'])
@login_required
@require_permission('department_manage')
def add_department():
    """添加部门"""
    form = DepartmentForm()
    scope = OrganizationService.get_user_scope()
    
    # 设置公司选项
    form.company_id.choices = [(c.id, c.name) for c in scope['companies']]

    # 设置上级部门选项
    company_id = request.args.get('company_id', type=int) or (
        scope['companies'][0].id if scope['companies'] else None
    )
    if company_id:
        parent_depts = Department.query.filter_by(company_id=company_id).all()
        form.parent_id.choices = [(0, '无')] + [(d.id, d.name) for d in parent_depts]

        # 设置部门负责人选项
        employees = User.query.filter_by(company_id=company_id).all()
        form.manager_id.choices = [(0, '请选择负责人')] + [(u.id, u.name or u.username) for u in employees]
    else:
        form.parent_id.choices = [(0, '无')]
        form.manager_id.choices = [(0, '请选择负责人')]
    
    # 添加表单验证处理
    if request.method == 'POST':
        # 如果选择了新建负责人，临时修改表单数据以通过验证
        if request.form.get('manager_id') == 'NEW_DEPT_MANAGER':
            # 创建一个可变的表单数据副本
            form_data = request.form.to_dict()
            form_data['manager_id'] = '0'  # 临时设置为0以通过验证

            # 重新创建表单对象
            from werkzeug.datastructures import MultiDict
            form = DepartmentForm(MultiDict(form_data))

            # 重新设置选项
            form.company_id.choices = [(c.id, c.name) for c in scope['companies']]

            # 重新设置上级部门和负责人选项
            company_id = int(form_data.get('company_id', 0))
            if company_id:
                parent_depts = Department.query.filter_by(company_id=company_id).all()
                form.parent_id.choices = [(0, '无')] + [(d.id, d.name) for d in parent_depts]

                employees = User.query.filter_by(company_id=company_id).all()
                form.manager_id.choices = [(0, '请选择负责人')] + [(u.id, u.name or u.username) for u in employees]
            else:
                form.parent_id.choices = [(0, '无')]
                form.manager_id.choices = [(0, '请选择负责人')]

    if form.validate_on_submit():
        try:
            # 检查编码唯一性
            existing = Department.query.filter(
                Department.code == form.code.data,
                Department.company_id == form.company_id.data
            ).first()
            if existing:
                flash('部门编码在该公司内已存在', 'danger')
                return render_template('organization_unified/department_form.html',
                                     form=form, action='add', scope=scope)

            # 处理新建负责人数据
            new_manager_id = None
            new_manager_data = request.form.get('new_dept_manager_data')
            original_manager_id = request.form.get('manager_id')
            if new_manager_data and original_manager_id == 'NEW_DEPT_MANAGER':
                try:
                    import json
                    manager_info = json.loads(new_manager_data)
                    print(f"创建新部门负责人: {manager_info}")

                    # 检查用户名唯一性
                    existing_user = User.query.filter_by(username=manager_info['username']).first()
                    if existing_user:
                        flash(f"用户名 {manager_info['username']} 已存在", 'danger')
                        return render_template('organization_unified/department_form.html',
                                             form=form, action='add', scope=scope)

                    # 检查邮箱唯一性（如果提供）
                    if manager_info.get('email'):
                        existing_email = User.query.filter_by(email=manager_info['email']).first()
                        if existing_email:
                            flash(f"邮箱 {manager_info['email']} 已存在", 'danger')
                            return render_template('organization_unified/department_form.html',
                                                 form=form, action='add', scope=scope)

                    # 检查手机号唯一性（如果提供）
                    if manager_info.get('phone'):
                        existing_phone = User.query.filter_by(phone=manager_info['phone']).first()
                        if existing_phone:
                            flash(f"手机号 {manager_info['phone']} 已存在", 'danger')
                            return render_template('organization_unified/department_form.html',
                                                 form=form, action='add', scope=scope)

                    # 获取部门管理员角色
                    role = Role.query.filter_by(code='department_admin').first()
                    if not role:
                        flash('部门管理员角色不存在', 'danger')
                        return render_template('organization_unified/department_form.html',
                                             form=form, action='add', scope=scope)

                    # 创建新负责人
                    new_manager = User(
                        username=manager_info['username'],
                        name=manager_info['name'],
                        email=manager_info.get('email', ''),
                        phone=manager_info.get('phone', ''),
                        company_id=manager_info['company_id'],
                        role_id=role.id,
                        password_hash=generate_password_hash(manager_info.get('password', '123456')),
                        status='active'
                    )

                    db.session.add(new_manager)
                    db.session.flush()  # 获取新负责人ID
                    new_manager_id = new_manager.id
                    print(f"新部门负责人创建成功，ID: {new_manager_id}")

                except Exception as e:
                    db.session.rollback()  # 回滚会话
                    print(f"创建新部门负责人失败: {str(e)}")
                    flash(f'创建新部门负责人失败: {str(e)}', 'danger')
                    return render_template('organization_unified/department_form.html',
                                         form=form, action='add', scope=scope)

            # 创建部门
            department = Department(
                name=form.name.data,
                code=form.code.data,
                company_id=form.company_id.data,
                parent_id=form.parent_id.data if form.parent_id.data != 0 else None,
                manager_id=new_manager_id or (form.manager_id.data if form.manager_id.data != 0 else None),
                type=form.type.data
            )

            db.session.add(department)
            db.session.flush()  # 获取部门ID

            # 如果有新创建的负责人，设置其部门归属
            if new_manager_id:
                new_manager = User.query.get(new_manager_id)
                new_manager.department_id = department.id
                print(f"为新创建的部门负责人设置部门ID: {department.id}")

            db.session.commit()

            flash('部门创建成功', 'success')
            return redirect(url_for('organization_unified.departments',
                                  company_id=form.company_id.data))
            
        except Exception as e:
            db.session.rollback()
            flash(f'创建部门失败: {str(e)}', 'danger')
    
    return render_template('organization_unified/department_form.html',
                         form=form, action='add', scope=scope)

@bp.route('/departments/<int:department_id>/edit', methods=['GET', 'POST'])
@login_required
@require_permission('department_manage')
def edit_department(department_id):
    """编辑部门"""
    department = Department.query.get_or_404(department_id)

    # 权限检查
    scope = OrganizationService.get_user_scope()
    if department not in scope['departments'] and not scope['can_manage_departments']:
        flash('您没有权限编辑此部门', 'danger')
        return redirect(url_for('organization_unified.departments'))

    form = DepartmentForm(obj=department)

    # 设置公司选项
    form.company_id.choices = [(c.id, c.name) for c in scope['companies']]

    # 设置上级部门选项（排除自己和子部门）
    parent_depts = Department.query.filter(
        Department.company_id == department.company_id,
        Department.id != department_id
    ).all()
    form.parent_id.choices = [(0, '无')] + [(d.id, d.name) for d in parent_depts]

    # 设置部门负责人选项
    employees = User.query.filter_by(company_id=department.company_id).all()
    form.manager_id.choices = [(0, '请选择负责人')] + [(u.id, u.name or u.username) for u in employees]

    if form.validate_on_submit():
        try:
            # 验证操作合法性
            is_valid, error_msg = OrganizationService.validate_department_operation(
                'edit', department_id, code=form.code.data, parent_id=form.parent_id.data
            )
            if not is_valid:
                flash(error_msg, 'danger')
                return render_template('organization_unified/department_form.html',
                                     form=form, action='edit', scope=scope, department=department)

            # 更新部门信息
            department.name = form.name.data
            department.code = form.code.data
            department.company_id = form.company_id.data
            department.parent_id = form.parent_id.data if form.parent_id.data != 0 else None
            department.manager_id = form.manager_id.data if form.manager_id.data != 0 else None
            department.type = form.type.data

            db.session.commit()
            flash('部门信息更新成功', 'success')
            return redirect(url_for('organization_unified.departments'))

        except Exception as e:
            db.session.rollback()
            flash(f'更新部门失败: {str(e)}', 'danger')

    return render_template('organization_unified/department_form.html',
                         form=form, action='edit', scope=scope, department=department)

@bp.route('/departments/<int:department_id>/delete', methods=['POST'])
@login_required
@require_permission('department_manage')
def delete_department(department_id):
    """删除部门"""
    try:
        # 验证操作合法性
        is_valid, error_msg = OrganizationService.validate_department_operation('delete', department_id)
        if not is_valid:
            flash(error_msg, 'danger')
            return redirect(url_for('organization_unified.departments'))

        department = Department.query.get_or_404(department_id)

        # 权限检查
        scope = OrganizationService.get_user_scope()
        if department not in scope['departments'] and not scope['can_manage_departments']:
            flash('您没有权限删除此部门', 'danger')
            return redirect(url_for('organization_unified.departments'))

        db.session.delete(department)
        db.session.commit()

        flash('部门删除成功', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'删除部门失败: {str(e)}', 'danger')

    return redirect(url_for('organization_unified.departments'))

# ==================== 员工管理 ====================

@bp.route('/employees')
@login_required
@require_permission('organization_view')
def employees():
    """员工管理页面"""
    try:
        company_id = request.args.get('company_id', type=int)
        department_id = request.args.get('department_id', type=int)

        employees = OrganizationService.get_user_statistics(company_id, department_id)
        scope = OrganizationService.get_user_scope()
        roles = Role.query.all()

        # 确保权限检查一致性
        if not scope.get('can_manage_users', False) and current_user.role.code not in ['super_admin', 'company_admin', 'department_admin']:
            flash('您没有权限访问员工管理', 'danger')
            return redirect(url_for('organization_unified.index'))

        return render_template('organization_unified/employees.html',
                             employees=employees,
                             scope=scope,
                             roles=roles,
                             selected_company_id=company_id,
                             selected_department_id=department_id)
    except Exception as e:
        flash(f'获取员工列表失败: {str(e)}', 'danger')
        return redirect(url_for('organization_unified.index'))

@bp.route('/employees/add', methods=['GET', 'POST'])
@login_required
def add_employee():
    """添加员工"""
    # 检查权限
    if not current_user.has_permission('user_manage'):
        flash('您没有权限添加员工', 'danger')
        return redirect(url_for('organization_unified.employees'))

    try:
        scope = OrganizationService.get_user_scope()

        if request.method == 'POST':
            # 获取表单数据
            name = request.form.get('name', '').strip()
            username = request.form.get('username', '').strip()
            email = request.form.get('email', '').strip()
            phone = request.form.get('phone', '').strip()
            company_id = request.form.get('company_id', type=int)
            department_id = request.form.get('department_id', type=int)
            role_id = request.form.get('role_id', type=int)
            password = request.form.get('password', '').strip()



            # 验证必填字段
            errors = []
            if not name:
                errors.append('姓名不能为空')
            if not username:
                errors.append('用户名不能为空')
            if not email:
                errors.append('邮箱不能为空')
            if not company_id or company_id == 0:
                errors.append('请选择公司')
            if not role_id or role_id == 0:
                errors.append('请选择角色')

            # 验证权限
            if company_id not in [c.id for c in scope['companies']]:
                errors.append('无权限在该公司添加员工')

            # 验证用户名唯一性
            if User.query.filter_by(username=username).first():
                errors.append('用户名已存在')

            # 验证邮箱唯一性
            if email and User.query.filter_by(email=email).first():
                errors.append('邮箱已存在')

            if errors:
                for error in errors:
                    flash(error, 'danger')
            else:
                try:
                    # 创建用户
                    user = User(
                        username=username,
                        name=name,
                        email=email,
                        phone=phone,
                        company_id=company_id,
                        department_id=department_id if department_id and department_id != 0 else None,
                        role_id=role_id,
                        status='active'
                    )

                    # 设置密码
                    user.set_password(password if password else '123456')

                    db.session.add(user)
                    db.session.commit()

                    flash('员工添加成功', 'success')
                    return redirect(url_for('organization_unified.employees'))

                except Exception as e:
                    db.session.rollback()
                    flash(f'添加员工失败: {str(e)}', 'danger')

        # 获取数据
        companies = scope['companies']
        roles = Role.query.all()

        # 获取URL参数
        department_id = request.args.get('department_id', type=int)
        company_id = request.args.get('company_id', type=int)

        # 如果指定了部门，获取部门信息
        selected_department = None
        if department_id:
            selected_department = Department.query.get(department_id)
            if selected_department:
                company_id = selected_department.company_id

        # 如果没有指定公司，使用第一个可用公司
        if not company_id and companies:
            company_id = companies[0].id

        return render_template('organization_unified/add_employee_new.html',
                             companies=companies,
                             roles=roles,
                             selected_company_id=company_id,
                             selected_department_id=department_id,
                             title='添加员工')

    except Exception as e:
        flash(f'页面加载失败: {str(e)}', 'danger')
        return redirect(url_for('organization_unified.employees'))

@bp.route('/employees/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
@require_permission('user_manage')
def edit_employee(user_id):
    """编辑员工"""
    user = User.query.get_or_404(user_id)

    # 权限检查
    scope = OrganizationService.get_user_scope()
    if user not in scope['users'] and not scope['can_manage_users']:
        flash('您没有权限编辑此员工', 'danger')
        return redirect(url_for('organization_unified.employees'))

    form = UserForm(obj=user)

    # 设置选项
    form.company_id.choices = [(c.id, c.name) for c in scope['companies']]
    form.role_id.choices = [(r.id, r.name) for r in Role.query.all()]

    # 设置部门选项
    departments = [d for d in scope['departments'] if d.company_id == user.company_id]
    form.department_id.choices = [(0, '请选择部门')] + [(d.id, d.name) for d in departments]

    if request.method == 'POST':
        print(f"DEBUG: 收到编辑员工POST请求，用户ID: {user_id}")
        print(f"DEBUG: 表单数据: {dict(request.form)}")
        print(f"DEBUG: 表单验证结果: {form.validate()}")
        if form.errors:
            print(f"DEBUG: 表单验证错误: {form.errors}")

    if form.validate_on_submit():
        try:
            # 检查邮箱唯一性（排除自己）
            existing = User.query.filter(
                User.email == form.email.data,
                User.id != user_id
            ).first()
            if existing:
                flash('邮箱已被其他用户使用', 'danger')
                return render_template('organization_unified/employee_form.html',
                                     form=form, action='edit', scope=scope, employee=user)

            # 更新用户信息
            user.name = form.name.data
            user.email = form.email.data
            user.phone = form.phone.data
            user.company_id = form.company_id.data
            user.department_id = form.department_id.data if form.department_id.data and form.department_id.data != 0 else None
            user.role_id = form.role_id.data

            # 如果提供了新密码，则更新密码
            if form.password.data:
                user.password_hash = generate_password_hash(form.password.data)

            db.session.commit()
            flash('员工信息更新成功', 'success')
            return redirect(url_for('organization_unified.employees'))

        except Exception as e:
            db.session.rollback()
            flash(f'更新员工失败: {str(e)}', 'danger')

    return render_template('organization_unified/employee_form.html',
                         form=form, action='edit', scope=scope, employee=user)

@bp.route('/employees/<int:user_id>/delete', methods=['POST'])
@login_required
@require_permission('user_manage')
def delete_employee(user_id):
    """删除员工"""
    try:
        # 验证操作合法性
        is_valid, error_msg = OrganizationService.validate_user_operation('delete', user_id)
        if not is_valid:
            flash(error_msg, 'danger')
            return redirect(url_for('organization_unified.employees'))

        user = User.query.get_or_404(user_id)

        # 权限检查
        scope = OrganizationService.get_user_scope()
        if user not in scope['users'] and not scope['can_manage_users']:
            flash('您没有权限删除此员工', 'danger')
            return redirect(url_for('organization_unified.employees'))

        db.session.delete(user)
        db.session.commit()

        flash('员工删除成功', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'删除员工失败: {str(e)}', 'danger')

    return redirect(url_for('organization_unified.employees'))

# ==================== API 辅助函数 ====================

def update_company_api(company_id, data):
    """更新公司API"""
    try:
        company = Company.query.get_or_404(company_id)

        # 权限检查
        scope = OrganizationService.get_user_scope()
        if company not in scope['companies'] and not scope['can_manage_companies']:
            return jsonify({'error': '您没有权限编辑此公司'}), 403

        # 验证操作合法性
        is_valid, error_msg = OrganizationService.validate_company_operation(
            'edit', company_id, code=data.get('code')
        )
        if not is_valid:
            return jsonify({'error': error_msg}), 400

        # 更新公司信息
        if 'name' in data:
            company.name = data['name']
        if 'code' in data:
            company.code = data['code']
        if 'type' in data:
            company.type = data['type']
        if 'description' in data:
            company.description = data['description']

        db.session.commit()
        return jsonify({'success': True, 'message': '公司更新成功'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def update_department_api(department_id, data):
    """更新部门API"""
    try:
        department = Department.query.get_or_404(department_id)

        # 权限检查
        scope = OrganizationService.get_user_scope()
        if department not in scope['departments'] and not scope['can_manage_departments']:
            return jsonify({'error': '您没有权限编辑此部门'}), 403

        # 更新部门信息
        if 'name' in data:
            department.name = data['name']
        if 'code' in data:
            department.code = data['code']
        if 'type' in data:
            department.type = data['type']
        if 'parent_id' in data:
            department.parent_id = data['parent_id'] if data['parent_id'] != 0 else None
        if 'manager_id' in data:
            department.manager_id = data['manager_id'] if data['manager_id'] != 0 else None

        db.session.commit()
        return jsonify({'success': True, 'message': '部门更新成功'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def update_user_api(user_id, data):
    """更新用户API"""
    try:
        user = User.query.get_or_404(user_id)

        # 权限检查
        scope = OrganizationService.get_user_scope()
        if user not in scope['users'] and not scope['can_manage_users']:
            return jsonify({'error': '您没有权限编辑此用户'}), 403

        # 更新用户信息
        if 'name' in data:
            user.name = data['name']
        if 'email' in data:
            user.email = data['email']
        if 'phone' in data:
            user.phone = data['phone']
        if 'department_id' in data:
            user.department_id = data['department_id']
        if 'role_id' in data:
            user.role_id = data['role_id']
        if 'status' in data:
            user.status = data['status']

        db.session.commit()
        return jsonify({'success': True, 'message': '用户更新成功'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def delete_company_api(company_id):
    """删除公司API"""
    try:
        # 验证操作合法性
        is_valid, error_msg = OrganizationService.validate_company_operation('delete', company_id)
        if not is_valid:
            return jsonify({'error': error_msg}), 400

        company = Company.query.get_or_404(company_id)

        # 权限检查
        scope = OrganizationService.get_user_scope()
        if company not in scope['companies'] and not scope['can_manage_companies']:
            return jsonify({'error': '您没有权限删除此公司'}), 403

        # 删除公司相关的所有数据（按依赖关系顺序）
        from models import Lead, Activity, LeadTransferHistory, LeadStatusHistory, LeadPool, Department, User

        # 1. 删除线索相关数据
        # 获取该公司的所有线索ID
        lead_ids = [lead.id for lead in Lead.query.filter_by(company_id=company_id).all()]
        if lead_ids:
            # 删除线索的活动记录
            Activity.query.filter(Activity.lead_id.in_(lead_ids)).delete(synchronize_session=False)
            # 删除线索的状态历史
            LeadStatusHistory.query.filter(LeadStatusHistory.lead_id.in_(lead_ids)).delete(synchronize_session=False)
            # 删除线索的转移历史
            LeadTransferHistory.query.filter(LeadTransferHistory.lead_id.in_(lead_ids)).delete(synchronize_session=False)
            # 删除线索
            Lead.query.filter_by(company_id=company_id).delete()

        # 2. 删除线索池
        LeadPool.query.filter_by(company_id=company_id).delete()

        # 3. 删除部门
        Department.query.filter_by(company_id=company_id).delete()

        # 4. 删除用户
        User.query.filter_by(company_id=company_id).delete()

        # 5. 最后删除公司
        db.session.delete(company)
        db.session.commit()

        return jsonify({'success': True, 'message': '公司删除成功'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def delete_department_api(department_id):
    """删除部门API"""
    try:
        department = Department.query.get_or_404(department_id)

        # 权限检查
        scope = OrganizationService.get_user_scope()
        if department not in scope['departments'] and not scope['can_manage_departments']:
            return jsonify({'error': '您没有权限删除此部门'}), 403

        # 检查是否有子部门
        child_departments = Department.query.filter_by(parent_id=department_id).count()
        if child_departments > 0:
            return jsonify({'error': '该部门下还有子部门，无法删除'}), 400

        # 检查是否有员工
        employees = User.query.filter_by(department_id=department_id).count()
        if employees > 0:
            return jsonify({'error': '该部门下还有员工，无法删除'}), 400

        db.session.delete(department)
        db.session.commit()

        return jsonify({'success': True, 'message': '部门删除成功'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def delete_user_api(user_id):
    """删除用户API"""
    try:
        user = User.query.get_or_404(user_id)

        # 权限检查
        scope = OrganizationService.get_user_scope()
        if user not in scope['users'] and not scope['can_manage_users']:
            return jsonify({'error': '您没有权限删除此用户'}), 403

        # 不能删除自己
        if user.id == current_user.id:
            return jsonify({'error': '不能删除自己的账号'}), 400

        # 不能删除超级管理员（除非自己也是超级管理员）
        if user.role.code == 'super_admin' and current_user.role.code != 'super_admin':
            return jsonify({'error': '无权限删除超级管理员账号'}), 403

        db.session.delete(user)
        db.session.commit()

        return jsonify({'success': True, 'message': '用户删除成功'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def create_company_api(data):
    """创建公司API"""
    try:
        # 权限检查
        if not current_user.has_permission('company_manage'):
            return jsonify({'error': '您没有权限创建公司'}), 403

        # 检查必填字段
        if not data.get('name') or not data.get('code'):
            return jsonify({'error': '公司名称和编码为必填项'}), 400

        # 检查编码唯一性
        existing = Company.query.filter_by(code=data['code']).first()
        if existing:
            return jsonify({'error': '公司编码已存在'}), 400

        # 创建公司
        company = Company(
            name=data['name'],
            code=data['code'],
            type=data.get('type', 'branch'),
            description=data.get('description', ''),
            parent_id=data.get('parent_id') if data.get('parent_id') != 0 else None
        )

        db.session.add(company)
        db.session.commit()

        return jsonify({'success': True, 'message': '公司创建成功', 'id': company.id})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def create_department_api(data):
    """创建部门API"""
    try:
        # 权限检查
        if not current_user.has_permission('department_manage'):
            return jsonify({'error': '您没有权限创建部门'}), 403

        # 检查必填字段
        if not data.get('name') or not data.get('code') or not data.get('company_id'):
            return jsonify({'error': '部门名称、编码和所属公司为必填项'}), 400

        # 检查编码在公司内唯一性
        existing = Department.query.filter_by(
            code=data['code'],
            company_id=data['company_id']
        ).first()
        if existing:
            return jsonify({'error': '部门编码在该公司内已存在'}), 400

        # 创建部门
        department = Department(
            name=data['name'],
            code=data['code'],
            company_id=data['company_id'],
            parent_id=data.get('parent_id') if data.get('parent_id') != 0 else None,
            manager_id=data.get('manager_id') if data.get('manager_id') != 0 else None,
            type=data.get('type', 'department')
        )

        db.session.add(department)
        db.session.commit()

        return jsonify({'success': True, 'message': '部门创建成功', 'id': department.id})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def create_user_api(data):
    """创建用户API"""
    try:
        # 权限检查
        if not current_user.has_permission('user_manage'):
            return jsonify({'error': '您没有权限创建用户'}), 403

        # 检查必填字段
        required_fields = ['username', 'name', 'company_id', 'role_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} 为必填项'}), 400

        # 检查用户名唯一性
        existing = User.query.filter_by(username=data['username']).first()
        if existing:
            return jsonify({'error': '用户名已存在，请使用其他用户名'}), 400

        # 检查邮箱唯一性
        if data.get('email'):
            existing = User.query.filter_by(email=data['email']).first()
            if existing:
                return jsonify({'error': '邮箱已被其他用户使用，请使用其他邮箱'}), 400

        # 检查手机号唯一性
        if data.get('phone'):
            existing = User.query.filter_by(phone=data['phone']).first()
            if existing:
                return jsonify({
                    'error': f'手机号 {data["phone"]} 已被其他用户使用，请使用其他手机号或留空',
                    'field': 'phone',
                    'value': data['phone']
                }), 400

        # 创建用户
        user = User(
            username=data['username'],
            name=data['name'],
            email=data.get('email', ''),
            phone=data.get('phone', ''),
            company_id=data['company_id'],
            department_id=data.get('department_id') if data.get('department_id') else None,
            role_id=data['role_id'],
            password_hash=generate_password_hash(data.get('password', '123456')),
            status='active'
        )

        db.session.add(user)
        db.session.commit()

        return jsonify({'success': True, 'message': '员工添加成功！', 'id': user.id})

    except Exception as e:
        db.session.rollback()
        # 检查是否是数据库约束错误
        error_msg = str(e)
        if 'UNIQUE constraint failed: user.phone' in error_msg:
            return jsonify({
                'error': '手机号已被其他用户使用，请使用其他手机号或留空',
                'field': 'phone'
            }), 400
        elif 'UNIQUE constraint failed: user.username' in error_msg:
            return jsonify({
                'error': '用户名已存在，请使用其他用户名',
                'field': 'username'
            }), 400
        elif 'UNIQUE constraint failed: user.email' in error_msg:
            return jsonify({
                'error': '邮箱已被其他用户使用，请使用其他邮箱',
                'field': 'email'
            }), 400
        else:
            return jsonify({'error': f'添加员工失败：{error_msg}'}), 500
