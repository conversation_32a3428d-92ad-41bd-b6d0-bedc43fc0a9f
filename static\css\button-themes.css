/* ========================================
   CRM系统按钮主题样式
   统一的现代化按钮设计
======================================== */

/* 主题按钮样式 - 纯色设计 */
.btn-theme-primary {
    background-color: #ff6b00;
    color: white;
    border: none;
    box-shadow: 0 2px 8px rgba(255, 107, 0, 0.3);
}

.btn-theme-primary:hover {
    background-color: #cc5500;
    color: white;
    box-shadow: 0 4px 12px rgba(255, 107, 0, 0.4);
    transform: translateY(-2px) !important;
}

.btn-theme-info {
    background-color: #17a2b8;
    color: white;
    border: none;
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

.btn-theme-info:hover {
    background-color: #138496;
    color: white;
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4);
    transform: translateY(-2px) !important;
}

/* 成功主题按钮 */
.btn-theme-success {
    background-color: #28a745;
    color: white;
    border: none;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.btn-theme-success:hover {
    background-color: #1e7e34;
    color: white;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
    transform: translateY(-2px) !important;
}

/* 警告主题按钮 */
.btn-theme-warning {
    background-color: #ffc107;
    color: #856404;
    border: none;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
    font-weight: 600;
}

.btn-theme-warning:hover {
    background-color: #e0a800;
    color: #856404;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
    transform: translateY(-2px) !important;
}

/* 危险主题按钮 */
.btn-theme-danger {
    background-color: #dc3545;
    color: white;
    border: none;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.btn-theme-danger:hover {
    background-color: #c82333;
    color: white;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
    transform: translateY(-2px) !important;
}

/* 特殊效果按钮已移除 - 保持简洁的纯色设计 */

/* 3D效果按钮 */
.btn-3d {
    border-bottom: 4px solid rgba(0, 0, 0, 0.2);
    transition: all 0.1s ease;
}

.btn-3d:hover {
    border-bottom: 2px solid rgba(0, 0, 0, 0.2);
    transform: translateY(2px) !important;
}

.btn-3d:active {
    border-bottom: 1px solid rgba(0, 0, 0, 0.2);
    transform: translateY(3px) !important;
}

/* 简化的特殊按钮样式 */
.btn-glass {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #333;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-glass:hover {
    background: rgba(255, 255, 255, 1);
    color: #333;
    transform: translateY(-2px) !important;
}

/* 按钮组合样式 */
.btn-group-theme .btn {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.btn-group-theme .btn:last-child {
    margin-right: 0;
}

/* 响应式按钮 */
@media (max-width: 768px) {
    .btn-group-theme {
        display: flex;
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group-theme .btn {
        width: 100%;
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
    
    .btn-group-theme .btn:last-child {
        margin-bottom: 0;
    }
}

/* 加载状态按钮 */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    color: white;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 按钮图标样式增强 */
.btn i.fas,
.btn i.far,
.btn i.fab {
    margin-right: 0.5rem;
    font-size: 0.875em;
}

.btn i.fas:last-child,
.btn i.far:last-child,
.btn i.fab:last-child {
    margin-right: 0;
    margin-left: 0.5rem;
}

.btn-icon-only i {
    margin: 0 !important;
}
