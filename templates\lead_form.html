{% extends 'base.html' %}

{% block title %}{% if lead %}编辑线索{% else %}添加线索{% endif %} - CRM系统{% endblock %}

{% block extra_css %}
<style>
/* 城市省份输入选择框样式 */
.position-relative .dropdown-menu {
    display: none;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    background-color: #fff;
    margin-top: 2px;
}

.position-relative .dropdown-menu.show {
    display: block;
}

.position-relative .dropdown-item {
    padding: 0.5rem 1rem;
    color: #212529;
    text-decoration: none;
    cursor: pointer;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.position-relative .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #1e2125;
}

.position-relative .dropdown-item:focus {
    background-color: #e9ecef;
    color: #1e2125;
    outline: none;
}

.position-relative .dropdown-item.text-muted {
    color: #6c757d !important;
    cursor: default;
}

.position-relative .dropdown-item.text-muted:hover {
    background-color: transparent;
}

/* 输入框获得焦点时的样式 */
.form-control:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- 页面标题区域 -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">
                    <i class="fas fa-user-edit me-2"></i>{{ '编辑线索' if lead else '添加线索' }}
                </h1>
                <p class="page-subtitle">{{ '修改线索信息和跟进状态' if lead else '创建新的销售线索并设置基本信息' }}</p>
            </div>
            <div class="text-end">
                <div class="btn-group-theme">
                    <a href="{{ url_for('leads.leads_unified') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回线索管理
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    
                    <style>
                        #company_search_results {
                            max-height: 300px;
                            overflow-y: auto;
                            position: absolute;
                            z-index: 1000;
                        }
                        .selected-info {
                            padding: 5px;
                            background-color: #f8f9fa;
                            border-radius: 3px;
                            border: 1px solid #dee2e6;
                        }
                    </style>
                    
                    <!-- 强制刷新按钮 -->
                    <div class="d-flex justify-content-end mb-3">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="forceRefresh()">
                            <i class="fas fa-sync-alt"></i> 强制刷新页面
                        </button>
                    </div>
                    
                    <form method="POST" data-skip-validation="true">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <div class="mb-3">
                            <label for="name" class="form-label">姓名</label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ lead.name if lead else '' }}" required {% if is_editing_cross_lead_by_b_employee %}disabled{% endif %}>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="province" class="form-label">省份</label>
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="province" name="company_name"
                                           placeholder="请输入或选择省份" autocomplete="off" required
                                           {% if is_editing_cross_lead_by_b_employee %}disabled{% endif %}>
                                    <div id="province-dropdown" class="dropdown-menu position-absolute w-100" style="max-height: 200px; overflow-y: auto; z-index: 1000;"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="city" class="form-label">城市</label>
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="city" name="email"
                                           placeholder="请输入或选择城市" autocomplete="off" required
                                           {% if is_editing_cross_lead_by_b_employee %}disabled{% endif %}>
                                    <div id="city-dropdown" class="dropdown-menu position-absolute w-100" style="max-height: 200px; overflow-y: auto; z-index: 1000;"></div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">电话</label>
                            <input type="tel" class="form-control" id="phone" name="phone" value="{{ lead.phone if lead else '' }}" {% if is_editing_cross_lead_by_b_employee %}disabled{% endif %}>
                        </div>

                        <div class="card mb-3">
                            <div class="card-header">
                                <h5 class="mb-0">跟进状态</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="is_called" id="is_called" value="yes" {% if lead and lead.is_called %}checked{% endif %} {% if is_editing_cross_lead_by_b_employee and lead and lead.is_called %}disabled{% endif %}>
                                            <label class="form-check-label" for="is_called">
                                                已拨电话
                                            </label>
                                        </div>
                                        
                                        <div class="form-check mb-3" style="display:none;">
                                            <input class="form-check-input" type="checkbox" name="is_connected" id="is_connected" value="yes" {% if lead and lead.is_connected %}checked{% endif %} {% if is_editing_cross_lead_by_b_employee and lead and lead.is_connected %}disabled{% endif %}>
                                            <label class="form-check-label" for="is_connected">
                                                接通
                                            </label>
                                        </div>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="is_valid_call" id="is_valid_call" value="yes" {% if lead and lead.is_valid_call %}checked{% endif %} {% if is_editing_cross_lead_by_b_employee and lead and lead.is_valid_call %}disabled{% endif %}>
                                            <label class="form-check-label" for="is_valid_call">
                                                有效通话
                                            </label>
                                        </div>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="is_wechat_added" id="is_wechat_added" value="yes" {% if lead and lead.is_wechat_added %}checked{% endif %} {% if is_editing_cross_lead_by_b_employee and lead and lead.is_wechat_added %}disabled{% endif %}>
                                            <label class="form-check-label" for="is_wechat_added">
                                                添加微信
                                            </label>
                                        </div>
                                        
                                        <div class="form-check mb-3 d-flex align-items-center">
                                            <div>
                                                <input class="form-check-input" type="checkbox" name="is_intentional" id="is_intentional" value="yes" {% if lead and lead.is_intentional %}checked{% endif %} {% if is_editing_cross_lead_by_b_employee and lead and lead.is_intentional %}disabled{% endif %}>
                                                <label class="form-check-label" for="is_intentional">
                                                    意向客户
                                                </label>
                                            </div>
                                            <!-- 目标公司搜索和选择区域 -->
                                            <div id="target_company_section" class="ms-3" style="display:none; flex-grow: 1;">
                                                <div class="input-group input-group-sm">
                                                    <input type="text" class="form-control form-control-sm" id="target_company_search" placeholder="输入公司名称...">
                                                    <input type="hidden" id="target_company_id" name="target_company_id">
                                                    <button class="btn btn-outline-secondary btn-sm" type="button" id="clear_company_btn">清除</button>
                                                </div>
                                                <div id="company_search_results" class="dropdown-menu" style="width: auto; max-width: 300px;"></div>
                                                <div id="selected_company_info" class="selected-info mt-1 small" style="display:none; font-size: 0.8rem;">
                                                    推送至: <span id="selected_company_name"></span>
                                                </div>
                                                <small class="form-text text-muted" style="font-size: 0.7rem;">推送到目标公司（本公司不用填写）</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="is_visited" id="is_visited" value="yes" {% if lead and lead.is_visited %}checked{% endif %} {% if is_editing_cross_lead_by_b_employee and lead and lead.is_visited %}disabled{% endif %}>
                                            <label class="form-check-label" for="is_visited">
                                                确认到面
                                            </label>
                                        </div>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="is_compliant" id="is_compliant" value="yes" {% if lead and lead.is_compliant %}checked{% endif %} {% if is_editing_cross_lead_by_b_employee and lead and lead.is_compliant %}disabled{% endif %}>
                                            <label class="form-check-label" for="is_compliant">
                                                信息合规
                                            </label>
                                        </div>
                                        
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="is_deal_done" id="is_deal_done" value="yes" {% if lead and lead.is_deal_done %}checked{% endif %} {% if is_editing_cross_lead_by_b_employee and lead and lead.is_deal_done %}disabled{% endif %}>
                                            <label class="form-check-label" for="is_deal_done">
                                                完成签约
                                            </label>
                                        </div>

                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" name="is_car_selected" id="is_car_selected" value="yes" {% if lead and lead.is_car_selected %}checked{% endif %} {% if is_editing_cross_lead_by_b_employee and lead and lead.is_car_selected %}disabled{% endif %}>
                                            <label class="form-check-label" for="is_car_selected">
                                                完成提车
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Lease Plan Section (Initially Hidden) -->
                        <div id="lease_plan_section" class="card mb-3" style="display: none;">
                            <div class="card-header">
                                <h5 class="mb-0">租赁计划详情</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="lease_term_months" class="form-label">租期 (月)</label>
                                    <select class="form-select" id="lease_term_months" name="lease_term_months">
                                        <option value="" {% if not lead or not lead.lease_plan or not lead.lease_plan.term_months %}selected{% endif %}>请选择</option>
                                        <option value="1" {% if lead and lead.lease_plan and lead.lease_plan.term_months == 1 %}selected{% endif %}>1个月</option>
                                        <option value="2" {% if lead and lead.lease_plan and lead.lease_plan.term_months == 2 %}selected{% endif %}>2个月</option>
                                        <option value="3" {% if lead and lead.lease_plan and lead.lease_plan.term_months == 3 %}selected{% endif %}>3个月</option>
                                        <option value="4" {% if lead and lead.lease_plan and lead.lease_plan.term_months == 4 %}selected{% endif %}>4个月</option>
                                        <option value="5" {% if lead and lead.lease_plan and lead.lease_plan.term_months == 5 %}selected{% endif %}>5个月</option>
                                        <option value="6" {% if lead and lead.lease_plan and lead.lease_plan.term_months == 6 %}selected{% endif %}>6个月</option>
                                        <option value="7" {% if lead and lead.lease_plan and lead.lease_plan.term_months == 7 %}selected{% endif %}>7个月</option>
                                        <option value="8" {% if lead and lead.lease_plan and lead.lease_plan.term_months == 8 %}selected{% endif %}>8个月</option>
                                        <option value="9" {% if lead and lead.lease_plan and lead.lease_plan.term_months == 9 %}selected{% endif %}>9个月</option>
                                        <option value="10" {% if lead and lead.lease_plan and lead.lease_plan.term_months == 10 %}selected{% endif %}>10个月</option>
                                        <option value="11" {% if lead and lead.lease_plan and lead.lease_plan.term_months == 11 %}selected{% endif %}>11个月</option>
                                        <option value="12" {% if lead and lead.lease_plan and lead.lease_plan.term_months == 12 %}selected{% endif %}>12个月</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="lease_free_rent_days" class="form-label">免租天数</label>
                                    <input type="number" class="form-control" id="lease_free_rent_days" name="lease_free_rent_days" min="0" value="{{ lead.lease_plan.free_rent_days if lead and lead.lease_plan else '0' }}">
                                </div>
                                <div class="mb-3">
                                    <label for="lease_custom_notes" class="form-label">其他赠送或备注</label>
                                    <textarea class="form-control" id="lease_custom_notes" name="lease_custom_notes" rows="3">{{ lead.lease_plan.custom_notes if lead and lead.lease_plan else '' }}</textarea>
                                </div>
                            </div>
                        </div>

                    

                        <div class="mb-3">
                            <label for="notes" class="form-label">备注</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3">{{ lead.notes if lead else '' }}</textarea>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-theme-primary">保存线索</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 状态级联逻辑
document.addEventListener('DOMContentLoaded', function() {
    // 状态字段定义（按照业务流程顺序）
    const statusFields = [
        'is_called',        // 已拨电话
        'is_valid_call',    // 有效通话
        'is_wechat_added',  // 添加微信
        'is_intentional',   // 意向客户
        'is_visited',       // 确认到面
        'is_compliant',     // 信息合规
        'is_deal_done',     // 完成签约
        'is_car_selected'   // 完成提车
    ];

    // 加载编辑模式的状态
    const pageTitle = document.querySelector('h1.page-title');
    const isEditMode = pageTitle && pageTitle.textContent.includes('编辑');
    console.log('页面模式:', isEditMode ? '编辑线索' : '添加线索');

    // 为状态字段添加事件监听器
    statusFields.forEach((field, index) => {
        const checkbox = document.getElementById(field);

        if (checkbox) {
            console.log(`为 ${field} 添加事件监听器，索引: ${index}`);

            // 点击复选框时的级联逻辑
            checkbox.addEventListener('change', function() {
                console.log(`${field} 状态变更为: ${this.checked}`);

                if (this.checked) {
                    // 选中时：自动选中前面所有字段
                    console.log(`选中 ${field}，自动选中前面的字段`);
                    for (let i = 0; i < index; i++) {
                        const prevField = statusFields[i];
                        const prevCheckbox = document.getElementById(prevField);
                        if (prevCheckbox && !prevCheckbox.disabled && !prevCheckbox.checked) {
                            console.log(`自动选中: ${prevField}`);
                            prevCheckbox.checked = true;
                        }
                    }

                    // 特别处理is_connected字段(已隐藏)
                    if (field === 'is_valid_call') {
                        const connectedCheckbox = document.getElementById('is_connected');
                        if (connectedCheckbox && !connectedCheckbox.checked) {
                            console.log('自动选中隐藏的 is_connected 字段');
                            connectedCheckbox.checked = true;
                        }
                    }
                } else {
                    // 取消选中时：自动取消后面所有字段
                    console.log(`取消选中 ${field}，自动取消后面的字段`);
                    for (let i = index + 1; i < statusFields.length; i++) {
                        const nextField = statusFields[i];
                        const nextCheckbox = document.getElementById(nextField);
                        if (nextCheckbox && !nextCheckbox.disabled && nextCheckbox.checked) {
                            console.log(`自动取消选中: ${nextField}`);
                            nextCheckbox.checked = false;
                        }
                    }

                    // 特别处理：如果取消"有效通话"，也要取消隐藏的"接通"字段
                    if (field === 'is_valid_call') {
                        const connectedCheckbox = document.getElementById('is_connected');
                        if (connectedCheckbox && connectedCheckbox.checked) {
                            console.log('自动取消选中隐藏的 is_connected 字段');
                            connectedCheckbox.checked = false;
                        }
                    }
                }
            });
        } else {
            console.warn(`未找到复选框: ${field}`);
        }
    });

    console.log('状态级联逻辑初始化完成');
});
</script>

<script>
// 强制刷新页面
function forceRefresh() {
    // 清除浏览器缓存并重新加载页面
    window.location.reload(true);
}
</script>

<script>
// 省份和城市二级联动 - 支持输入选择
document.addEventListener('DOMContentLoaded', function() {
    const provinceInput = document.getElementById('province');
    const cityInput = document.getElementById('city');
    const provinceDropdown = document.getElementById('province-dropdown');
    const cityDropdown = document.getElementById('city-dropdown');

    // 获取当前选中的省份和城市（如果有）
    const currentProvince = "{{ lead.company_name if lead else '' }}";
    const currentCity = "{{ lead.email if lead else '' }}";

    // 直辖市列表
    const directMunicipalities = ["北京市", "上海市", "天津市", "重庆市"];

    let regionsData = null;

    // 加载省份和城市数据
    fetch('/data/china_regions.json')
        .then(response => response.json())
        .then(data => {
            regionsData = data;

            // 设置当前值
            if (currentProvince) {
                provinceInput.value = currentProvince;
                updateCityOptions(currentProvince, currentCity);
            }

            // 初始化省份输入框事件
            initProvinceInput();
            // 初始化城市输入框事件
            initCityInput();
        })
        .catch(error => console.error('加载省份和城市数据失败:', error));

    // 初始化省份输入框
    function initProvinceInput() {
        // 输入事件 - 实时搜索
        provinceInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            showProvinceDropdown(query);
        });

        // 获得焦点时显示所有选项
        provinceInput.addEventListener('focus', function() {
            showProvinceDropdown('');
        });

        // 失去焦点时隐藏下拉框
        provinceInput.addEventListener('blur', function() {
            setTimeout(() => {
                hideDropdown(provinceDropdown);
            }, 200);
        });

        // 省份变化时更新城市
        provinceInput.addEventListener('change', function() {
            updateCityOptions(this.value);
        });
    }

    // 初始化城市输入框
    function initCityInput() {
        // 输入事件 - 实时搜索
        cityInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            const selectedProvince = provinceInput.value;
            showCityDropdown(selectedProvince, query);
        });

        // 获得焦点时显示选项
        cityInput.addEventListener('focus', function() {
            const selectedProvince = provinceInput.value;
            if (selectedProvince) {
                showCityDropdown(selectedProvince, '');
            }
        });

        // 失去焦点时隐藏下拉框
        cityInput.addEventListener('blur', function() {
            setTimeout(() => {
                hideDropdown(cityDropdown);
            }, 200);
        });
    }

    // 显示省份下拉选项
    function showProvinceDropdown(query) {
        if (!regionsData) return;

        const filteredProvinces = regionsData.provinces.filter(province =>
            province.name.toLowerCase().includes(query)
        );

        provinceDropdown.innerHTML = '';

        if (filteredProvinces.length === 0) {
            provinceDropdown.innerHTML = '<div class="dropdown-item text-muted">未找到匹配的省份</div>';
        } else {
            filteredProvinces.forEach(province => {
                const item = document.createElement('a');
                item.className = 'dropdown-item';
                item.href = '#';
                item.textContent = province.name;
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    provinceInput.value = province.name;
                    hideDropdown(provinceDropdown);
                    updateCityOptions(province.name);
                    provinceInput.focus();
                });
                provinceDropdown.appendChild(item);
            });
        }

        showDropdown(provinceDropdown);
    }

    // 显示城市下拉选项
    function showCityDropdown(selectedProvince, query) {
        if (!regionsData || !selectedProvince) return;

        let cities = [];

        // 检查是否是直辖市
        if (directMunicipalities.includes(selectedProvince)) {
            cities = [selectedProvince];
        } else {
            const province = regionsData.provinces.find(p => p.name === selectedProvince);
            if (province && province.cities) {
                cities = province.cities;
            }
        }

        const filteredCities = cities.filter(city =>
            city.toLowerCase().includes(query)
        );

        cityDropdown.innerHTML = '';

        if (filteredCities.length === 0) {
            cityDropdown.innerHTML = '<div class="dropdown-item text-muted">未找到匹配的城市</div>';
        } else {
            filteredCities.forEach(city => {
                const item = document.createElement('a');
                item.className = 'dropdown-item';
                item.href = '#';
                item.textContent = city;
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    cityInput.value = city;
                    hideDropdown(cityDropdown);
                    cityInput.focus();
                });
                cityDropdown.appendChild(item);
            });
        }

        showDropdown(cityDropdown);
    }

    // 更新城市选项
    function updateCityOptions(selectedProvince, selectedCity = '') {
        if (!selectedProvince) {
            cityInput.value = '';
            cityInput.placeholder = '请先选择省份';
            cityInput.disabled = true;
            return;
        }

        cityInput.disabled = false;
        cityInput.placeholder = '请输入或选择城市';

        // 如果是直辖市，自动设置城市
        if (directMunicipalities.includes(selectedProvince)) {
            cityInput.value = selectedProvince;
            cityInput.disabled = true;
        } else if (selectedCity) {
            cityInput.value = selectedCity;
        } else {
            cityInput.value = '';
        }
    }

    // 显示下拉框
    function showDropdown(dropdown) {
        dropdown.classList.add('show');
    }

    // 隐藏下拉框
    function hideDropdown(dropdown) {
        dropdown.classList.remove('show');
    }

    // 点击页面其他地方时隐藏所有下拉框
    document.addEventListener('click', function(e) {
        if (!provinceInput.contains(e.target) && !provinceDropdown.contains(e.target)) {
            hideDropdown(provinceDropdown);
        }
        if (!cityInput.contains(e.target) && !cityDropdown.contains(e.target)) {
            hideDropdown(cityDropdown);
        }
    });
});
</script>

<script>
// 新增：租赁计划区域显示/隐藏逻辑
document.addEventListener('DOMContentLoaded', function() {
    const dealDoneCheckbox = document.getElementById('is_deal_done');
    const leasePlanSection = document.getElementById('lease_plan_section');

    function toggleLeasePlanSection() {
        if (dealDoneCheckbox && leasePlanSection) {
            if (dealDoneCheckbox.checked) {
                leasePlanSection.style.display = 'block';
            } else {
                leasePlanSection.style.display = 'none';
                // Optional: Clear lease plan fields if deal is unchecked
                // document.getElementById('lease_term_months').value = '';
                // document.getElementById('lease_free_rent_days').value = '0';
                // document.getElementById('lease_custom_notes').value = '';
            }
        }
    }

    if (dealDoneCheckbox) {
        // Initial check on page load
        toggleLeasePlanSection(); 
        // Add event listener for changes
        dealDoneCheckbox.addEventListener('change', toggleLeasePlanSection);
    }

    // 省份城市二级联动等其他脚本保持不变
    // ... existing province/city script ...
});
</script>

<script>
// 公司搜索和选择功能
document.addEventListener('DOMContentLoaded', function() {
    const targetCompanySection = document.getElementById('target_company_section');
    const isIntentionalCheckbox = document.getElementById('is_intentional');
    const targetCompanySearch = document.getElementById('target_company_search');
    const targetCompanyIdInput = document.getElementById('target_company_id');
    const searchResultsDiv = document.getElementById('company_search_results');
    const selectedCompanyInfo = document.getElementById('selected_company_info');
    const selectedCompanyName = document.getElementById('selected_company_name');
    const clearCompanyBtn = document.getElementById('clear_company_btn');
    
    // 获取后续状态复选框
    const isVisitedCheckbox = document.getElementById('is_visited');
    const isCompliantCheckbox = document.getElementById('is_compliant');
    const isDealDoneCheckbox = document.getElementById('is_deal_done');
    const isCarSelectedCheckbox = document.getElementById('is_car_selected');
    
    // 根据是否选择了公司来启用/禁用后续复选框
    function toggleLateStageCheckboxes(hasSelectedCompany) {
        if (isVisitedCheckbox) isVisitedCheckbox.disabled = hasSelectedCompany;
        if (isCompliantCheckbox) isCompliantCheckbox.disabled = hasSelectedCompany;
        if (isDealDoneCheckbox) isDealDoneCheckbox.disabled = hasSelectedCompany;
        if (isCarSelectedCheckbox) isCarSelectedCheckbox.disabled = hasSelectedCompany;
        
        // 如果禁用复选框，同时取消选中
        if (hasSelectedCompany) {
            if (isVisitedCheckbox) isVisitedCheckbox.checked = false;
            if (isCompliantCheckbox) isCompliantCheckbox.checked = false;
            if (isDealDoneCheckbox) isDealDoneCheckbox.checked = false;
            if (isCarSelectedCheckbox) isCarSelectedCheckbox.checked = false;
        }
    }
    
    // 意向客户复选框变化时控制公司搜索区域显示/隐藏
    if (isIntentionalCheckbox && targetCompanySection) {
        function toggleTargetCompanySection() {
            if (isIntentionalCheckbox.checked) {
                targetCompanySection.style.display = 'block';
                // 如果已经选择了公司，禁用后续复选框
                if (targetCompanyIdInput && targetCompanyIdInput.value) {
                    toggleLateStageCheckboxes(true);
                }
            } else {
                targetCompanySection.style.display = 'none';
                // 清除已选公司
                clearSelectedCompany();
                // 启用后续复选框
                toggleLateStageCheckboxes(false);
            }
        }
        
        // 初始化显示状态
        toggleTargetCompanySection();
        
        // 添加事件监听
        isIntentionalCheckbox.addEventListener('change', toggleTargetCompanySection);
    }
    
    // 清除选择的公司
    function clearSelectedCompany() {
        if (targetCompanyIdInput) targetCompanyIdInput.value = '';
        if (targetCompanySearch) targetCompanySearch.value = '';
        if (selectedCompanyInfo) selectedCompanyInfo.style.display = 'none';
        // 启用后续复选框
        toggleLateStageCheckboxes(false);
    }
    
    // 清除按钮事件
    if (clearCompanyBtn) {
        clearCompanyBtn.addEventListener('click', clearSelectedCompany);
    }
    
    // 公司搜索输入事件
    if (targetCompanySearch) {
        targetCompanySearch.addEventListener('input', function() {
            const query = this.value.trim();
            if (query.length < 2) {
                searchResultsDiv.style.display = 'none';
                return;
            }
            
            // AJAX请求搜索公司
            fetch(`/organization_api/companies/search?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    searchResultsDiv.innerHTML = '';

                    // 处理错误响应
                    if (data.error) {
                        searchResultsDiv.innerHTML = '<div class="dropdown-item text-danger">搜索出错，请重试</div>';
                        searchResultsDiv.style.display = 'block';
                        return;
                    }

                    // 确保data是数组格式
                    const companies = Array.isArray(data) ? data : (data.companies || []);

                    if (companies.length === 0) {
                        searchResultsDiv.innerHTML = '<div class="dropdown-item text-muted">未找到匹配的公司</div>';
                    } else {
                        companies.forEach(company => {
                            const item = document.createElement('a');
                            item.classList.add('dropdown-item');
                            item.href = '#';
                            item.textContent = company.name;
                            item.dataset.id = company.id;
                            item.addEventListener('click', function(e) {
                                e.preventDefault();
                                // 选择公司
                                targetCompanyIdInput.value = company.id;
                                targetCompanySearch.value = company.name;
                                selectedCompanyName.textContent = company.name;
                                selectedCompanyInfo.style.display = 'block';
                                searchResultsDiv.style.display = 'none';
                                // 禁用后续复选框
                                toggleLateStageCheckboxes(true);
                            });
                            searchResultsDiv.appendChild(item);
                        });
                    }
                    searchResultsDiv.style.display = 'block';
                })
                .catch(error => {
                    console.error('搜索公司时出错:', error);
                    searchResultsDiv.innerHTML = '<div class="dropdown-item text-danger">网络错误，请重试</div>';
                    searchResultsDiv.style.display = 'block';
                });
        });
        
        // 点击其他区域关闭搜索结果
        document.addEventListener('click', function(e) {
            if (!targetCompanySearch.contains(e.target) && !searchResultsDiv.contains(e.target)) {
                searchResultsDiv.style.display = 'none';
            }
        });
    }
    
    // 页面加载时，检查是否已选择公司
    if (targetCompanyIdInput && targetCompanyIdInput.value) {
        toggleLateStageCheckboxes(true);
    }
});
</script>
{% endblock %}