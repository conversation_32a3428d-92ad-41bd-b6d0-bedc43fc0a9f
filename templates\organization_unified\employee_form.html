{% extends 'base.html' %}

{% block title %}{{ '编辑员工' if action == 'edit' else '添加员工' }} - CRM系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ '编辑员工' if action == 'edit' else '添加员工' }}</h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="employeeForm" data-skip-validation="true">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        
                        <div class="row">
                            <!-- 基本信息 -->
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">姓名 <span class="text-danger">*</span></label>
                                    {{ form.name(class="form-control", placeholder="请输入员工姓名") }}
                                    {% if form.name.errors %}
                                        <div class="text-danger">
                                            {% for error in form.name.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">用户名 <span class="text-danger">*</span></label>
                                    {{ form.username(class="form-control", placeholder="请输入用户名", readonly=(action == 'edit')) }}
                                    {% if form.username.errors %}
                                        <div class="text-danger">
                                            {% for error in form.username.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">邮箱 <span class="text-danger">*</span></label>
                                    {{ form.email(class="form-control", placeholder="请输入邮箱地址") }}
                                    {% if form.email.errors %}
                                        <div class="text-danger">
                                            {% for error in form.email.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label">手机号码</label>
                                    {{ form.phone(class="form-control", placeholder="请输入手机号码") }}
                                    {% if form.phone.errors %}
                                        <div class="text-danger">
                                            {% for error in form.phone.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- 组织关系 -->
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label class="form-label">所属公司 <span class="text-danger">*</span></label>
                                    <select name="company_id" id="companySelect" class="form-control" required>
                                        {% for value, label in form.company_id.choices %}
                                        <option value="{{ value }}" {% if form.company_id.data == value %}selected{% endif %}>{{ label }}</option>
                                        {% endfor %}
                                    </select>
                                    {% if form.company_id.errors %}
                                        <div class="text-danger">
                                            {% for error in form.company_id.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label class="form-label">所属部门</label>
                                    <select name="department_id" id="departmentSelect" class="form-control">
                                        {% for value, label in form.department_id.choices %}
                                        <option value="{{ value }}" {% if form.department_id.data == value %}selected{% endif %}>{{ label }}</option>
                                        {% endfor %}
                                    </select>
                                    {% if form.department_id.errors %}
                                        <div class="text-danger">
                                            {% for error in form.department_id.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label class="form-label">角色 <span class="text-danger">*</span></label>
                                    {{ form.role_id(class="form-control") }}
                                    {% if form.role_id.errors %}
                                        <div class="text-danger">
                                            {% for error in form.role_id.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- 密码设置 -->
                            {% if action == 'edit' %}
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label class="form-label">新密码</label>
                                    {{ form.password(class="form-control", placeholder="留空则不修改密码") }}
                                    <small class="form-text text-muted">如需修改密码，请输入新密码</small>
                                    {% if form.password.errors %}
                                        <div class="text-danger">
                                            {% for error in form.password.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">{{ '更新员工' if action == 'edit' else '创建员工' }}</button>
                            <a href="{{ url_for('organization_unified.employees') }}" class="btn btn-secondary">取消</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    console.log('员工表单页面加载完成');

    // 添加表单提交监听器
    $('#employeeForm').on('submit', function(e) {
        console.log('表单提交事件触发');
        console.log('表单数据:', $(this).serialize());

        // 检查必填字段
        const name = $('input[name="name"]').val();
        const email = $('input[name="email"]').val();
        const companyId = $('select[name="company_id"]').val();
        const roleId = $('select[name="role_id"]').val();

        console.log('字段值:', {name, email, companyId, roleId});

        if (!name || !email || !companyId || !roleId) {
            e.preventDefault();
            alert('请填写所有必填字段');
            return false;
        }
    });
    
    const $companySelect = $('#companySelect');
    const $departmentSelect = $('#departmentSelect');
    
    // 编辑模式下，部门选项已经在后端设置好了，不需要重新加载
    {% if action == 'edit' and employee %}
    console.log('编辑模式，部门选项已预设');
    {% endif %}
    
    // 公司选择变化时更新部门
    $companySelect.change(function() {
        const companyId = $(this).val();
        console.log('公司选择变化:', companyId);
        updateDepartments(companyId);
    });
    
    // 更新部门列表
    function updateDepartments(companyId, selectedDepartmentId = null) {
        console.log('更新部门列表, 公司ID:', companyId, '选中部门ID:', selectedDepartmentId);
        
        // 清空部门选项
        $departmentSelect.html('<option value="0">请选择部门</option>');
        
        if (companyId && companyId !== '0') {
            const apiUrl = `/organization_api/companies/${companyId}/departments`;
            console.log('请求API:', apiUrl);
            
            $.ajax({
                url: apiUrl,
                type: 'GET',
                success: function(data) {
                    console.log('部门数据:', data);

                    // API直接返回部门数组
                    if (Array.isArray(data)) {
                        data.forEach(function(dept) {
                            const selected = selectedDepartmentId && dept.id == selectedDepartmentId ? 'selected' : '';
                            $departmentSelect.append(`<option value="${dept.id}" ${selected}>${dept.name}</option>`);
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('加载部门失败:', error);
                    console.error('响应状态:', xhr.status);
                    console.error('响应文本:', xhr.responseText);
                }
            });
        }
    }
});
</script>
{% endblock %}
