# 异地到店原始员工管理功能说明

## 问题描述
原始员工推送线索到异地后，无法查看和管理已推送的异地线索，包括：
- 无法查看推送状态
- 无法重新推送给其他店
- 无法撤回异地线索

## 解决方案

### 1. 新增"我推送的异地线索"标签页

#### 功能特性
- **查看推送历史**: 显示所有由当前用户推送的异地线索
- **状态跟踪**: 实时显示线索状态（待认领/已认领/已撤回）
- **目标公司信息**: 显示推送到哪个公司
- **当前负责人**: 显示异地线索的当前负责人
- **推送时间**: 记录推送的具体时间

#### 权限控制
- 仅公司管理员和超级管理员可见此标签页
- 只能查看自己推送的异地线索

### 2. 状态筛选功能

#### 筛选选项
- **全部状态**: 显示所有推送的线索
- **待认领**: 在异地池中等待认领的线索
- **已认领**: 已被目标公司员工认领的线索
- **已撤回**: 已撤回到本公司的线索

### 3. 线索管理操作

#### 撤回功能
- **适用条件**: 仅限待认领状态的线索
- **操作结果**: 线索回到原始负责人名下
- **权限要求**: 原推送公司的管理员

#### 重新推送功能
- **适用条件**: 已撤回或待认领状态的线索
- **操作流程**: 选择新的目标公司 → 确认推送
- **智能处理**: 自动撤回当前推送状态，再推送到新公司

## 技术实现

### 1. 后端API接口

#### 获取我推送的异地线索
```
GET /api/leads/my-pushed-cross-location
参数:
- status: 状态筛选 (pending/claimed/recalled)
- target_company: 目标公司筛选
- page: 分页参数
```

#### 重新推送异地线索
```
POST /leads/<lead_id>/re-push-to-cross-location
参数:
- target_company_id: 新的目标公司ID
```

### 2. 数据查询逻辑

#### 查询我推送的线索
```python
# 通过转移历史记录找到我推送的线索
pushed_lead_ids_query = db.session.query(LeadTransferHistory.lead_id).filter(
    LeadTransferHistory.created_by == current_user.id,
    LeadTransferHistory.transfer_type == 'PUSH_TO_CROSS_PENDING'
).distinct()

# 构建主查询
query = Lead.query.filter(
    Lead.id.in_(pushed_lead_ids_query),
    Lead.company_id == current_user.company_id
)
```

#### 状态判断逻辑
```python
if lead.current_processing_company_id is None:
    status = 'recalled'  # 已撤回
elif lead.owner_id is None:
    status = 'pending'   # 待认领
else:
    status = 'claimed'   # 已认领
```

### 3. 前端界面实现

#### 表格展示
- 客户基本信息（姓名、联系方式、省份城市）
- 推送信息（目标公司、推送时间）
- 状态信息（当前状态、当前负责人）
- 操作按钮（查看详情、撤回、重新推送）

#### 交互功能
- 状态筛选下拉框
- 实时刷新按钮
- 模态框选择目标公司
- 公司名称搜索功能

## 使用流程

### 1. 查看推送的异地线索
1. 进入线索管理页面
2. 点击"我推送的异地线索"标签页
3. 查看所有推送的线索状态

### 2. 撤回异地线索
1. 找到状态为"待认领"的线索
2. 点击"撤回"按钮
3. 确认撤回操作
4. 线索回到原始负责人名下

### 3. 重新推送异地线索
1. 找到状态为"已撤回"或"待认领"的线索
2. 点击"重新推送"按钮
3. 搜索并选择新的目标公司
4. 确认推送操作

## 业务价值

### 1. 提高管理效率
- 原始员工可以实时跟踪异地线索状态
- 及时发现未被认领的线索并采取行动
- 统一管理所有推送的异地线索

### 2. 优化资源配置
- 可以将未被认领的线索重新推送给其他公司
- 避免线索在异地池中长期积压
- 提高线索转化效率

### 3. 增强协作透明度
- 清楚了解异地合作的进展情况
- 便于与目标公司沟通协调
- 提供完整的异地服务追踪

## 权限说明

### 查看权限
- **公司管理员**: 可查看本公司所有推送的异地线索
- **超级管理员**: 可查看所有公司推送的异地线索
- **普通员工**: 无此功能权限

### 操作权限
- **撤回权限**: 仅限原推送公司的管理员
- **重新推送权限**: 仅限原推送公司的管理员
- **查看详情权限**: 所有有查看权限的用户

## 注意事项

### 1. 数据一致性
- 撤回和重新推送操作都会记录完整的转移历史
- 确保线索状态的准确性和可追溯性

### 2. 权限控制
- 严格控制操作权限，防止误操作
- 只能操作本公司推送的线索

### 3. 用户体验
- 提供清晰的状态标识和操作提示
- 支持批量操作和快速筛选
- 实时更新数据状态

## 扩展功能建议

### 1. 批量操作
- 批量撤回多个待认领线索
- 批量重新推送到同一目标公司

### 2. 统计分析
- 推送成功率统计
- 各公司认领效率分析
- 异地合作效果评估

### 3. 自动化处理
- 超时未认领自动撤回
- 智能推荐目标公司
- 自动化状态通知
