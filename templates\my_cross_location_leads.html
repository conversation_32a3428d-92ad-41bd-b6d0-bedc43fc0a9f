{% extends 'base.html' %}

{% block title %}我的异地到店线索 - CRM系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题区域 -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">
                    <i class="fas fa-user-check me-2"></i>我的异地到店线索
                </h1>
                <p class="page-subtitle">管理您已认领的来自其他公司的异地到店线索</p>
            </div>
            <div class="text-end">
                <div class="btn-group-theme">
                    <a href="{{ url_for('leads.leads_unified') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回线索管理
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 过滤器 -->
    <div class="card mb-4">
        <div class="card-body">
            <p class="text-muted small mb-3">这里显示了您已认领的来自其他公司的异地到店线索。</p>
            <form method="GET" action="{{ url_for('leads.my_cross_location_leads') }}" class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label for="source_company" class="form-label">来源公司</label>
                    <select class="form-select" id="source_company" name="source_company_id">
                        <option value="">全部</option>
                        {% for company in source_companies %}
                        <option value="{{ company.id }}" {% if request.args.get('source_company_id')|int == company.id %}selected{% endif %}>
                            {{ company.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">当前阶段</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">全部</option>
                        <option value="is_visited" {% if request.args.get('status') == 'is_visited' %}selected{% endif %}>到面</option>
                        <option value="is_intentional" {% if request.args.get('status') == 'is_intentional' %}selected{% endif %}>意向客户</option>
                        <option value="is_compliant" {% if request.args.get('status') == 'is_compliant' %}selected{% endif %}>已合规</option>
                        <option value="is_car_selected" {% if request.args.get('status') == 'is_car_selected' %}selected{% endif %}>提车</option>
                        <option value="is_deal_done" {% if request.args.get('status') == 'is_deal_done' %}selected{% endif %}>签约</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="search" class="form-label">搜索</label>
                    <input type="text" class="form-control" id="search" name="search" placeholder="姓名/电话/省份/城市" value="{{ request.args.get('search', '') }}">
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-theme-primary w-100"><i class="bi bi-search"></i> 查询</button>
                </div>
            </form>
        </div>
    </div>
            
    <!-- 线索表格 -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>操作</th>
                            <th>来源公司</th>
                            <th>推送人</th>
                            <th>姓名</th>
                            <th>电话</th>
                            <th>省份</th>
                            <th>城市</th>
                            <th>认领时间</th>
                            <th>当前阶段</th>
                            <th>最后活动</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if leads_data %}
                            {% for item in leads_data %}
                            <tr>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('leads.view_lead', lead_id=item.lead.id) }}" class="btn btn-sm btn-theme-info">查看</a>
                                        <a href="{{ url_for('leads.edit_lead', lead_id=item.lead.id) }}" class="btn btn-sm btn-theme-primary ms-1">编辑</a>
                                        <form action="{{ url_for('leads.release_leads_to_pool_route') }}" method="POST" style="display:inline;">
                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                            <input type="hidden" name="lead_ids" value="{{ item.lead.id }}">
                                            <button type="submit" class="btn btn-sm btn-outline-secondary ms-1">释放</button>
                                        </form>
                                    </div>
                                </td>
                                <td><span class="badge bg-info">{{ item.lead.company.name if item.lead.company else '未知公司' }}</span></td>
                                <td>{{ item.pusher_name }}</td>
                                <td>{{ item.lead.name }}</td>
                                <td>{{ item.lead.phone | mask_phone }}</td>
                                <td>{{ item.lead.company_name }}</td>
                                <td>{{ item.lead.email }}</td>
                                <td>{{ get_claim_time(item.lead.id).strftime('%Y-%m-%d %H:%M') if get_claim_time(item.lead.id) else '未知' }}</td>
                                <td>{{ item.lead.current_stage }}</td>
                                <td>{{ item.lead.last_activity_time.strftime('%Y-%m-%d %H:%M') if item.lead.last_activity_time else '无' }}</td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <div class="alert alert-info mb-0">
                                        <i class="fas fa-info-circle me-2"></i> 您当前没有认领任何异地到店线索
                                    </div>
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            {% if pagination and pagination.pages > 1 %}
                {% include '_pagination.html' %}
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 