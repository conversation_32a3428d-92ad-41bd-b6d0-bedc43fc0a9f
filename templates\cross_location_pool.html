{% extends 'base.html' %}

{% block title %}异地到店池 - CRM系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题区域 -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">
                    <i class="fas fa-map-marked-alt me-2"></i>异地到店池
                </h1>
                <p class="page-subtitle">查看和认领其他公司推送的异地到店线索，扩展业务覆盖范围</p>
            </div>
            <div class="text-end">
                <div class="btn-group-theme">
                    <a href="{{ url_for('leads.leads_unified') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回线索管理
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-primary shadow-sm">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-inbox fa-2x"></i>
                    </div>
                    <h5 class="card-title text-primary">待认领线索</h5>
                    <h3 class="mb-0">{{ leads|length }}</h3>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-info shadow-sm">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-building fa-2x"></i>
                    </div>
                    <h5 class="card-title text-info">来源公司数</h5>
                    <h3 class="mb-0">{{ source_companies|length }}</h3>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-success shadow-sm">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                    <h5 class="card-title text-success">今日新增</h5>
                    <h3 class="mb-0">{{ leads|selectattr('last_cross_location_push_time')|selectattr('last_cross_location_push_time', 'ge', today_start)|list|length }}</h3>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-warning shadow-sm">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                    <h5 class="card-title text-warning">超时未处理</h5>
                    <h3 class="mb-0">{{ leads|selectattr('last_cross_location_push_time')|selectattr('last_cross_location_push_time', 'lt', three_days_ago)|list|length }}</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body py-2">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <select class="form-select form-select-sm" id="source-company-filter">
                                <option value="">所有来源公司</option>
                                {% for company in source_companies %}
                                <option value="{{ company.id }}" {% if request.args.get('source_company_id')|int == company.id %}selected{% endif %}>
                                    {{ company.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select form-select-sm" id="time-filter">
                                <option value="">所有时间</option>
                                <option value="today">今天</option>
                                <option value="week">本周</option>
                                <option value="month">本月</option>
                                <option value="overdue">超时未处理</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select form-select-sm" id="stage-filter">
                                <option value="">所有阶段</option>
                                <option value="初步接触">初步接触</option>
                                <option value="需求确认">需求确认</option>
                                <option value="方案报价">方案报价</option>
                                <option value="商务谈判">商务谈判</option>
                                <option value="合同签署">合同签署</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <div class="input-group input-group-sm">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="search-input"
                                       placeholder="搜索姓名、电话、公司..." value="{{ request.args.get('search', '') }}">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-outline-secondary btn-sm w-100" id="reset-filter-btn">
                                <i class="fas fa-undo"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 线索列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> 异地到店线索列表
                        </h5>
                        <div class="text-muted">
                            共 <span id="totalCount">{{ leads|length }}</span> 条线索
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="leadsTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="80">序号</th>
                                    <th>来源公司</th>
                                    <th>客户信息</th>
                                    <th>联系方式</th>
                                    <th>客户公司</th>
                                    <th>当前阶段</th>
                                    <th>推送时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for lead in leads %}
                                <tr class="lead-item"
                                    data-source-company="{{ lead.company_id }}"
                                    data-stage="{{ lead.current_stage }}"
                                    data-push-time="{{ lead.last_cross_location_push_time.strftime('%Y-%m-%d') if lead.last_cross_location_push_time else '' }}"
                                    data-search="{{ lead.name|lower }} {{ lead.phone|mask_phone }} {{ (lead.company_name or '')|lower }}">
                                    <td class="text-center">
                                        <div class="lead-number" style="width: 40px; height: 40px; border-radius: 50%; background: #f8f9fa; border: 2px solid #dee2e6; display: flex; align-items: center; justify-content: center; font-weight: bold; color: #6c757d; font-size: 14px; margin: 0 auto;">
                                            {{ loop.index }}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <i class="fas fa-building me-1"></i>
                                            {{ lead.company.name }}
                                        </span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ lead.name }}</strong>
                                            {% if lead.last_cross_location_push_time and (now - lead.last_cross_location_push_time).days > 3 %}
                                            <span class="badge bg-warning text-dark ms-2">超时</span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if lead.phone %}
                                        <div><i class="fas fa-phone text-muted me-1"></i> {{ lead.phone|mask_phone }}</div>
                                        {% endif %}
                                        {% if lead.email %}
                                        <div><i class="fas fa-envelope text-muted me-1"></i> {{ lead.email }}</div>
                                        {% endif %}
                                        {% if not lead.phone and not lead.email %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <i class="fas fa-building text-primary me-1"></i>
                                        {{ lead.company_name or '-' }}
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ lead.current_stage or '未设置' }}</span>
                                    </td>
                                    <td>
                                        {% if lead.last_cross_location_push_time %}
                                        <div>{{ lead.last_cross_location_push_time.strftime('%Y-%m-%d') }}</div>
                                        <small class="text-muted">{{ lead.last_cross_location_push_time.strftime('%H:%M') }}</small>
                                        {% else %}
                                        <span class="text-muted">未知</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('leads.view_lead', lead_id=lead.id) }}" class="btn btn-theme-info btn-sm" title="查看详情">
                                                <i class="fas fa-eye"></i> 查看
                                            </a>
                                            <form action="{{ url_for('leads.claim_cross_location_lead_route', lead_id=lead.id) }}" method="POST" style="display:inline;">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                <button type="submit" class="btn btn-theme-success btn-sm" title="认领线索">
                                                    <i class="fas fa-hand-paper"></i> 认领
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="text-center py-5">
                                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">暂无异地到店线索</h5>
                                        <p class="text-muted">当前没有来自其他公司推送的异地到店线索</p>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分页 -->
    {% if pagination and pagination.pages > 1 %}
    <div class="row mt-3">
        <div class="col-12">
            {% include '_pagination.html' %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
// 异地到店池管理页面交互逻辑
$(document).ready(function() {
    // 筛选功能
    function filterLeads() {
        const sourceCompanyFilter = $('#source-company-filter').val();
        const timeFilter = $('#time-filter').val();
        const stageFilter = $('#stage-filter').val();
        const searchText = $('#search-input').val().toLowerCase();

        let visibleCount = 0;
        const today = new Date();
        const threeDaysAgo = new Date(today.getTime() - 3 * 24 * 60 * 60 * 1000);
        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

        $('.lead-item').each(function() {
            const $item = $(this);
            let show = true;

            // 来源公司筛选
            if (sourceCompanyFilter && $item.data('source-company') != sourceCompanyFilter) show = false;

            // 阶段筛选
            if (stageFilter && $item.data('stage') !== stageFilter) show = false;

            // 时间筛选
            if (timeFilter && $item.data('push-time')) {
                const pushDate = new Date($item.data('push-time'));
                switch(timeFilter) {
                    case 'today':
                        if (pushDate.toDateString() !== today.toDateString()) show = false;
                        break;
                    case 'week':
                        if (pushDate < weekAgo) show = false;
                        break;
                    case 'month':
                        if (pushDate < monthAgo) show = false;
                        break;
                    case 'overdue':
                        if (pushDate >= threeDaysAgo) show = false;
                        break;
                }
            }

            // 搜索文本筛选
            if (searchText && !$item.data('search').includes(searchText)) show = false;

            $item.toggle(show);
            if (show) visibleCount++;
        });

        $('#totalCount').text(visibleCount);
    }

    // 绑定筛选事件
    $('#source-company-filter, #time-filter, #stage-filter').change(filterLeads);
    $('#search-input').on('input', filterLeads);

    // 重置筛选
    $('#reset-filter-btn').click(function() {
        $('#source-company-filter, #time-filter, #stage-filter').val('');
        $('#search-input').val('');
        $('.lead-item').show();
        $('#totalCount').text($('.lead-item').length);
    });

    // 认领确认
    $('form[action*="claim_cross_location_lead_route"]').submit(function(e) {
        const leadName = $(this).closest('tr').find('strong').text();
        if (!confirm(`确定要认领线索"${leadName}"吗？认领后该线索将分配给您处理。`)) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}