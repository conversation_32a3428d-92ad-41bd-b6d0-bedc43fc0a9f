# -*- coding: utf-8 -*-
from models import db, Role, Permission

# 步骤 1: 将 permissions_data 移动到模块级别
permissions_data = [
    # 用户与组织管理
    {'code': 'view_all_companies', 'name': '查看所有公司', 'description': '允许跨公司查看公司列表', 'category': '用户与组织管理'},
    {'code': 'company_view', 'name': '查看公司管理页面', 'description': '访问公司管理页面', 'category': '用户与组织管理'},
    {'code': 'company_manage', 'name': '公司管理操作', 'description': '创建、编辑、删除公司', 'category': '用户与组织管理'},
    {'code': 'manage_companies', 'name': '公司管理', 'description': '创建、编辑、删除公司', 'category': '用户与组织管理'},
    {'code': 'department_view', 'name': '查看部门管理页面', 'description': '访问部门管理页面', 'category': '用户与组织管理'},
    {'code': 'department_manage', 'name': '部门管理', 'description': '在本公司内创建、编辑、删除部门', 'category': '用户与组织管理'},
    {'code': 'user_view', 'name': '查看员工管理页面', 'description': '访问员工管理页面', 'category': '用户与组织管理'},
    {'code': 'user_manage', 'name': '用户管理', 'description': '添加、编辑、删除用户', 'category': '用户与组织管理'},
    {'code': 'view_users', 'name': '查看用户', 'description': '查看用户列表的基本权限', 'category': '用户与组织管理'},
    {'code': 'organization_view', 'name': '查看组织架构', 'description': '访问组织架构页面', 'category': '用户与组织管理'},

    # 线索管理
    {'code': 'lead_import', 'name': '导入线索', 'description': '允许导入线索到自己的公司或部门', 'category': '线索管理'},
    {'code': 'view_all_leads', 'name': '查看所有线索', 'description': '查看系统内所有线索', 'category': '线索管理'},
    {'code': 'view_company_leads', 'name': '查看公司线索', 'description': '查看本公司所有线索', 'category': '线索管理'},
    {'code': 'view_department_leads', 'name': '查看部门线索', 'description': '查看本部门及子部门所有线索', 'category': '线索管理'},
    {'code': 'assign_leads', 'name': '分配线索', 'description': '向下分配线索给其他用户', 'category': '线索管理'},
    {'code': 'export_leads', 'name': '导出线索', 'description': '导出其有权查看的线索', 'category': '线索管理'},
    {'code': 'manage_import_requests', 'name': '管理导入请求', 'description': '批准或拒绝其他公司向本公司发起的线索导入请求', 'category': '线索管理'},
]

def setup_roles_and_permissions(app):
    """
    初始化或重置角色和权限。
    这个函数是幂等的，可以安全地重复运行。
    """
    roles_permissions = {
        'super_admin': {
            'name': '超级管理员',
            'description': '拥有系统的全部权限',
            'permissions': [p['code'] for p in permissions_data] # 所有权限
        },
        'company_admin': {
            'name': '公司管理员',
            'description': '管理本公司的所有事务',
            'permissions': [
                'organization_view', 'department_view', 'department_manage', 'user_view', 'user_manage', 'view_users',
                'lead_import', 'view_company_leads', 'view_department_leads', 'assign_leads', 'export_leads',
                'manage_import_requests',
            ]
        },
        'department_admin': {
            'name': '部门管理员',
            'description': '管理本部门及子部门的员工和线索',
            'permissions': [
                'organization_view', 'user_view', 'user_manage', 'view_users',
                'lead_import', 'view_department_leads', 'assign_leads', 'export_leads',
            ]
        },
        'sales': {
            'name': '销售',
            'description': '普通销售人员，只能操作自己的线索',
            'permissions': [
                'organization_view', 'view_users', # 允许查看用户列表以便分配线索等操作
                'lead_import',
                'export_leads'
            ]
        }
    }
    
    with app.app_context():
        # 1. 创建或更新权限
        app.logger.info("开始同步权限...")
        for perm_data in permissions_data:
            # 尝试通过唯一的 code 查找
            perm = Permission.query.filter_by(code=perm_data['code']).first()
            if perm:
                # 如果找到了，更新名称和描述以防有变
                if perm.name != perm_data['name'] or perm.description != perm_data['description']:
                    perm.name = perm_data['name']
                    perm.description = perm_data['description']
                    app.logger.info("  通过 code 更新权限: {} ({})".format(perm.name, perm.code))
            else:
                # 如果通过 code 没找到，再通过 name 查找，以处理旧数据或 code 变更的情况
                perm_by_name = Permission.query.filter_by(name=perm_data['name']).first()
                if perm_by_name:
                    # 如果找到了同名的，更新它的 code 和描述
                    perm_by_name.code = perm_data['code']
                    perm_by_name.description = perm_data['description']
                    app.logger.info("  通过 name 找到权限 '{}', 更新其 code 为 '{}'".format(perm_data['name'], perm_data['code']))
                else:
                    # 如果 code 和 name 都不存在，则创建新权限
                    new_perm = Permission(
                        code=perm_data['code'], 
                        name=perm_data['name'], 
                        description=perm_data['description']
                    )
                    db.session.add(new_perm)
                    app.logger.info("  创建新权限: {} ({})".format(new_perm.name, new_perm.code))

        db.session.commit()
        app.logger.info("权限同步完成。")

        # 2. 创建或更新角色并分配权限
        app.logger.info("开始同步角色和分配权限...")
        for role_code, role_info in roles_permissions.items():
            role = Role.query.filter_by(code=role_code).first()
            if not role:
                role = Role(code=role_code, name=role_info['name'], description=role_info['description'], is_system=True)
                db.session.add(role)
                app.logger.info("  创建新角色: {} ({})".format(role.name, role.code))
            else:
                if role.name != role_info['name'] or role.description != role_info['description']:
                    role.name = role_info['name']
                    role.description = role_info['description']
                    app.logger.info("  更新角色: {} ({})".format(role.name, role.code))

            # 分配权限
            role.permissions.clear() # 清空现有权限，确保精确匹配
            for perm_code in role_info['permissions']:
                perm = Permission.query.filter_by(code=perm_code).first()
                if perm:
                    role.permissions.append(perm)
            
            app.logger.info("  为角色 '{}' 分配了 {} 个权限。".format(role.name, len(role.permissions)))

        db.session.commit()
        app.logger.info("角色和权限分配完成。") 