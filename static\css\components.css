/* ========================================
   CRM系统组件样式库
   专门的组件样式定义
======================================== */

/* ========================================
   数据表格组件
======================================== */
.data-table {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.data-table-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.data-table-title {
    font-size: 1.125rem;
    font-weight: var(--font-weight-semibold);
    color: var(--dark-color);
    margin: 0;
}

.data-table-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.data-table-search {
    position: relative;
    min-width: 250px;
}

.data-table-search input {
    padding-left: 2.5rem;
}

.data-table-search .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-color);
}

/* ========================================
   状态徽章组件
======================================== */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius-xl);
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: currentColor;
}

.status-badge-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.status-badge-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #856404;
}

.status-badge-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.status-badge-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.status-badge-primary {
    background-color: rgba(255, 107, 0, 0.1);
    color: var(--primary-color);
}

/* ========================================
   操作按钮组
======================================== */
.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: var(--border-radius-sm);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.875rem;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.action-btn-edit {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.action-btn-edit:hover {
    background-color: var(--info-color);
    color: var(--white);
}

.action-btn-delete {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.action-btn-delete:hover {
    background-color: var(--danger-color);
    color: var(--white);
}

.action-btn-view {
    background-color: rgba(255, 107, 0, 0.1);
    color: var(--primary-color);
}

.action-btn-view:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

/* ========================================
   筛选器组件
======================================== */
.filter-panel {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.filter-row {
    display: flex;
    gap: var(--spacing-md);
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* ========================================
   分页组件
======================================== */
.pagination-wrapper {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--white);
    border-top: 1px solid var(--border-color);
}

.pagination-info {
    color: var(--gray-color);
    font-size: 0.875rem;
}

.pagination {
    margin: 0;
}

.page-link {
    border: 1px solid var(--border-color);
    color: var(--gray-color);
    padding: 0.5rem 0.75rem;
    transition: var(--transition-fast);
}

.page-link:hover {
    background-color: var(--light-gray);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

/* ========================================
   统计卡片网格
======================================== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stats-card-enhanced {
    background: linear-gradient(135deg, var(--white) 0%, #fafbfc 100%);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    position: relative;
    overflow: hidden;
    transition: var(--transition-normal);
    border: 1px solid transparent;
}

.stats-card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.stats-card-enhanced:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    margin-bottom: var(--spacing-md);
}

.stats-icon-primary {
    background: rgba(255, 107, 0, 0.1);
    color: var(--primary-color);
}

.stats-icon-success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.stats-icon-info {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.stats-icon-warning {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.stats-value {
    font-size: 2rem;
    font-weight: var(--font-weight-semibold);
    line-height: 1;
    margin-bottom: var(--spacing-xs);
    color: var(--dark-color);
}

.stats-label {
    color: var(--gray-color);
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-sm);
}

.stats-change {
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.stats-change-positive {
    color: var(--success-color);
}

.stats-change-negative {
    color: var(--danger-color);
}

/* ========================================
   表单布局组件
======================================== */
.form-section {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.form-section-title {
    font-size: 1.125rem;
    font-weight: var(--font-weight-semibold);
    color: var(--dark-color);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.form-grid-2 {
    grid-template-columns: repeat(2, 1fr);
}

.form-grid-3 {
    grid-template-columns: repeat(3, 1fr);
}

/* ========================================
   增强表格组件
======================================== */
.enhanced-table {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-lg);
}

.enhanced-table-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: between;
    align-items: center;
}

.enhanced-table-title {
    font-size: 1.125rem;
    font-weight: var(--font-weight-semibold);
    color: var(--dark-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.enhanced-table-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.enhanced-table table {
    width: 100%;
    margin: 0;
}

.enhanced-table th {
    background: var(--light-gray);
    font-weight: var(--font-weight-semibold);
    color: var(--dark-color);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.enhanced-table td {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
}

.enhanced-table tbody tr {
    transition: var(--transition-fast);
}

.enhanced-table tbody tr:hover {
    background-color: rgba(255, 107, 0, 0.05);
}

.enhanced-table tbody tr:last-child td {
    border-bottom: none;
}

/* 排名徽章 */
.rank-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    font-weight: var(--font-weight-semibold);
    font-size: 0.875rem;
}

.rank-badge-1 {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #8b5a00;
}

.rank-badge-2 {
    background: linear-gradient(135deg, #c0c0c0, #e5e5e5);
    color: #666;
}

.rank-badge-3 {
    background: linear-gradient(135deg, #cd7f32, #daa520);
    color: #fff;
}

.rank-badge-default {
    background: var(--light-gray);
    color: var(--gray-color);
}

/* 转化率徽章 */
.conversion-badge {
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius-xl);
    font-size: 0.75rem;
    font-weight: var(--font-weight-semibold);
}

.conversion-badge-excellent {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.conversion-badge-good {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
}

.conversion-badge-poor {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

/* ========================================
   表单增强组件
======================================== */
.form-floating-enhanced {
    position: relative;
    margin-bottom: var(--spacing-lg);
}

.form-floating-enhanced .form-control {
    height: 56px;
    padding: 1rem 0.75rem 0.25rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    transition: var(--transition-normal);
}

.form-floating-enhanced .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 107, 0, 0.15);
}

.form-floating-enhanced label {
    position: absolute;
    top: 0;
    left: 0.75rem;
    height: 100%;
    padding: 1rem 0;
    pointer-events: none;
    border: none;
    transform-origin: 0 0;
    transition: var(--transition-normal);
    color: var(--gray-color);
}

.form-floating-enhanced .form-control:focus ~ label,
.form-floating-enhanced .form-control:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    color: var(--primary-color);
}

/* ========================================
   按钮组增强
======================================== */
.btn-group-enhanced {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.btn-group-enhanced .btn {
    flex: 1;
    min-width: 120px;
}

.btn-icon-text {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-icon-text i {
    font-size: 0.875rem;
}

/* ========================================
   响应式设计
======================================== */
@media (max-width: 768px) {
    .form-grid,
    .form-grid-2,
    .form-grid-3 {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        min-width: auto;
    }

    .enhanced-table-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .enhanced-table-actions {
        justify-content: center;
    }

    .enhanced-table {
        font-size: 0.875rem;
    }

    .enhanced-table th,
    .enhanced-table td {
        padding: var(--spacing-sm);
    }

    .btn-group-enhanced {
        flex-direction: column;
    }

    .btn-group-enhanced .btn {
        min-width: auto;
    }
}
