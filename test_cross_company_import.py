#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试跨公司导入功能的脚本
"""

import os
import sys
import csv
import io
import tempfile
from datetime import datetime

def test_cross_company_import():
    """测试跨公司导入功能"""
    try:
        # 确保在正确的目录
        if not os.path.isfile('app.py'):
            print("❌ 错误: 请在项目根目录下运行此脚本！")
            return False

        # 导入应用和模型
        from app import create_app
        from config import Config
        from models import db, Lead, User, Company, LeadImportRequest
        from utils.lead_utils import _process_lead_import_data
        
        app = create_app(Config)
        
        with app.app_context():
            print("🧪 测试跨公司导入功能")
            print("="*60)
            
            # 1. 检查基础数据
            companies = Company.query.all()
            users = User.query.filter_by(status='active').all()
            
            if len(companies) < 2:
                print("❌ 需要至少2个公司来测试跨公司导入")
                return False
                
            if len(users) < 2:
                print("❌ 需要至少2个活跃用户来测试导入")
                return False
            
            # 选择两个不同的公司
            company_a = companies[0]
            company_b = companies[1]
            
            # 找到每个公司的用户
            user_a = User.query.filter_by(company_id=company_a.id, status='active').first()
            user_b = User.query.filter_by(company_id=company_b.id, status='active').first()
            
            if not user_a:
                user_a = users[0]  # 使用第一个用户
            if not user_b:
                user_b = users[1] if len(users) > 1 else users[0]  # 使用第二个用户
            
            print(f"✅ 源公司: {company_a.name} (用户: {user_a.name})")
            print(f"✅ 目标公司: {company_b.name} (用户: {user_b.name})")
            
            # 2. 创建测试CSV数据
            print("\n🔧 创建测试CSV数据...")
            test_data = [
                ['姓名', '电话', '省份', '城市', '备注'],
                ['跨公司客户1', '13900000001', '广东省', '深圳市', '跨公司导入测试1'],
                ['跨公司客户2', '13900000002', '北京市', '北京市', '跨公司导入测试2'],
                ['跨公司客户3', '13900000003', '上海市', '上海市', '跨公司导入测试3'],
            ]
            
            # 创建临时CSV文件
            temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8')
            writer = csv.writer(temp_file)
            for row in test_data:
                writer.writerow(row)
            temp_file.close()
            
            print(f"   创建了包含 {len(test_data)-1} 行数据的测试CSV文件: {temp_file.name}")
            
            # 3. 创建跨公司导入请求
            print("\n📝 创建跨公司导入请求...")
            
            import_request = LeadImportRequest(
                requester_user_id=user_a.id,
                requester_company_id=company_a.id,
                target_company_id=company_b.id,
                file_path=temp_file.name,
                original_filename='test_cross_company_import.csv',
                status='pending',
                notes='自动化测试跨公司导入功能'
            )
            
            db.session.add(import_request)
            db.session.commit()
            
            print(f"   ✅ 创建了导入请求 ID: {import_request.id}")
            print(f"   状态: {import_request.status}")
            print(f"   从 {company_a.name} 到 {company_b.name}")
            
            # 4. 模拟管理员审批
            print("\n👨‍💼 模拟目标公司管理员审批...")
            
            # 记录审批前的线索数量
            before_count = Lead.query.filter_by(company_id=company_b.id).count()
            print(f"   审批前目标公司线索数量: {before_count}")
            
            # 更新请求状态为处理中
            import_request.status = 'processing'
            import_request.approver_user_id = user_b.id
            import_request.processed_at = datetime.now()
            db.session.commit()
            
            # 读取文件并处理导入
            with open(temp_file.name, 'rb') as f:
                file_content = f.read()
            
            from utils.lead_utils import _process_file_content, _validate_csv_format
            
            file_stream = _process_file_content(file_content, import_request.original_filename)
            if file_stream is None:
                print("   ❌ 无法处理文件内容")
                return False
                
            if not _validate_csv_format(file_stream):
                print("   ❌ 文件格式无效")
                return False
            
            # 执行导入
            success_count, error_count, error_messages = _process_lead_import_data(
                file_stream,
                company_b.id,
                user_b.id
            )
            
            # 更新请求结果
            import_request.status = 'completed'
            import_request.imported_count = success_count
            import_request.failed_count = error_count
            if error_messages:
                import_request.failure_reason = "; ".join(error_messages[:3])
            
            db.session.commit()
            
            # 检查结果
            after_count = Lead.query.filter_by(company_id=company_b.id).count()
            print(f"   审批后目标公司线索数量: {after_count}")
            print(f"   成功导入: {success_count}")
            print(f"   失败数量: {error_count}")
            
            if error_messages:
                print("   错误信息:")
                for msg in error_messages[:3]:
                    print(f"     - {msg}")
            
            # 5. 验证导入结果
            print("\n✅ 验证导入结果...")
            
            imported_leads = Lead.query.filter(
                Lead.company_id == company_b.id,
                Lead.name.like('跨公司客户%'),
                Lead.owner_id == user_b.id
            ).all()
            
            print(f"   在目标公司找到 {len(imported_leads)} 条导入的线索:")
            for lead in imported_leads:
                print(f"     - {lead.name}: {lead.phone} (负责人: {lead.owner.name})")
            
            # 6. 检查导入请求状态
            print("\n📊 检查导入请求最终状态:")
            final_request = LeadImportRequest.query.get(import_request.id)
            print(f"   请求状态: {final_request.status}")
            print(f"   导入成功: {final_request.imported_count}")
            print(f"   导入失败: {final_request.failed_count}")
            if final_request.failure_reason:
                print(f"   失败原因: {final_request.failure_reason}")
            
            # 7. 清理测试数据
            print("\n🧹 清理测试数据...")
            cleanup_count = 0
            
            # 删除导入的线索
            for lead in imported_leads:
                db.session.delete(lead)
                cleanup_count += 1
            
            # 删除导入请求
            db.session.delete(final_request)
            
            # 删除临时文件
            try:
                os.unlink(temp_file.name)
                print(f"   删除临时文件: {temp_file.name}")
            except:
                pass
            
            db.session.commit()
            print(f"   清理了 {cleanup_count} 条测试线索和1个导入请求")
            
            # 8. 总结
            print("\n" + "="*60)
            print("📊 跨公司导入测试总结:")
            print("="*60)
            
            if success_count > 0:
                print("✅ 跨公司导入功能正常工作")
                print("✅ 导入请求创建成功")
                print("✅ 审批流程正常")
                print("✅ 数据导入到目标公司成功")
            else:
                print("❌ 跨公司导入功能可能有问题")
                
            if error_count == 0:
                print("✅ 所有测试数据导入成功")
            else:
                print(f"⚠️  有 {error_count} 条数据导入失败")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_cross_company_import()
