"""
线索管理系统配置文件
包含系统的各种配置参数和设置
"""

class LeadManagementConfig:
    """线索管理配置类"""
    
    # 分页设置
    LEADS_PER_PAGE = 20
    ACTIVITIES_PER_PAGE = 10
    
    # 缓存设置
    CACHE_TIMEOUT = 300  # 5分钟
    STATISTICS_CACHE_TIMEOUT = 600  # 10分钟
    
    # 线索保护期设置（天）
    LEAD_PROTECTION_DAYS = 50
    
    # 公海自动释放设置
    AUTO_RELEASE_TO_PUBLIC_SEA = True
    AUTO_RELEASE_DAYS = 30  # 30天未跟进自动释放
    
    # 权限设置
    PERMISSION_LEVELS = {
        'READ': 1,
        'edit': 2,
        'delete': 3,
        'assign': 4,
        'admin': 5
    }
    
    # 角色权限映射
    ROLE_PERMISSIONS = {
        'super_admin': ['read', 'edit', 'delete', 'assign', 'admin'],
        'company_admin': ['read', 'edit', 'delete', 'assign'],
        'department_admin': ['read', 'edit', 'assign'],
        'sales': ['read', 'edit']
    }
    
    # 线索状态配置
    LEAD_STAGES = [
        {'key': 'new', 'name': '新线索', 'color': 'secondary'},
        {'key': 'called', 'name': '已拨电话', 'color': 'info'},
        {'key': 'connected', 'name': '已接通', 'color': 'primary'},
        {'key': 'valid_call', 'name': '有效通话', 'color': 'success'},
        {'key': 'wechat_added', 'name': '已加微信', 'color': 'warning'},
        {'key': 'intentional', 'name': '意向客户', 'color': 'orange'},
        {'key': 'visited', 'name': '已到面', 'color': 'purple'},
        {'key': 'compliant', 'name': '信息合规', 'color': 'teal'},
        {'key': 'deal_done', 'name': '已签约', 'color': 'success'},
        {'key': 'car_selected', 'name': '已提车', 'color': 'dark'}
    ]
    
    # 线索池类型配置
    POOL_TYPES = {
        'PRIVATE': {'name': '私有池', 'description': '公司内部线索池'},
        'PUBLIC_SEA': {'name': '公海池', 'description': '公共线索池'},
        'CROSS_LOCATION_PENDING': {'name': '异地到店池', 'description': '异地到店待认领池'}
    }
    
    # 导航菜单配置
    NAVIGATION_CONFIG = {
        'leads': {
            'name': '线索管理',
            'icon': 'bi-kanban',
            'url': 'leads.leads_unified',
            'permissions': ['read']
        }
    }
    
    # 统一界面标签页配置
    TAB_CONFIG = [
        {
            'id': 'my-leads',
            'name': '我的线索',
            'icon': 'bi-person-check',
            'api_endpoint': '/api/leads/my-leads',
            'permissions': ['read']
        },
        {
            'id': 'team-leads',
            'name': '团队线索',
            'icon': 'bi-people',
            'api_endpoint': '/api/leads/team-leads',
            'permissions': ['assign'],
            'roles': ['company_admin', 'department_admin', 'super_admin']
        },
        {
            'id': 'public-sea',
            'name': '线索公海',
            'icon': 'bi-water',
            'api_endpoint': '/api/leads/public-sea',
            'permissions': ['read']
        },
        {
            'id': 'cross-location',
            'name': '异地到店',
            'icon': 'bi-building',
            'api_endpoint': '/api/leads/cross-location',
            'permissions': ['read']
        },
        {
            'id': 'pool-management',
            'name': '线索池管理',
            'icon': 'bi-collection',
            'api_endpoint': '/api/pools/management',
            'permissions': ['admin'],
            'roles': ['company_admin', 'super_admin']
        }
    ]
    
    # 批量操作配置
    BATCH_OPERATIONS = {
        'assign': {
            'name': '批量分配',
            'icon': 'bi-person-plus',
            'permissions': ['assign'],
            'max_items': 50
        },
        'release': {
            'name': '批量释放',
            'icon': 'bi-arrow-up-circle',
            'permissions': ['assign'],
            'max_items': 50
        },
        'claim': {
            'name': '批量认领',
            'icon': 'bi-check2-square',
            'permissions': ['read'],
            'max_items': 20
        }
    }
    
    # 搜索配置
    SEARCH_CONFIG = {
        'fields': ['name', 'company_name', 'phone', 'email'],
        'min_length': 2,
        'max_results': 100
    }
    
    # 筛选配置
    FILTER_CONFIG = {
        'stage': {
            'type': 'select',
            'options': LEAD_STAGES
        },
        'owner': {
            'type': 'select',
            'dynamic': True,
            'api_endpoint': '/api/users/list'
        },
        'date_range': {
            'type': 'daterange',
            'default_range': 30  # 默认30天
        },
        'company': {
            'type': 'select',
            'dynamic': True,
            'api_endpoint': '/api/companies/list'
        }
    }
    
    # 性能监控配置
    PERFORMANCE_CONFIG = {
        'slow_query_threshold': 1.0,  # 1秒
        'enable_query_logging': True,
        'enable_cache_monitoring': True
    }
    
    # 通知配置
    NOTIFICATION_CONFIG = {
        'lead_assigned': {
            'enabled': True,
            'template': 'lead_assigned_notification'
        },
        'lead_claimed': {
            'enabled': True,
            'template': 'lead_claimed_notification'
        },
        'cross_location_pushed': {
            'enabled': True,
            'template': 'cross_location_push_notification'
        }
    }
    
    # API配置
    API_CONFIG = {
        'rate_limit': '100/minute',
        'enable_cors': True,
        'response_format': 'json'
    }
    
    # 数据导出配置
    EXPORT_CONFIG = {
        'formats': ['csv', 'xlsx', 'pdf'],
        'max_records': 10000,
        'include_activities': False
    }
    
    # 系统集成配置
    INTEGRATION_CONFIG = {
        'enable_email_sync': False,
        'enable_phone_integration': False,
        'enable_wechat_integration': False
    }

class DevelopmentConfig(LeadManagementConfig):
    """开发环境配置"""
    DEBUG = True
    CACHE_TIMEOUT = 60  # 开发环境缓存时间短一些
    PERFORMANCE_CONFIG = {
        'slow_query_threshold': 0.5,
        'enable_query_logging': True,
        'enable_cache_monitoring': True
    }

class ProductionConfig(LeadManagementConfig):
    """生产环境配置"""
    DEBUG = False
    CACHE_TIMEOUT = 600  # 生产环境缓存时间长一些
    PERFORMANCE_CONFIG = {
        'slow_query_threshold': 2.0,
        'enable_query_logging': False,
        'enable_cache_monitoring': True
    }

class TestingConfig(LeadManagementConfig):
    """测试环境配置"""
    TESTING = True
    CACHE_TIMEOUT = 0  # 测试环境不使用缓存
    AUTO_RELEASE_TO_PUBLIC_SEA = False  # 测试环境不自动释放

# 根据环境选择配置
def get_config(env='development'):
    """根据环境获取配置"""
    configs = {
        'development': DevelopmentConfig,
        'production': ProductionConfig,
        'testing': TestingConfig
    }
    return configs.get(env, DevelopmentConfig)
