from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from datetime import datetime
from models.lead import Lead
from models.lead_deal import LeadDealHistory
from models import db
from utils.notification import send_deal_notification

lead_deal = Blueprint('lead_deal', __name__)

@lead_deal.route('/leads/<int:lead_id>/deal-status', methods=['GET', 'POST'])
@login_required
def update_deal_status(lead_id):
    lead = Lead.query.get_or_404(lead_id)
    
    # 检查权限
    if lead.owner_id != current_user.id and not current_user.has_role('admin'):
        flash('您没有权限更新此线索的签约状态', 'error')
        return redirect(url_for('lead.detail', lead_id=lead_id))
    
    if request.method == 'POST':
        new_status = request.form.get('deal_status')
        feedback = request.form.get('feedback')
        deal_amount = request.form.get('deal_amount')
        deal_time = request.form.get('deal_time')
        
        # 创建签约状态历史记录
        history = LeadDealHistory(
            lead_id=lead.id,
            old_status=lead.deal_status,
            new_status=new_status,
            feedback_content=feedback,
            deal_amount=deal_amount,
            deal_time=datetime.strptime(deal_time, '%Y-%m-%d') if deal_time else None,
            created_by=current_user.id
        )
        
        # 更新线索签约状态
        lead.deal_status = new_status
        lead.deal_feedback_time = datetime.utcnow()
        
        db.session.add(history)
        db.session.commit()
        
        # 如果线索不是自录入的，发送签约通知给原始公司
        if not lead.is_self_created and lead.original_company_id:
            send_deal_notification(lead, history)
        
        flash('签约状态更新成功', 'success')
        return redirect(url_for('lead.detail', lead_id=lead_id))
    
    return render_template('lead_deal/status_form.html', lead=lead)

@lead_deal.route('/company/deal-feedback')
@login_required
def company_deal_feedback():
    """查看公司释放的线索签约状态"""
    # 获取当前用户所在公司释放的所有线索
    released_leads = Lead.query.filter_by(
        original_company_id=current_user.company_id,
        is_self_created=False
    ).all()
    
    return render_template('lead_deal/company_feedback.html', leads=released_leads)

@lead_deal.route('/api/deal-statistics')
@login_required
def deal_statistics():
    """获取签约统计数据"""
    company_id = current_user.company_id
    
    # 获取公司的线索签约统计
    total_leads = Lead.query.filter_by(original_company_id=company_id).count()
    dealt_leads = Lead.query.filter_by(
        original_company_id=company_id,
        deal_status='won'
    ).count()
    
    # 计算签约率
    deal_rate = (dealt_leads / total_leads * 100) if total_leads > 0 else 0
    
    # 获取最近的签约记录
    recent_deals = LeadDealHistory.query\
        .join(Lead)\
        .filter(Lead.original_company_id == company_id)\
        .order_by(LeadDealHistory.created_at.desc())\
        .limit(5)\
        .all()
    
    return jsonify({
        'total_leads': total_leads,
        'dealt_leads': dealt_leads,
        'deal_rate': round(deal_rate, 2),
        'recent_deals': [{
            'lead_name': deal.lead.name,
            'deal_status': deal.new_status,
            'deal_time': deal.deal_time.strftime('%Y-%m-%d') if deal.deal_time else None,
            'deal_amount': float(deal.deal_amount) if deal.deal_amount else None
        } for deal in recent_deals]
    })