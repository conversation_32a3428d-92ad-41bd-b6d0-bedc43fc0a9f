
# -*- coding: utf-8 -*-
from flask import Flask, render_template, redirect, url_for, flash, send_file, request, jsonify, current_app, session
from flask_login import Login<PERSON>anager, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from datetime import datetime, timedelta, timezone
from sqlalchemy import or_, desc, asc, case, func, and_
from models import (db, User, Company, Department, Role, Lead, Activity, LeadPool, 
                    LeadStatusHistory, LeadTransferHistory, Notification, Setting) 
from models.setting import decrypt_value
from utils.lead_utils import bp as leads_bp, mask_phone_number
from utils.dashboard_utils import get_dashboard_data
from utils.auth_utils import handle_logout, handle_change_password, create_admin, get_captcha
from utils.user_utils import get_user_list, handle_add_user, handle_edit_user, handle_delete_user, handle_profile
from utils.error_utils import handle_bad_request, handle_forbidden, handle_not_found, handle_server_error, handle_error
from utils.app_init_utils import init_app
from utils.csrf_utils import setup_csrf, add_csrf_header
from utils.logging_utils import setup_logging
from utils.blueprint_utils import register_blueprints
from utils.app_factory_utils import create_app
from utils.department_utils import get_child_departments, get_department_tree
from utils.permission_setup import setup_roles_and_permissions # 导入权限设置函数
from config import ProductionConfig
from flask_migrate import Migrate
from flask_mail import Mail
import io
import csv
import json
import logging
import os
import re # 导入 re 用于 nl2br

# 创建日志记录器
app_logger = logging.getLogger('app')

app = Flask(__name__, static_url_path='', static_folder='static')
app.jinja_env.add_extension('jinja2.ext.do') # 启用 do 扩展
app.jinja_env.filters['mask_phone'] = mask_phone_number # 注册自定义过滤器

# 角色翻译过滤器
def translate_role(role_code):
    """将角色代码翻译为中文名称"""
    role_translations = {
        'super_admin': '超级管理员',
        'company_admin': '公司管理员',
        'department_admin': '部门管理员',
        'employee': '员工',
        'sales': '销售'
    }
    return role_translations.get(role_code, role_code)

app.jinja_env.filters['translate_role'] = translate_role

# 定义并注册 nl2br 过滤器
_paragraph_re = re.compile(r'(\r\n|\r|\n){2,}')
def nl2br(value):
    # 替换单个换行符为<br>
    # 替换两个或多个连续换行符为</p><p> (模拟段落，但Jinja的Markup转义可能需要注意)
    # 更简单和安全的方式是只替换换行符为<br>
    if isinstance(value, str):
        return value.replace('\n', '<br>\n')
    return value
app.jinja_env.filters['nl2br'] = nl2br

# 加载配置
app.config.from_object(ProductionConfig)

# 初始化CSRF保护
csrf = setup_csrf(app)

# 配置日志
setup_logging(app)

# 初始化数据库
db.init_app(app)
migrate = Migrate(app, db)

# --- 定义邮件配置加载函数 ---
def load_email_config(flask_app):
    MAIL_SETTING_KEYS = [
        'MAIL_SERVER', 'MAIL_PORT', 'MAIL_USERNAME', 'MAIL_PASSWORD', 
        'MAIL_USE_TLS', 'MAIL_USE_SSL', 'MAIL_DEFAULT_SENDER'
    ]
    app_logger.info("尝试从数据库加载邮件配置...")
    try:
        with flask_app.app_context(): # 确保在应用上下文中操作
            for key in MAIL_SETTING_KEYS:
                decrypt = (key == 'MAIL_PASSWORD') # 只有密码需要解密
                setting_value = Setting.get(key, decrypt=decrypt)
                
                if setting_value is not None:
                    # 类型转换
                    if key == 'MAIL_PORT':
                        try:
                            flask_app.config[key] = int(setting_value)
                        except (ValueError, TypeError):
                            app_logger.warning("邮件配置项 {} 的值 '{}' 无法转换为整数，使用默认值或保持None".format(key, setting_value))
                            flask_app.config[key] = None # 或者设置一个默认端口？
                    elif key in ['MAIL_USE_TLS', 'MAIL_USE_SSL']:
                        flask_app.config[key] = str(setting_value).lower() in ['true', '1', 't', 'yes']
                    else:
                        flask_app.config[key] = str(setting_value)
                    app_logger.info("加载邮件配置: {} = {}".format(key, flask_app.config[key]))
                else:
                    app_logger.info("邮件配置项 {} 在数据库中未找到，保持默认值 None".format(key))
                    flask_app.config[key] = None # 确保未设置的配置为None
            
            # 确保至少有一个加密方式被设置，避免冲突
            if flask_app.config.get('MAIL_USE_TLS') and flask_app.config.get('MAIL_USE_SSL'):
                app_logger.warning("MAIL_USE_TLS 和 MAIL_USE_SSL 不能同时为True，将优先使用SSL")
                flask_app.config['MAIL_USE_TLS'] = False
                
    except Exception as e:
        app_logger.error("从数据库加载邮件配置失败: {}".format(e), exc_info=True)

# --- 在初始化Mail之前加载邮件配置 ---
load_email_config(app) 

# 初始化 Mail (现在应该能获取到正确的配置了)
mail = Mail(app)

# 初始化LoginManager
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# 注册蓝图
register_blueprints(app, csrf)

# 确保请求中包含CSRF令牌的处理器
@app.after_request
def after_request(response):
    return add_csrf_header(response, app)

# 初始化应用 (创建角色、权限等)
with app.app_context():
    init_app(app, db) # 这个函数现在只做它原来的事情

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# 步骤 17/18/19: 注入未读通知数量到模板上下文
@app.context_processor
def inject_notifications():
    if current_user.is_authenticated:
        unread_count = Notification.query.filter_by(user_id=current_user.id, is_read=False).count()
        return dict(unread_notifications=unread_count)
    return dict(unread_notifications=0)

# 路由：主页
@app.route('/')
@login_required
def index():
    from utils.homepage_utils import (
        get_overview_data, get_funnel_stats, get_department_stats,
        get_employee_stats, get_filter_options, get_company_stats
    )

    # 获取筛选参数
    sort_by = request.args.get('sort_by', 'deal_done')  # 默认按签约数排序
    sort_order = request.args.get('sort_order', 'desc')  # 默认降序
    company_filter = request.args.get('company_id', type=int)
    department_filter = request.args.get('department_id', type=int)
    time_range = request.args.get('time_range', 'all')  # all, week, month, quarter

    # 排序方式中文映射
    sort_by_names = {
        'deal_done': '签约数',
        'total_leads': '线索总数',
        'conversion_rate': '转化率',
        'called': '已拨电话',
        'connected': '接通电话',
        'valid_call': '有效通话',
        'wechat_added': '添加微信',
        'intentional': '意向客户',
        'visited': '确认到面',
        'compliant': '信息合规'
    }
    sort_by_name = sort_by_names.get(sort_by, sort_by)

    try:
        # 获取总体概览数据
        overview_data = get_overview_data(company_filter, department_filter, time_range)

        # 获取漏斗统计数据
        funnel_stats = get_funnel_stats(company_filter, department_filter, time_range)

        # 获取部门统计数据
        department_stats = get_department_stats(company_filter, sort_by, sort_order)

        # 获取员工统计数据
        employee_stats = get_employee_stats(company_filter, department_filter, sort_by, sort_order)

        # 获取公司统计数据
        company_stats = get_company_stats(sort_by, sort_order)

        # 获取筛选选项
        filter_options = get_filter_options()

        # 异地到店统计（保留原有逻辑）
        cross_location_pending_count = 0
        my_cross_location_leads_count = 0

        cross_location_pool = LeadPool.query.filter_by(
            company_id=current_user.company_id,
            pool_type='CROSS_LOCATION_PENDING'
        ).first()

        if cross_location_pool:
            cross_location_pending_count = Lead.query.filter_by(
                pool_id=cross_location_pool.id,
                owner_id=None
            ).count()

        my_cross_location_leads_count = Lead.query.filter(
            Lead.owner_id == current_user.id,
            Lead.company_id != current_user.company_id
        ).count()

    except Exception as e:
        app_logger.error("获取首页统计数据失败: {}".format(str(e)), exc_info=True)
        flash('获取统计数据失败，请稍后重试', 'danger')

        # 设置默认值
        overview_data = {
            'total_leads': 0, 'my_leads': 0, 'weekly_new': 0,
            'pending_follow': 0, 'overall_conversion': 0, 'avg_conversion': 0
        }
        funnel_stats = {
            'total': 0, 'called': 0, 'connected': 0, 'valid_call': 0, 'wechat_added': 0,
            'intentional': 0, 'visited': 0, 'compliant': 0, 'deal_done': 0, 'car_selected': 0
        }
        department_stats = []
        employee_stats = []
        company_stats = []
        filter_options = {'companies': [], 'departments': []}
        cross_location_pending_count = 0
        my_cross_location_leads_count = 0
    
    # 获取系统主题设置
    from models import Setting
    system_theme = Setting.get('system_theme', 'orange')

    return render_template('index.html',
                           overview_data=overview_data,
                           funnel_stats=funnel_stats,
                           department_stats=department_stats,
                           employee_stats=employee_stats,
                           company_stats=company_stats,
                           filter_options=filter_options,
                           cross_location_pending_count=cross_location_pending_count,
                           my_cross_location_leads_count=my_cross_location_leads_count,
                           # 筛选参数
                           sort_by=sort_by,
                           sort_by_name=sort_by_name,
                           sort_order=sort_order,
                           company_filter=company_filter,
                           department_filter=department_filter,
                           time_range=time_range,
                           # 系统主题
                           system_theme=system_theme)

# 路由：综合仪表盘
@app.route('/dashboard')
@login_required
def dashboard():
    try:
        dashboard_data = get_dashboard_data()
        return render_template('dashboard.html', **dashboard_data)
    except Exception as e:
        current_app.logger.error("仪表盘渲染失败: {}".format(str(e)))
        flash('加载仪表盘数据失败，请稍后重试', 'danger')
        return render_template('dashboard.html', error=True)

# 路由：登录页面
@app.route('/login', methods=['GET', 'POST'])
def login():
    """简洁的登录处理"""
    from models import User, Setting
    from flask_login import login_user, current_user, logout_user
    from flask import request, render_template, redirect, url_for

    error_message = None
    success_message = None

    # 获取系统主题设置
    system_theme = Setting.get('system_theme', 'orange')

    if request.method == 'GET':
        # GET请求：如果已经登录，重定向到首页
        if current_user.is_authenticated:
            return redirect(url_for('index'))

    elif request.method == 'POST':
        # POST请求：总是处理登录逻辑，先登出当前用户
        if current_user.is_authenticated:
            logout_user()

        try:
            username = request.form.get('username', '').strip()
            password = request.form.get('password', '').strip()

            # 验证输入
            if not username or not password:
                error_message = '请输入用户名和密码'
            else:
                # 查找用户
                user = User.query.filter_by(username=username).first()

                if user and user.check_password(password):
                    # 检查用户状态
                    if user.status != 'active':
                        error_message = '账户已被禁用，请联系管理员'
                    else:
                        # 登录成功
                        login_user(user)
                        return redirect(url_for('index'))
                else:
                    error_message = '用户名或密码错误'

        except Exception as e:
            current_app.logger.error("登录处理失败: {}".format(str(e)))
            error_message = '登录失败，请稍后重试'

    return render_template('login.html',
                         error_message=error_message,
                         success_message=success_message,
                         system_theme=system_theme)



# 路由：修改密码
@app.route('/change_password', methods=['GET', 'POST'])
def change_password():
    return handle_change_password()

# 路由：登出
@app.route('/logout')
@login_required
def logout():
    return handle_logout()

# 创建管理员账户的命令
@app.cli.command('create-admin')
def create_admin_command():
    username = input('请输入管理员用户名：')
    email = input('请输入管理员邮箱：')
    password = input('请输入管理员密码：')
    
    if create_admin(username, email, password):
        print('管理员账户创建成功！')
    else:
        print('管理员账户创建失败！')

# 权限初始化命令
@app.cli.command('init-permissions')
def init_permissions_command():
    """初始化或重置角色和权限"""
    setup_roles_and_permissions(app)

@app.route('/users')
@login_required
def users():
    """显示用户列表页面"""
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('USERS_PER_PAGE', 15)
    return get_user_list(page=page, per_page=per_page)

@app.route('/users/add', methods=['GET', 'POST'])
@login_required
def add_user():
    return handle_add_user()

# 路由：编辑用户
@app.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_user(user_id):
    return handle_edit_user(user_id)

# 路由：删除用户
@app.route('/users/<int:user_id>/delete', methods=['POST'])
@login_required
def delete_user(user_id):
    return handle_delete_user(user_id)

# 路由：个人资料
@app.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """个人资料页面 - 使用可工作的最小版本逻辑"""
    app.logger.info(f"Profile route accessed, method: {request.method}")
    app.logger.info(f"User: {current_user.username}")

    if request.method == 'POST':
        app.logger.info("=== POST REQUEST RECEIVED ===")
        app.logger.info(f"Form data: {dict(request.form)}")

        try:
            username = request.form.get('username', '').strip()
            email = request.form.get('email', '').strip()
            phone = request.form.get('phone', '').strip()
            password = request.form.get('password', '').strip()

            app.logger.info(f"Extracted - username: {username}, email: {email}, phone: {phone}")

            if not username or not email:
                flash('用户名和邮箱不能为空', 'danger')
                return render_template('profile.html')

            # 更新用户信息
            current_user.username = username
            current_user.email = email
            current_user.phone = phone if phone else None

            if password:
                current_user.set_password(password)
                app.logger.info("Password updated")

            db.session.commit()
            app.logger.info("=== UPDATE SUCCESSFUL ===")

            flash('个人资料更新成功！', 'success')
            return redirect(url_for('profile'))

        except Exception as e:
            db.session.rollback()
            app.logger.error(f"Update failed: {str(e)}", exc_info=True)
            flash(f'更新失败: {str(e)}', 'danger')

    app.logger.info("Rendering profile template")
    return render_template('profile.html')

@app.route('/profile-simple', methods=['GET', 'POST'])
@login_required
def profile_simple():
    """简化版个人资料页面"""
    app.logger.info(f"Simple profile route accessed, method: {request.method}")
    app.logger.info(f"Request form data: {dict(request.form) if request.method == 'POST' else 'GET request'}")

    if request.method == 'POST':
        try:
            # 获取表单数据
            username = request.form.get('username', '').strip()
            email = request.form.get('email', '').strip()
            phone = request.form.get('phone', '').strip()
            password = request.form.get('password', '').strip()

            app.logger.info(f"Processing update - username: {username}, email: {email}, phone: {phone}")

            # 基本验证
            if not username or not email:
                flash('用户名和邮箱不能为空', 'danger')
                return render_template('profile_simple.html')

            # 更新用户信息
            current_user.username = username
            current_user.email = email
            current_user.phone = phone if phone else None

            if password:
                current_user.set_password(password)
                app.logger.info("Password updated")

            db.session.commit()
            app.logger.info("Profile updated successfully")

            flash('个人资料更新成功！', 'success')
            return redirect(url_for('profile_simple'))

        except Exception as e:
            db.session.rollback()
            app.logger.error(f"Profile update failed: {str(e)}", exc_info=True)
            flash(f'更新失败: {str(e)}', 'danger')

    return render_template('profile_simple.html')

@app.route('/profile-test', methods=['GET', 'POST'])
@login_required
def profile_test():
    """测试版个人资料页面"""
    app.logger.info(f"Test profile route accessed, method: {request.method}")
    app.logger.info(f"User authenticated: {current_user.is_authenticated}")
    app.logger.info(f"Current user ID: {current_user.id}")

    if request.method == 'POST':
        app.logger.info(f"Test profile POST data: {dict(request.form)}")
        app.logger.info(f"Form keys: {list(request.form.keys())}")

        try:
            # 简单的更新逻辑
            username = request.form.get('username', '').strip()
            email = request.form.get('email', '').strip()
            phone = request.form.get('phone', '').strip()
            password = request.form.get('password', '').strip()

            app.logger.info(f"Extracted data - username: {username}, email: {email}, phone: {phone}")

            if username and email:
                current_user.username = username
                current_user.email = email
                current_user.phone = phone if phone else None

                if password:
                    current_user.set_password(password)
                    app.logger.info("Password updated")

                db.session.commit()
                app.logger.info("Database commit successful")

                flash('个人资料更新成功！', 'success')
                return redirect(url_for('profile_test'))
            else:
                flash('用户名和邮箱不能为空', 'danger')

        except Exception as e:
            db.session.rollback()
            app.logger.error(f"Update failed: {str(e)}", exc_info=True)
            flash(f'更新失败: {str(e)}', 'danger')

    return render_template('profile_test.html')

@app.route('/profile-minimal', methods=['GET', 'POST'])
@login_required
def profile_minimal():
    """最小版本个人资料页面"""
    app.logger.info(f"Minimal profile route accessed, method: {request.method}")
    app.logger.info(f"User: {current_user.username}")

    if request.method == 'POST':
        app.logger.info("=== POST REQUEST RECEIVED ===")
        app.logger.info(f"Form data: {dict(request.form)}")
        app.logger.info(f"Headers: {dict(request.headers)}")

        try:
            username = request.form.get('username', '').strip()
            email = request.form.get('email', '').strip()
            phone = request.form.get('phone', '').strip()
            password = request.form.get('password', '').strip()

            app.logger.info(f"Extracted - username: {username}, email: {email}, phone: {phone}")

            if not username or not email:
                flash('用户名和邮箱不能为空', 'danger')
                return render_template('profile_minimal.html')

            # 更新用户信息
            current_user.username = username
            current_user.email = email
            current_user.phone = phone if phone else None

            if password:
                current_user.set_password(password)
                app.logger.info("Password updated")

            db.session.commit()
            app.logger.info("=== UPDATE SUCCESSFUL ===")

            flash('个人资料更新成功！', 'success')
            return redirect(url_for('profile_minimal'))

        except Exception as e:
            db.session.rollback()
            app.logger.error(f"Update failed: {str(e)}", exc_info=True)
            flash(f'更新失败: {str(e)}', 'danger')

    app.logger.info("Rendering minimal profile template")
    return render_template('profile_minimal.html')

# 路由：获取验证码
@app.route('/captcha')
def get_captcha():
    try:
        # 无论验证码功能是否启用，都正常生成验证码
        # 登录逻辑中会根据配置决定是否验证
        from utils.captcha_utils import generate_captcha
        img_data, code = generate_captcha()
        session['captcha_code'] = code
        return send_file(
            io.BytesIO(img_data),
            mimetype='image/png',
            cache_timeout=0  # 禁用缓存
        )
    except Exception as e:
        current_app.logger.error('生成验证码错误: {}'.format(str(e)), exc_info=True)
        return '', 500

# 错误处理
@app.errorhandler(400)
def bad_request(e):
    return handle_bad_request(e, app)

@app.errorhandler(403)
def forbidden(e):
    return handle_forbidden(e, app)

@app.errorhandler(404)
def not_found(e):
    return handle_not_found(e, app)

@app.errorhandler(500)
def server_error(e):
    return handle_server_error(e, app)

@app.errorhandler(Exception)
def error(error):
    return handle_error(error, app)

@app.route('/analytics')
@login_required
def analytics():
    return redirect(url_for('dashboard'))

@app.route('/debug-api')
@login_required
def debug_api():
    return render_template('debug_api.html')

@app.route('/ui-test')
def ui_test():
    return render_template('ui-test.html')

@app.route('/button-showcase')
@login_required
def button_showcase():
    return render_template('button_showcase.html')

@app.route('/data/china_regions.json')
def china_regions_data():
    """提供省份城市数据"""
    try:
        import os
        json_path = os.path.join(app.static_folder, 'data', 'china_regions.json')
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return jsonify(data)
    except Exception as e:
        app_logger.error(f'加载省份城市数据失败: {str(e)}')
        return jsonify({'error': '加载数据失败'}), 500

@app.route('/test-simple-permissions')
@login_required
def test_simple_permissions():
    """简单的权限数据显示"""
    from models import Role, Permission
    from flask import render_template_string

    # 使用原始SQL查询确保数据正确
    roles_result = db.session.execute(db.text("SELECT * FROM roles ORDER BY id")).fetchall()
    perms_result = db.session.execute(db.text("SELECT * FROM permissions ORDER BY id")).fetchall()

    template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>简单权限测试</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-4">
            <h1>简单权限数据测试</h1>

            <div class="alert alert-info">
                <h5>原始SQL查询结果：</h5>
                <p><strong>角色数量：</strong>{{ roles_result|length }}</p>
                <p><strong>权限数量：</strong>{{ perms_result|length }}</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>角色列表</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <thead>
                                    <tr><th>ID</th><th>名称</th><th>代码</th></tr>
                                </thead>
                                <tbody>
                                    {% for role in roles_result %}
                                    <tr>
                                        <td>{{ role[0] }}</td>
                                        <td>{{ role[1] }}</td>
                                        <td>{{ role[2] }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>权限列表</h5>
                        </div>
                        <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                            <table class="table table-sm">
                                <thead>
                                    <tr><th>ID</th><th>名称</th><th>代码</th></tr>
                                </thead>
                                <tbody>
                                    {% for perm in perms_result %}
                                    <tr>
                                        <td>{{ perm[0] }}</td>
                                        <td>{{ perm[1] }}</td>
                                        <td>{{ perm[2] }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4">
                <a href="/rbac/roles/permissions" class="btn btn-primary">访问权限管理页面</a>
            </div>
        </div>
    </body>
    </html>
    """

    return render_template_string(template, roles_result=roles_result, perms_result=perms_result)

@app.route('/test-permissions-debug')
@login_required
def test_permissions_debug():
    """测试权限数据显示"""
    from models import Role, Permission
    from flask import render_template_string

    # 直接查询，不需要显式事务
    roles = Role.query.all()
    permissions = Permission.query.all()

    template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>权限数据调试</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-4">
            <h1>权限数据调试</h1>

            <div class="alert alert-info">
                <h5>数据统计：</h5>
                <p><strong>角色数量：</strong>{{ roles|length }}</p>
                <p><strong>权限数量：</strong>{{ permissions|length }}</p>
                <p><strong>当前用户：</strong>{{ current_user.username }}</p>
                <p><strong>用户角色：</strong>{{ current_user.role.name if current_user.role else '无角色' }}</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>角色列表</h5>
                        </div>
                        <div class="card-body">
                            {% for role in roles %}
                            <div class="mb-2">
                                <strong>{{ role.name }}</strong> ({{ role.code }})
                                <br><small>权限数: {{ role.permissions|length }}</small>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>权限列表</h5>
                        </div>
                        <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                            {% for permission in permissions %}
                            <div class="mb-1">
                                <strong>{{ permission.name }}</strong> ({{ permission.code }})
                                <br><small class="text-muted">{{ permission.description }}</small>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4">
                <h5>权限分配矩阵</h5>
                <div class="table-responsive">
                    <table class="table table-bordered table-sm">
                        <thead>
                            <tr>
                                <th>权限名称</th>
                                {% for role in roles %}
                                <th class="text-center">{{ role.name }}</th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for permission in permissions %}
                            <tr>
                                <td>{{ permission.name }}</td>
                                {% for role in roles %}
                                <td class="text-center">
                                    {% if permission in role.permissions %}
                                    <span class="badge bg-success">✓</span>
                                    {% else %}
                                    <span class="badge bg-secondary">-</span>
                                    {% endif %}
                                </td>
                                {% endfor %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="mt-4">
                <a href="/rbac/roles/permissions" class="btn btn-primary">访问权限管理页面</a>
                <a href="/rbac/roles" class="btn btn-secondary">角色管理</a>
            </div>
        </div>
    </body>
    </html>
    """

    return render_template_string(template, roles=roles, permissions=permissions)

@app.route('/test-rbac-debug')
def test_rbac_debug():
    """调试RBAC路由问题 - 无需登录"""
    from flask import render_template_string

    # 检查所有RBAC相关路由
    rbac_routes = []
    for rule in app.url_map.iter_rules():
        if 'rbac' in rule.rule:
            rbac_routes.append({
                'rule': rule.rule,
                'endpoint': rule.endpoint,
                'methods': list(rule.methods)
            })

    template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>RBAC路由调试</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-4">
            <h1>RBAC路由调试</h1>

            <div class="alert alert-info">
                <h5>发现的RBAC路由 ({{ rbac_routes|length }}个)：</h5>
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>路由</th>
                            <th>端点</th>
                            <th>方法</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for route in rbac_routes %}
                        <tr>
                            <td><code>{{ route.rule }}</code></td>
                            <td><code>{{ route.endpoint }}</code></td>
                            <td>{{ route.methods|join(', ') }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <div class="mt-4">
                <div class="card">
                    <div class="card-header">
                        <h5>直接测试RBAC路由</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-warning">注意：这些链接可能需要登录才能访问</p>
                        <div class="d-grid gap-2">
                            <a href="/rbac/roles" class="btn btn-primary" target="_blank">
                                测试: /rbac/roles
                            </a>
                            <a href="/rbac/roles/permissions" class="btn btn-info" target="_blank">
                                测试: /rbac/roles/permissions
                            </a>
                            <a href="/rbac/roles/add" class="btn btn-success" target="_blank">
                                测试: /rbac/roles/add
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4">
                <div class="alert alert-warning">
                    <h6>调试说明：</h6>
                    <ul>
                        <li>如果上面显示了RBAC路由，说明蓝图注册成功</li>
                        <li>如果点击链接显示"页面不存在"，可能是权限问题</li>
                        <li>如果显示"权限不足"，说明需要超级管理员登录</li>
                        <li>如果显示登录页面，说明需要先登录</li>
                    </ul>
                </div>
            </div>
        </div>
    </body>
    </html>
    """

    return render_template_string(template, rbac_routes=rbac_routes)

@app.route('/test-rbac-status')
@login_required
def test_rbac_status():
    """测试RBAC系统状态"""
    from models import Role, Permission
    from flask import render_template_string

    roles = Role.query.all()
    permissions = Permission.query.all()

    template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>RBAC系统状态测试</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-4">
            <h1>RBAC系统状态测试</h1>

            <div class="alert alert-info">
                <h5>当前用户信息：</h5>
                <p><strong>用户名：</strong>{{ current_user.username }}</p>
                <p><strong>角色：</strong>{{ current_user.role.name if current_user.role else '无角色' }}</p>
                <p><strong>角色代码：</strong>{{ current_user.role.code if current_user.role else '无代码' }}</p>
                <p><strong>是否超级管理员：</strong>{{ current_user.role.code == 'super_admin' if current_user.role else False }}</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>系统角色 ({{ roles|length }}个)</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <thead>
                                    <tr><th>ID</th><th>名称</th><th>代码</th><th>权限数</th></tr>
                                </thead>
                                <tbody>
                                    {% for role in roles %}
                                    <tr>
                                        <td>{{ role.id }}</td>
                                        <td>{{ role.name }}</td>
                                        <td>{{ role.code }}</td>
                                        <td>{{ role.permissions|length }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>系统权限 ({{ permissions|length }}个)</h5>
                        </div>
                        <div class="card-body">
                            <div style="max-height: 300px; overflow-y: auto;">
                                <table class="table table-sm">
                                    <thead>
                                        <tr><th>ID</th><th>名称</th><th>代码</th></tr>
                                    </thead>
                                    <tbody>
                                        {% for permission in permissions %}
                                        <tr>
                                            <td>{{ permission.id }}</td>
                                            <td>{{ permission.name }}</td>
                                            <td>{{ permission.code }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4">
                <div class="card">
                    <div class="card-header">
                        <h5>RBAC路由测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>直接URL访问：</h6>
                                <div class="d-grid gap-2">
                                    <a href="/rbac/roles" class="btn btn-primary" target="_blank">
                                        /rbac/roles (角色管理)
                                    </a>
                                    <a href="/rbac/roles/permissions" class="btn btn-info" target="_blank">
                                        /rbac/roles/permissions (权限管理)
                                    </a>
                                    <a href="/rbac/roles/add" class="btn btn-success" target="_blank">
                                        /rbac/roles/add (添加角色)
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Flask url_for()：</h6>
                                <div class="d-grid gap-2">
                                    <a href="{{ url_for('rbac.roles') }}" class="btn btn-outline-primary" target="_blank">
                                        url_for('rbac.roles')
                                    </a>
                                    <a href="{{ url_for('rbac.role_permissions') }}" class="btn btn-outline-info" target="_blank">
                                        url_for('rbac.role_permissions')
                                    </a>
                                    <a href="{{ url_for('rbac.add_role') }}" class="btn btn-outline-success" target="_blank">
                                        url_for('rbac.add_role')
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4">
                <div class="alert alert-warning">
                    <h6>测试说明：</h6>
                    <ul>
                        <li>如果上面的链接显示"页面不存在"，说明RBAC蓝图注册有问题</li>
                        <li>如果显示"权限不足"，说明当前用户不是超级管理员</li>
                        <li>如果正常显示页面但没有数据，说明数据库数据有问题</li>
                    </ul>
                </div>
            </div>
        </div>
    </body>
    </html>
    """

    return render_template_string(template, roles=roles, permissions=permissions)

@app.route('/test-simple-roles')
@login_required
def test_simple_roles():
    """简化版角色管理测试"""
    from models import Role, Permission
    from flask import render_template_string

    roles = Role.query.all()
    permissions = Permission.query.all()

    # 打印调试信息到控制台
    print(f"=== 角色管理调试信息 ===")
    print(f"当前用户: {current_user.username}")
    print(f"用户角色: {current_user.role.name if current_user.role else '无角色'}")
    print(f"角色代码: {current_user.role.code if current_user.role else '无代码'}")
    print(f"是否超级管理员: {current_user.role.code == 'super_admin' if current_user.role else False}")
    print(f"查询到的角色数量: {len(roles)}")
    print(f"查询到的权限数量: {len(permissions)}")

    for role in roles:
        print(f"角色: {role.name} ({role.code}) - 权限数: {len(role.permissions)} - 用户数: {len(role.users)}")

    simple_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>简化版角色管理</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body { padding: 20px; }
            .debug-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>简化版角色管理测试</h1>

            <div class="debug-info">
                <h5>调试信息：</h5>
                <p><strong>当前用户：</strong>{{ current_user.username }}</p>
                <p><strong>用户角色：</strong>{{ current_user.role.name if current_user.role else '无角色' }}</p>
                <p><strong>角色代码：</strong>{{ current_user.role.code if current_user.role else '无代码' }}</p>
                <p><strong>是否超级管理员：</strong>{{ current_user.role.code == 'super_admin' if current_user.role else False }}</p>
                <p><strong>角色数量：</strong>{{ roles|length }}</p>
                <p><strong>权限数量：</strong>{{ permissions|length }}</p>
            </div>

            {% if roles %}
            <div class="card">
                <div class="card-header">
                    <h5>角色列表 ({{ roles|length }}个)</h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>角色名称</th>
                                <th>角色代码</th>
                                <th>描述</th>
                                <th>权限数量</th>
                                <th>用户数量</th>
                                <th>系统角色</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for role in roles %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>
                                    <strong>{{ role.name }}</strong>
                                    {% if role.is_system %}
                                    <span class="badge bg-info ms-2">系统</span>
                                    {% endif %}
                                </td>
                                <td><code>{{ role.code }}</code></td>
                                <td>{{ role.description or '无描述' }}</td>
                                <td><span class="badge bg-primary">{{ role.permissions|length }}</span></td>
                                <td><span class="badge bg-info">{{ role.users|length }}</span></td>
                                <td>
                                    {% if role.is_system %}
                                    <span class="badge bg-warning">是</span>
                                    {% else %}
                                    <span class="badge bg-success">否</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="/rbac/roles/{{ role.id }}/edit" class="btn btn-sm btn-outline-primary">编辑</a>
                                    {% if not role.is_system %}
                                    <button class="btn btn-sm btn-outline-danger" onclick="alert('删除功能')">删除</button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            {% else %}
            <div class="alert alert-warning">
                <h5>没有找到任何角色！</h5>
                <p>这可能表示：</p>
                <ul>
                    <li>数据库中没有角色数据</li>
                    <li>查询出现了问题</li>
                    <li>权限初始化没有正确执行</li>
                </ul>
            </div>
            {% endif %}

            <div class="mt-4">
                <h5>测试链接：</h5>
                <a href="/rbac/roles" class="btn btn-primary me-2">原始角色管理页面</a>
                <a href="/rbac/roles/permissions" class="btn btn-info me-2">权限管理页面</a>
                <a href="/rbac/roles/add" class="btn btn-success me-2">添加角色页面</a>
            </div>
        </div>
    </body>
    </html>
    """

    return render_template_string(simple_template, roles=roles, permissions=permissions)

@app.route('/test-profile')
def test_profile():
    """测试个人资料修改页面"""
    return send_from_directory('.', 'test_browser_profile.html')

# API：测试表单提交
@app.route('/api/test-form', methods=['POST'])
def test_form():
    """测试表单提交功能"""
    app.logger.info("Test form API called")
    app.logger.info(f"Request method: {request.method}")
    app.logger.info(f"Request headers: {dict(request.headers)}")
    app.logger.info(f"Request form data: {dict(request.form)}")
    app.logger.info(f"Request files: {dict(request.files)}")

    try:
        username = request.form.get('username', '')
        email = request.form.get('email', '')
        phone = request.form.get('phone', '')
        password = request.form.get('password', '')

        response_data = {
            'status': 'success',
            'message': '表单提交成功',
            'received_data': {
                'username': username,
                'email': email,
                'phone': phone,
                'password': '***' if password else ''
            }
        }

        app.logger.info(f"Sending response: {response_data}")
        return jsonify(response_data)

    except Exception as e:
        app.logger.error(f"Test form error: {str(e)}", exc_info=True)
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

# API：设置系统主题
@app.route('/api/set-system-theme', methods=['POST'])
@login_required
def set_system_theme():
    """设置系统主题（仅超级管理员可用）"""
    from models import Setting

    # 检查权限
    if not current_user.role or current_user.role.code != 'super_admin':
        return jsonify({'error': '权限不足'}), 403

    try:
        data = request.get_json()
        theme = data.get('theme')

        # 验证主题值
        valid_themes = ['orange', 'blue', 'green', 'purple', 'dark']
        if theme not in valid_themes:
            return jsonify({'error': '无效的主题'}), 400

        # 保存到数据库
        Setting.set('system_theme', theme)
        db.session.commit()

        return jsonify({'success': True, 'theme': theme})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"设置系统主题失败: {e}")
        return jsonify({'error': '设置失败'}), 500

if __name__ == '__main__':
    app.run(debug=True, port=5000)