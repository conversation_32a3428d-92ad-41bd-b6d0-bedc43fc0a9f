{% extends 'base.html' %}

{% block title %}部门用户管理 - CRM系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{{ company.name }} - {{ department.name }} - 用户管理</h1>
    <div>
        <a href="{{ url_for('add_user', company_id=company.id, department_id=department.id) }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> 添加用户
        </a>
    </div>
</div>

<!-- 用户列表 -->
<div class="card shadow">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>用户名</th>
                        <th>角色</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.username }}</td>
                        <td>
                            {% if user.role.code == 'super_admin' %}
                            <span class="badge bg-danger">{{ user.role.name }}</span>
                            {% elif user.role.code == 'company_admin' %}
                            <span class="badge bg-danger">{{ user.role.name }}</span>
                            {% elif user.role.code == 'department_admin' %}
                            <span class="badge bg-primary">{{ user.role.name }}</span>
                            {% else %}
                            <span class="badge bg-success">{{ user.role.name }}</span>
                            {% endif %}
                        </td>
                        <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            <a href="{{ url_for('edit_user', user_id=user.id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-pencil"></i> 编辑
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="mt-3">
    <a href="{{ url_for('organization.departments', company_id=company.id) }}" class="btn btn-secondary">
        <i class="bi bi-arrow-left"></i> 返回部门列表
    </a>
</div>
{% endblock %}