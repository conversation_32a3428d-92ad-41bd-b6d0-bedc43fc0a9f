{% extends 'base.html' %}

{% block title %}公司管理 - CRM系统{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-1">
                                <i class="fas fa-building"></i> 公司管理
                            </h1>
                            <p class="text-muted mb-0">管理系统中的所有公司信息</p>
                        </div>
                        <div class="text-end">
                            {% if scope.can_manage_companies %}
                            <a href="{{ url_for('organization_unified.add_company') }}" class="btn btn-success">
                                <i class="fas fa-plus"></i> 添加公司
                            </a>
                            {% endif %}
                            <a href="{{ url_for('organization_unified.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> 返回总览
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计信息 - 与线索管理样式完全一致 -->
    <div class="row mb-4">
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-building text-primary" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-primary mt-2 mb-1">总公司数</h6>
                    <h4 class="mb-1">{{ companies|length }}</h4>
                    <small class="text-muted">全部公司</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-diagram-3 text-info" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-info mt-2 mb-1">总部公司</h6>
                    <h4 class="mb-1">{{ companies|selectattr('type', 'equalto', 'headquarters')|list|length }}</h4>
                    <small class="text-muted">总部级别</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-git text-success" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-success mt-2 mb-1">子公司</h6>
                    <h4 class="mb-1">{{ companies|selectattr('type', 'equalto', 'subsidiary')|list|length }}</h4>
                    <small class="text-muted">分支机构</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-people text-warning" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-warning mt-2 mb-1">总员工数</h6>
                    <h4 class="mb-1">{{ companies|sum(attribute='user_count') or 0 }}</h4>
                    <small class="text-muted">全部员工</small>
                </div>
            </div>
        </div>
    </div>

    <!-- 公司列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> 公司列表
                        </h5>
                        <div class="d-flex gap-2">
                            <input type="text" class="form-control form-control-sm" id="searchInput" 
                                   placeholder="搜索公司名称或代码..." style="width: 200px;">
                            <select class="form-select form-select-sm" id="typeFilter" style="width: 120px;">
                                <option value="">所有类型</option>
                                <option value="headquarters">总部</option>
                                <option value="branch">分公司</option>
                                <option value="subsidiary">子公司</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="companiesTable">
                            <thead class="table-light">
                                <tr>
                                    <th>公司名称</th>
                                    <th>公司代码</th>
                                    <th>类型</th>
                                    <th>上级公司</th>
                                    <th>部门数量</th>
                                    <th>员工数量</th>
                                    <th>创建时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for company in companies %}
                                <tr data-company-type="{{ company.type }}" data-company-name="{{ company.name|lower }}" data-company-code="{{ company.code|lower }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-building text-primary me-2"></i>
                                            <div>
                                                <strong>{{ company.name }}</strong>
                                                {% if company.is_default %}
                                                <span class="badge bg-success ms-1">默认</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <code>{{ company.code }}</code>
                                    </td>
                                    <td>
                                        {% if company.type == 'headquarters' %}
                                        <span class="badge bg-primary">总部</span>
                                        {% elif company.type == 'branch' %}
                                        <span class="badge bg-info">分公司</span>
                                        {% elif company.type == 'subsidiary' %}
                                        <span class="badge bg-warning">子公司</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ company.type }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if company.parent %}
                                        <i class="fas fa-arrow-up text-muted me-1"></i>
                                        {{ company.parent.name }}
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">{{ company.department_count if company.department_count is not none else 0 }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">{{ company.user_count if company.user_count is not none else 0 }}</span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ company.created_at.strftime('%Y-%m-%d %H:%M') if company.created_at else '-' }}
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">正常</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            {% if scope.can_manage_companies %}
                                            <a href="{{ url_for('organization_unified.edit_company', company_id=company.id) }}" 
                                               class="btn btn-outline-primary" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}
                                            <a href="{{ url_for('organization_unified.departments', company_id=company.id) }}" 
                                               class="btn btn-outline-info" title="部门管理">
                                                <i class="fas fa-sitemap"></i>
                                            </a>
                                            <a href="{{ url_for('organization_unified.employees', company_id=company.id) }}" 
                                               class="btn btn-outline-success" title="员工管理">
                                                <i class="fas fa-users"></i>
                                            </a>
                                            {% if scope.can_manage_companies and (company.department_count or 0) == 0 and (company.user_count or 0) == 0 %}
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="deleteCompany({{ company.id }}, '{{ company.name }}')" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteCompanyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning"></i> 确认删除
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>您确定要删除公司 <strong id="deleteCompanyName"></strong> 吗？</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>警告：</strong>删除操作不可恢复，请谨慎操作！
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let deleteCompanyId = null;

// 搜索和筛选功能
document.getElementById('searchInput').addEventListener('input', filterTable);
document.getElementById('typeFilter').addEventListener('change', filterTable);

function filterTable() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const typeFilter = document.getElementById('typeFilter').value;
    const rows = document.querySelectorAll('#companiesTable tbody tr');
    
    rows.forEach(row => {
        const name = row.dataset.companyName;
        const code = row.dataset.companyCode;
        const type = row.dataset.companyType;
        
        const matchesSearch = name.includes(searchTerm) || code.includes(searchTerm);
        const matchesType = !typeFilter || type === typeFilter;
        
        row.style.display = matchesSearch && matchesType ? '' : 'none';
    });
}

// 删除公司
function deleteCompany(companyId, companyName) {
    deleteCompanyId = companyId;
    document.getElementById('deleteCompanyName').textContent = companyName;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteCompanyModal'));
    modal.show();
}

// 确认删除
document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
    if (!deleteCompanyId) return;
    
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/organization/companies/${deleteCompanyId}/delete`;
    
    // 添加CSRF token
    const csrfToken = document.querySelector('meta[name=csrf-token]');
    if (csrfToken) {
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = csrfToken.getAttribute('content');
        form.appendChild(csrfInput);
    }
    
    document.body.appendChild(form);
    form.submit();
});
</script>
{% endblock %}
