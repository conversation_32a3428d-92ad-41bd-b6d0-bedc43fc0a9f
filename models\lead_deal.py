from datetime import datetime
from . import db

class LeadDealHistory(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    lead_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON><PERSON>('lead.id'), nullable=False)
    old_status = db.Column(db.String(20))  # 原签约状态
    new_status = db.Column(db.String(20), nullable=False)  # 新签约状态
    feedback_content = db.Column(db.Text)  # 签约反馈内容
    deal_amount = db.Column(db.Numeric(10, 2))  # 签约金额
    deal_time = db.Column(db.DateTime)  # 实际签约时间
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.<PERSON>ey('user.id'), nullable=False)
    
    # 关系
    lead = db.relationship('Lead', backref='deal_history')
    creator = db.relationship('User', foreign_keys=[created_by])