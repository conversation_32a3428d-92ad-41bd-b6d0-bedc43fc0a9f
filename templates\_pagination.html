{# templates/_pagination.html #}
{% macro render_pagination(pagination, endpoint, fragment='', prev='«', next='»', size=None, Ellipsis='...') %}
    {% if pagination.pages > 1 %}
    <nav aria-label="Page navigation">
        <ul class="pagination {% if size %}pagination-{{ size }}{% endif %} justify-content-center">
            {# Previous Page Link #}
            <li class="page-item {% if not pagination.has_prev %}disabled{% endif %}">
                {% set query_params = request.args.copy() %}
                {% do query_params.pop('page', None) %}
                <a class="page-link" href="{% if pagination.has_prev %}{{ url_for(endpoint, page=pagination.prev_num, **query_params) }}{{ fragment }}{% else %}#}{% endif %}" aria-label="Previous">
                    <span aria-hidden="true">{{ prev }}</span>
                </a>
            </li>

            {# Page Numbers #}
            {% for page in pagination.iter_pages() %}
                {% if page %}
                    {% if page != pagination.page %}
                        <li class="page-item">
                            {% set query_params = request.args.copy() %}
                            {% do query_params.pop('page', None) %}
                            <a class="page-link" href="{{ url_for(endpoint, page=page, **query_params) }}{{ fragment }}">{{ page }}</a>
                        </li>
                    {% else %}
                        <li class="page-item active" aria-current="page">
                            <span class="page-link">{{ page }} <span class="visually-hidden">(current)</span></span>
                        </li>
                    {% endif %}
                {% else %}
                    <li class="page-item disabled"><span class="page-link">{{ Ellipsis }}</span></li>
                {% endif %}
            {% endfor %}

            {# Next Page Link #}
            <li class="page-item {% if not pagination.has_next %}disabled{% endif %}">
                {% set query_params = request.args.copy() %}
                {% do query_params.pop('page', None) %}
                <a class="page-link" href="{% if pagination.has_next %}{{ url_for(endpoint, page=pagination.next_num, **query_params) }}{{ fragment }}{% else %}#}{% endif %}" aria-label="Next">
                    <span aria-hidden="true">{{ next }}</span>
                </a>
            </li>
        </ul>
    </nav>
    {% endif %}
{% endmacro %}

{# Render the pagination macro using the pagination object passed from the view #}
{{ render_pagination(pagination, request.endpoint) }} 