from PIL import Image, ImageDraw, ImageFont
import random
import string
import io
import base64
import redis
from flask import session
import uuid

class CaptchaGenerator:
    def __init__(self, redis_client=None):
        self.width = 160
        self.height = 60
        self.font_size = 40
        self.code_length = 4
        self.redis_client = redis_client or redis.Redis(host='localhost', port=6379, db=0)
        
    def generate_code(self):
        """生成随机验证码"""
        chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(self.code_length))
    
    def generate_image(self, code):
        """生成验证码图片"""
        # 创建图片
        image = Image.new('RGB', (self.width, self.height), color='white')
        draw = ImageDraw.Draw(image)
        
        # 添加干扰线
        for _ in range(5):
            x1 = random.randint(0, self.width)
            y1 = random.randint(0, self.height)
            x2 = random.randint(0, self.width)
            y2 = random.randint(0, self.height)
            draw.line([(x1, y1), (x2, y2)], fill='gray')
        
        # 添加干扰点
        for _ in range(30):
            x = random.randint(0, self.width)
            y = random.randint(0, self.height)
            draw.point([x, y], fill='gray')
        
        # 添加验证码文字
        try:
            font = ImageFont.truetype('arial.ttf', self.font_size)
        except:
            font = ImageFont.load_default()
            
        for i, char in enumerate(code):
            x = 20 + i * 30
            y = random.randint(5, 15)
            draw.text((x, y), char, font=font, fill='black')
        
        # 转换为base64
        buffered = io.BytesIO()
        image.save(buffered, format="PNG")
        return base64.b64encode(buffered.getvalue()).decode()
    
    def save_code(self, session_id, code):
        """保存验证码到Redis"""
        key = f'captcha:{session_id}'
        self.redis_client.setex(key, 300, code.lower())  # 5分钟过期
    
    def verify_code(self, session_id, code):
        """验证验证码"""
        key = f'captcha:{session_id}'
        stored_code = self.redis_client.get(key)
        if stored_code:
            self.redis_client.delete(key)  # 验证后立即删除
            return stored_code.decode() == code.lower()
        return False
    
    def get_session_id(self):
        """获取或创建会话ID"""
        session_id = session.get('_id')
        if not session_id:
            session_id = str(uuid.uuid4())
            session['_id'] = session_id
        return session_id 