#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试登录功能
"""

import os
import sys
import requests
from bs4 import BeautifulSoup

def test_login():
    """测试登录功能"""
    try:
        base_url = "http://127.0.0.1:5000"
        login_url = f"{base_url}/login"
        
        print("🧪 测试登录功能")
        print("="*50)
        
        # 创建会话
        session = requests.Session()
        
        # 1. 获取登录页面
        print("1. 获取登录页面...")
        response = session.get(login_url)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ 无法访问登录页面: {response.status_code}")
            return False
        
        # 2. 解析CSRF token
        soup = BeautifulSoup(response.text, 'html.parser')
        csrf_token = None
        csrf_input = soup.find('input', {'name': 'csrf_token'})
        if csrf_input:
            csrf_token = csrf_input.get('value')
            print(f"   CSRF Token: {csrf_token[:20]}..." if csrf_token else "   CSRF Token: 无")
        
        # 3. 准备登录数据
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        if csrf_token:
            login_data['csrf_token'] = csrf_token
        
        print(f"2. 提交登录数据...")
        print(f"   用户名: {login_data['username']}")
        print(f"   密码: {login_data['password']}")
        
        # 4. 提交登录请求
        response = session.post(login_url, data=login_data, allow_redirects=False)
        print(f"   响应状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        # 5. 分析响应
        if response.status_code == 302:
            # 重定向，可能登录成功
            location = response.headers.get('Location', '')
            print(f"✅ 重定向到: {location}")
            
            if 'login' in location:
                print("❌ 重定向回登录页面，登录失败")
                return False
            else:
                print("✅ 登录成功！")
                return True
                
        elif response.status_code == 200:
            # 返回登录页面，检查是否有错误消息
            soup = BeautifulSoup(response.text, 'html.parser')
            error_div = soup.find('div', class_='error-message')
            if error_div:
                error_msg = error_div.get_text(strip=True)
                print(f"❌ 登录失败: {error_msg}")
            else:
                print("❌ 登录失败，但没有错误消息")
            return False
        else:
            print(f"❌ 意外的响应状态码: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_login():
    """直接测试登录逻辑"""
    try:
        # 确保在正确的目录
        if not os.path.isfile('app.py'):
            print("❌ 错误: 请在项目根目录下运行此脚本！")
            return False

        # 导入应用和模型
        from app import create_app
        from config import Config
        from models import db, User
        from flask_login import login_user
        
        app = create_app(Config)
        
        with app.app_context():
            print("\n🔧 直接测试登录逻辑")
            print("="*50)
            
            # 模拟登录请求
            with app.test_client() as client:
                # 1. 获取登录页面
                response = client.get('/login')
                print(f"GET /login: {response.status_code}")
                
                # 2. 提交登录数据
                response = client.post('/login', data={
                    'username': 'admin',
                    'password': 'admin123'
                }, follow_redirects=False)
                
                print(f"POST /login: {response.status_code}")
                print(f"Location: {response.headers.get('Location', '无')}")
                
                if response.status_code == 302:
                    location = response.headers.get('Location', '')
                    if '/login' in location:
                        print("❌ 重定向回登录页面")
                        # 获取错误消息
                        response = client.get('/login')
                        print(f"登录页面内容: {response.data.decode()[:500]}...")
                    else:
                        print("✅ 登录成功，重定向到首页")
                        return True
                else:
                    print(f"❌ 意外状态码: {response.status_code}")
                    print(f"响应内容: {response.data.decode()[:500]}...")
                
            return False
            
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("🚀 开始登录测试")
    print("="*50)
    
    # 测试1: HTTP请求测试
    success1 = test_login()
    
    # 测试2: 直接应用测试
    success2 = test_direct_login()
    
    print("\n" + "="*50)
    print("📋 测试结果:")
    print(f"   HTTP请求测试: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"   直接应用测试: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if not success1 and not success2:
        print("\n❌ 所有测试都失败了，需要进一步调试")
    elif success2 and not success1:
        print("\n⚠️  应用逻辑正常，但HTTP请求有问题")
    elif success1:
        print("\n✅ 登录功能正常")
