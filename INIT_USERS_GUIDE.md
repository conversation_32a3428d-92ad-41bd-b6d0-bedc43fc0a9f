# CRM系统初始化指南

## 📋 概述

本指南提供了完整的CRM系统初始化脚本，用于在服务器上快速设置CRM系统的权限系统、用户账户和演示数据。

## 🚀 脚本说明

### `init_crm.py` - 完整初始化脚本

**用途**: 服务器部署后的完整初始化，包括权限系统、用户账户和可选的演示数据。

**特点**:
- ✅ 功能完整，一个脚本解决所有初始化需求
- ✅ 自动初始化完整的权限系统（5个角色，17个权限）
- ✅ 自动处理数据库表创建
- ✅ 支持创建演示数据
- ✅ 支持自定义管理员密码
- ✅ 交互式确认操作
- ✅ 错误处理完善

**使用方法**:
```bash
# 基础初始化（仅创建超级管理员）
python init_crm.py

# 自定义管理员密码
python init_crm.py --password "your_password"

# 创建演示数据（包含演示公司、部门、用户）
python init_crm.py --demo

# 强制重新初始化（跳过确认）
python init_crm.py --force

# 组合使用
python init_crm.py --password "mypassword" --demo --force
```

**创建内容**:
- 完整的权限系统（5个角色，17个权限）
- 基础角色（超级管理员、公司管理员、部门管理员、普通员工、销售）
- 默认公司
- 超级管理员账户（admin/admin123）
- 演示数据（可选）：演示公司、销售部门、演示用户

## 🔧 服务器部署步骤

### 步骤1: 上传和解压
```bash
# 上传部署包到服务器
scp crm_deploy_*.tar.gz user@server:/path/to/deployment/

# 解压部署包
tar -xzf crm_deploy_*.tar.gz
cd crm_project/
```

### 步骤2: 安装依赖
```bash
# 安装Python依赖
pip install -r requirements.txt

# 或使用虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows
pip install -r requirements.txt
```

### 步骤3: 配置环境
```bash
# 复制环境配置文件
cp .env.prod .env

# 编辑配置文件（数据库连接等）
nano .env
```

### 步骤4: 初始化用户
```bash
# 基础初始化
python init_crm.py

# 或创建演示数据
python init_crm.py --demo
```

### 步骤5: 启动应用
```bash
# 使用uwsgi启动（生产环境）
uwsgi --ini uwsgi.ini

# 或直接启动（开发环境）
python app.py
```

## 📝 默认账户信息

### 超级管理员
- **用户名**: `admin`
- **密码**: `admin123`
- **邮箱**: `<EMAIL>`
- **权限**: 系统最高权限

### 演示账户（仅在使用 --demo 选项时创建）
- **公司管理员**: `company_admin` / `123456`
- **演示员工1**: `employee1` / `123456` (销售角色)
- **演示员工2**: `employee2` / `123456` (销售角色)

## ⚠️ 安全提醒

1. **立即修改密码**: 首次登录后请立即修改默认密码
2. **删除脚本**: 初始化完成后建议删除初始化脚本
3. **权限控制**: 根据实际需要创建用户和分配权限
4. **定期备份**: 定期备份数据库和重要配置

## 🔍 故障排除

### 常见问题

**1. 导入错误**
```
ImportError: No module named 'app'
```
**解决方案**: 确保在项目根目录（包含app.py的目录）下运行脚本

**2. 数据库连接错误**
```
sqlalchemy.exc.OperationalError: ...
```
**解决方案**: 检查数据库配置和连接信息

**3. 权限错误**
```
PermissionError: [Errno 13] Permission denied
```
**解决方案**: 使用适当的用户权限运行脚本

### 调试模式

如果遇到问题，可以在Python中逐步执行：
```python
# 进入Python交互模式
python

# 逐步执行
from app import create_app
from config import Config
app = create_app(Config)

with app.app_context():
    from models import db, User, Role
    # 检查数据库连接
    print(User.query.count())
```

## 📞 技术支持

如果在初始化过程中遇到问题：

1. 检查错误日志：`logs/app.log`
2. 确认数据库配置正确
3. 验证所有依赖包已安装
4. 确保有足够的文件系统权限

## 🎯 下一步操作

初始化完成后，建议进行以下操作：

1. **登录系统**: 使用创建的管理员账户登录
2. **修改密码**: 更改默认密码为安全密码
3. **创建组织**: 根据实际情况创建公司和部门结构
4. **添加用户**: 为团队成员创建账户
5. **配置权限**: 根据角色分配适当的权限
6. **系统配置**: 完善邮件配置等系统设置

---

**版本**: 1.0  
**更新时间**: 2025-06-20  
**适用版本**: CRM系统 v2.0+
