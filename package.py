#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""CRM系统打包脚本 - 包含业绩排名权限排除逻辑等最新修改"""

import os
import sys
import time
import shutil
import hashlib
import tarfile
import argparse
from datetime import datetime

def md5(fname):
    """计算文件MD5值"""
    hash_md5 = hashlib.md5()
    with open(fname, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def get_size(fname):
    """获取文件大小的人类可读格式"""
    size = os.path.getsize(fname)
    for unit in ['B','KB','MB','GB']:
        if size < 1024.0:
            return f"{size:.1f}{unit}"
        size /= 1024.0
    return f"{size:.1f}TB"

def clean_project():
    """清理项目文件"""
    print("清理临时文件...")

    # 删除Python缓存
    for root, dirs, files in os.walk(".", topdown=True):
        dirs[:] = [d for d in dirs if d != "__pycache__"]
        for file in files:
            if file.endswith((".pyc", ".pyo")) or file in [".DS_Store", "Thumbs.db"]:
                try:
                    os.remove(os.path.join(root, file))
                except OSError:
                    pass

    # 删除测试文件
    test_files = ["test_ranking_exclusion.py", "create_test_data.py"]
    for test_file in test_files:
        if os.path.exists(test_file):
            try:
                os.remove(test_file)
                print(f"删除测试文件: {test_file}")
            except OSError:
                pass

def create_package():
    """创建部署包"""
    # 确保在项目根目录执行
    if not os.path.isfile("app.py"):
        print("错误：请在项目根目录下运行此脚本！")
        sys.exit(1)

    # 清理项目
    clean_project()

    # 输出文件名
    pkg_name = f"crm_deploy_{time.strftime('%Y%m%d_%H%M%S')}.tar.gz"

    print("="*60)
    print("🚀 CRM系统打包工具")
    print("="*60)
    print(f"📦 创建部署包: {pkg_name}")
    print(f"📅 打包时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # 要包含的文件和目录
    include_items = [
        'app.py', 'wsgi.py', 'config.py', 'requirements.txt', 'uwsgi.ini', 'deploy.py',
        'init_crm.py', 'INIT_USERS_GUIDE.md',  # 添加初始化脚本
        '.env', '.env.prod',
        'blueprints/', 'config/', 'forms/', 'models/', 'routes/', 'services/', 'tasks/', 'utils/',
        'templates/', 'static/', 'migrations/'
    ]

    print("📋 本次打包包含的重要更新:")
    print("  ✅ 业绩排名权限排除逻辑 - 管理员和负责人不参与排名")
    print("  ✅ 删除公司功能修复 - 正确处理外键约束")
    print("  ✅ 组织架构管理优化")
    print("  ✅ 权限控制完善")
    print("  ✅ 数据库约束修复")
    print("  ✅ 用户初始化脚本 - 服务器部署必备工具")
    print()
    
    # 创建tar.gz归档
    print("📁 正在打包文件...")
    with tarfile.open(pkg_name, "w:gz") as tar:
        for item in include_items:
            if os.path.exists(item):
                tar.add(item)
                print(f"  ✅ 添加: {item}")
            else:
                print(f"  ⚠️  跳过: {item} (不存在)")

    # 创建MD5校验文件
    print("\n🔐 生成MD5校验...")
    md5_value = md5(pkg_name)
    with open(f"{pkg_name}.md5", "w") as f:
        f.write(f"{md5_value}  {pkg_name}\n")

    # 创建更新日志
    changelog_file = f"{pkg_name}.changelog"
    with open(changelog_file, "w", encoding="utf-8") as f:
        f.write(f"CRM系统更新日志 - {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("="*60 + "\n\n")
        f.write("🔥 重要更新:\n")
        f.write("1. 业绩排名权限排除逻辑\n")
        f.write("   - 角色管理员(super_admin, company_admin, department_admin)不参与排名\n")
        f.write("   - 公司负责人不参与排名\n")
        f.write("   - 部门负责人不参与排名\n")
        f.write("   - 只有普通员工参与业绩排名\n\n")
        f.write("2. 删除公司功能修复\n")
        f.write("   - 修复外键约束错误\n")
        f.write("   - 按依赖关系正确删除相关数据\n")
        f.write("   - 删除顺序: 线索数据 -> 线索池 -> 部门 -> 用户 -> 公司\n\n")
        f.write("3. 组织架构管理优化\n")
        f.write("   - 完善权限控制逻辑\n")
        f.write("   - 优化数据验证\n\n")
        f.write("4. 数据库约束修复\n")
        f.write("   - 解决lead_pool.company_id约束问题\n")
        f.write("   - 完善数据完整性检查\n\n")
        f.write("5. 用户初始化脚本\n")
        f.write("   - init_crm.py: 完整初始化脚本（合并版本）\n")
        f.write("   - 支持快速初始化和演示数据创建\n")
        f.write("   - INIT_USERS_GUIDE.md: 详细使用指南\n\n")
        f.write("📋 技术细节:\n")
        f.write("- 修改文件: utils/homepage_utils.py (业绩排名逻辑)\n")
        f.write("- 修改文件: routes/organization_unified.py (删除公司逻辑)\n")
        f.write("- 数据库: 增强外键约束处理\n")
        f.write("- 权限: 三层排除机制(角色+公司负责人+部门负责人)\n\n")

    # 显示结果
    size = get_size(pkg_name)
    print("\n" + "="*60)
    print("🎉 打包完成！")
    print("="*60)
    print(f"📦 部署包: {pkg_name}")
    print(f"🔐 MD5校验: {pkg_name}.md5")
    print(f"📝 更新日志: {changelog_file}")
    print(f"🔢 MD5值: {md5_value}")
    print(f"📏 大小: {size}")
    print("="*60)
    print("🚀 部署步骤:")
    print(f"1. 📤 上传 {pkg_name} 到服务器")
    print(f"2. 📂 解压: tar -xzf {pkg_name}")
    print("3. 🔧 运行: sudo python deploy.py")
    print("4. 👤 初始化: python init_crm.py")
    print("5. 🌐 访问: http://crm.jeyue.net")
    print("6. 🔑 登录: admin / admin123")
    print("="*60)
    print("⚠️  重要提醒:")
    print("- 部署前请备份现有数据库")
    print("- 本次更新包含业绩排名逻辑重要修改")
    print("- 管理员和负责人将不再出现在业绩排名中")
    print("="*60)

if __name__ == "__main__":
    create_package()
