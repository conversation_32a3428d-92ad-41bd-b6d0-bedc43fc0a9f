{% extends "base.html" %}

{% block title %}员工管理{% endblock %}

{% block extra_css %}
<style>
    .employee-card {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        transition: all 0.2s;
    }
    
    .employee-card:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transform: translateY(-1px);
    }
    

    
    .manager-badge {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        font-size: 0.75em;
        padding: 2px 8px;
        border-radius: 12px;
        margin-left: 8px;
    }
    
    .status-badge {
        font-size: 0.8em;
        padding: 4px 8px;
        border-radius: 12px;
    }
    
    .edit-mode {
        background-color: #fff3cd;
        border-color: #ffeaa7;
    }
    
    .edit-controls {
        display: none;
    }
    
    .edit-mode .edit-controls {
        display: block;
    }
    
    .edit-mode .display-content {
        display: none;
    }
    
    .contact-info {
        font-size: 0.9em;
        color: #6c757d;
    }
    
    .employee-actions {
        opacity: 0;
        transition: opacity 0.2s;
    }
    
    .employee-card:hover .employee-actions {
        opacity: 1;
    }
    
    .filter-section {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .add-employee-section {
        background: #e8f5e8;
        border: 2px dashed #28a745;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .add-employee-form {
        display: none;
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-top: 15px;
    }
</style>
{% endblock %}

{% block content %}
<!-- 页面标题区域 -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">
                <i class="fas fa-users me-2"></i>员工管理
            </h1>
            <p class="page-subtitle">管理系统中的所有员工信息，包括员工基本信息、角色权限和状态管理</p>
        </div>
        <div class="text-end">
            <div class="btn-group">
                {% if current_user.has_permission('user_manage') %}
                <a href="{{ url_for('organization_unified.add_employee', department_id=selected_department_id) if selected_department_id else url_for('organization_unified.add_employee') }}" class="btn btn-success">
                    <i class="fas fa-plus me-1"></i>添加员工
                </a>
                {% endif %}
                <a href="{{ url_for('organization_unified.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回总览
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">

    <!-- 统计信息 - 与线索管理样式完全一致 -->
    <div class="row mb-4">
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-people text-primary" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-primary mt-2 mb-1">总员工数</h6>
                    <h4 class="mb-1">{{ employees|length }}</h4>
                    <small class="text-muted">全部员工</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-person-check text-success" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-success mt-2 mb-1">在职员工</h6>
                    <h4 class="mb-1">{{ employees|selectattr('status', 'equalto', 'active')|list|length }}</h4>
                    <small class="text-muted">正常状态</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-shield-check text-warning" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-warning mt-2 mb-1">管理员</h6>
                    <h4 class="mb-1">{{ employees|selectattr('role_name', 'in', ['超级管理员', '公司管理员', '部门管理员'])|list|length }}</h4>
                    <small class="text-muted">权限用户</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-building text-info" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-info mt-2 mb-1">涉及公司</h6>
                    <h4 class="mb-1">{{ employees|map(attribute='company_id')|unique|list|length }}</h4>
                    <small class="text-muted">公司数量</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 筛选和搜索 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body py-2">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            <select class="form-select form-select-sm" id="company-filter">
                                <option value="">所有公司</option>
                                {% for company in scope.companies %}
                                <option value="{{ company.id }}" {% if selected_company_id == company.id %}selected{% endif %}>
                                    {{ company.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select form-select-sm" id="department-filter">
                                <option value="">所有部门</option>
                                {% for department in scope.departments %}
                                <option value="{{ department.id }}"
                                        data-company="{{ department.company_id }}"
                                        {% if selected_department_id == department.id %}selected{% endif %}>
                                    {{ department.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select form-select-sm" id="role-filter">
                                <option value="">所有角色</option>
                                <option value="manager">管理员</option>
                                <option value="employee">普通员工</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select form-select-sm" id="status-filter">
                                <option value="">所有状态</option>
                                <option value="active">在职</option>
                                <option value="inactive">离职</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <div class="input-group input-group-sm">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="search-input"
                                       placeholder="搜索员工姓名、邮箱或电话...">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-outline-secondary w-100" id="reset-filter-btn" title="重置所有筛选条件">
                                <i class="fas fa-undo me-1"></i>重置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    

    
    <!-- 员工列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> 员工列表
                        </h5>
                        <div class="text-muted">
                            共 <span id="totalCount">{{ employees|length }}</span> 名员工
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="employeesTable">
                            <thead class="table-light">
                                <tr>
                                    <th>员工信息</th>
                                    <th>所属公司</th>
                                    <th>所属部门</th>
                                    <th style="width: 150px;">角色</th>
                                    <th>联系方式</th>
                                    <th>状态</th>
                                    <th style="width: 120px;">创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for employee in employees %}
                                <tr class="employee-item"
                                    data-company="{{ employee.company_id }}"
                                    data-department="{{ employee.department_id }}"
                                    data-role="{{ 'manager' if employee.role_name in ['超级管理员', '公司管理员', '部门管理员'] else 'employee' }}"
                                    data-status="{{ employee.status }}"
                                    data-search="{{ employee.name|lower }} {{ employee.email|lower }} {{ (employee.phone or '')|mask_phone }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                {% if employee.role_name in ['超级管理员', '公司管理员', '部门管理员'] %}
                                                <i class="fas fa-user-tie text-warning fa-lg"></i>
                                                {% else %}
                                                <i class="fas fa-user text-info fa-lg"></i>
                                                {% endif %}
                                            </div>
                                            <div>
                                                <strong>{{ employee.name }}</strong>
                                                {% if employee.role_name in ['超级管理员', '公司管理员', '部门管理员'] %}
                                                <span class="badge bg-warning text-dark ms-1">管理员</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <i class="fas fa-building text-primary me-1"></i>
                                        {{ employee.company.name if employee.company else '-' }}
                                    </td>
                                    <td>
                                        <i class="fas fa-users-cog text-success me-1"></i>
                                        {{ employee.department.name if employee.department else '-' }}
                                    </td>
                                    <td>
                                        {{ employee.role.code | translate_role if employee.role else '-' }}
                                    </td>
                                    <td>
                                        {% if employee.email %}
                                        <div><i class="fas fa-envelope text-muted me-1"></i> {{ employee.email }}</div>
                                        {% endif %}
                                        {% if employee.phone %}
                                        <div><i class="fas fa-phone text-muted me-1"></i> {{ employee.phone|mask_phone }}</div>
                                        {% endif %}
                                        {% if not employee.email and not employee.phone %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if employee.status == 'active' else 'secondary' }}">
                                            {{ '在职' if employee.status == 'active' else '离职' }}
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ employee.created_at.strftime('%Y-%m-%d') if employee.created_at else '-' }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            {% if current_user.has_permission('user_manage') %}
                                            <button class="btn btn-outline-primary edit-employee-btn"
                                                    data-employee-id="{{ employee.id }}" title="编辑员工">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger delete-employee-btn"
                                                    data-employee-id="{{ employee.id }}"
                                                    data-employee-name="{{ employee.name }}" title="删除员工">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 空状态 -->
    {% if not employees %}
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无员工数据</h5>
                    <p class="text-muted">您的权限范围内暂时没有员工，或者员工数据正在加载中</p>
                    {% if current_user.has_permission('user_manage') %}
                    <a href="{{ url_for('organization_unified.add_employee', department_id=selected_department_id) if selected_department_id else url_for('organization_unified.add_employee') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 添加第一个员工
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>



<!-- 删除员工确认模态框 -->
<div class="modal fade" id="deleteEmployeeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning"></i> 确认删除
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>您确定要删除员工 <strong id="deleteEmployeeName"></strong> 吗？</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>警告：</strong>删除操作不可恢复，请谨慎操作！
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteEmployeeBtn">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 员工管理页面交互逻辑
$(document).ready(function() {


    // 筛选功能
    function filterEmployees() {
        const companyFilter = $('#company-filter').val();
        const departmentFilter = $('#department-filter').val();
        const roleFilter = $('#role-filter').val();
        const statusFilter = $('#status-filter').val();
        const searchText = $('#search-input').val().toLowerCase();

        let visibleCount = 0;
        $('.employee-item').each(function() {
            const $item = $(this);
            let show = true;

            if (companyFilter && $item.data('company') != companyFilter) show = false;
            if (departmentFilter && $item.data('department') != departmentFilter) show = false;
            if (roleFilter && $item.data('role') !== roleFilter) show = false;
            if (statusFilter && $item.data('status') !== statusFilter) show = false;
            if (searchText && !$item.data('search').includes(searchText)) show = false;

            $item.toggle(show);
            if (show) visibleCount++;
        });

        $('#totalCount').text(visibleCount);
    }

    // 绑定筛选事件
    $('#company-filter, #department-filter, #role-filter, #status-filter').change(filterEmployees);
    $('#search-input').on('input', filterEmployees);
    $('#search-btn').click(filterEmployees);

    // 公司选择时更新部门选项
    $('#company-filter').change(function() {
        const companyId = $(this).val();
        const $deptFilter = $('#department-filter');

        $deptFilter.find('option:not(:first)').hide();
        if (companyId) {
            $deptFilter.find(`option[data-company="${companyId}"]`).show();
        } else {
            $deptFilter.find('option').show();
        }
        $deptFilter.val('');
        filterEmployees();
    });

    // 重置筛选
    $('#reset-filter-btn').click(function() {
        $('#company-filter, #department-filter, #role-filter, #status-filter').val('');
        $('#search-input').val('');
        $('.employee-item').show();
        $('#totalCount').text($('.employee-item').length);
    });



    // 编辑员工 - 使用事件委托
    $(document).on('click', '.edit-employee-btn', function(e) {
        e.preventDefault();
        const employeeId = $(this).data('employee-id');

        if (employeeId) {
            const editUrl = `/organization/employees/${employeeId}/edit`;
            window.location.href = editUrl;
        } else {
            alert('员工ID获取失败');
        }
    });

    // 删除员工 - 使用事件委托
    $(document).on('click', '.delete-employee-btn', function(e) {
        e.preventDefault();
        const employeeId = $(this).data('employee-id');
        const employeeName = $(this).data('employee-name');

        $('#deleteEmployeeName').text(employeeName);
        $('#confirmDeleteEmployeeBtn').data('employee-id', employeeId);

        const modal = new bootstrap.Modal(document.getElementById('deleteEmployeeModal'));
        modal.show();
    });

    // 确认删除员工
    $('#confirmDeleteEmployeeBtn').click(function() {
        const employeeId = $(this).data('employee-id');
        if (!employeeId) return;

        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/organization/employees/${employeeId}/delete`;

        // 添加CSRF token
        const csrfToken = document.querySelector('meta[name=csrf-token]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    });




});
</script>
{% endblock %}
