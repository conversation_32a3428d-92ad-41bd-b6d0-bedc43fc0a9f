from flask import session
import random, time, json, hashlib
from datetime import datetime

class BehaviorCaptcha:  # 行为验证码
    def __init__(self):
        self.actions = ['click', 'move']  # 支持的行为类型
        self.shapes = ['circle', 'triangle', 'square']  # 支持的图形
        self.colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']  # 图形颜色
        self.expire_time = 300  # 5分钟过期

    def generate(self):  # 生成验证任务
        action = random.choice(self.actions)
        shape = random.choice(self.shapes)
        color = random.choice(self.colors)
        timestamp = int(time.time())
        
        task = {
            'action': action,
            'shape': shape,
            'color': color,
            'timestamp': timestamp,
            'token': self._generate_token(action, shape, timestamp)
        }
        
        session['behavior_captcha'] = task
        return {
            'action': action,
            'shape': shape,
            'color': color,
            'token': task['token']
        }

    def verify(self, token, behavior_data):  # 验证用户行为
        task = session.get('behavior_captcha')
        if not task or task['token'] != token:
            return False
            
        if int(time.time()) - task['timestamp'] > self.expire_time:
            return False
            
        # 验证行为特征
        if task['action'] == 'click':
            return self._verify_click(behavior_data)
        elif task['action'] == 'move':
            return self._verify_move(behavior_data)
        return False

    def _verify_click(self, data):  # 验证点击行为
        try:
            click_time = float(data.get('click_time', 0))  # 点击用时
            click_pos = data.get('click_pos', {})  # 点击位置
            
            # 检查点击时间是否合理（通常人类点击在0.1-2秒之间）
            if not (0.1 <= click_time <= 2.0):
                return False
                
            # 检查点击位置是否在目标区域内
            if not (isinstance(click_pos, dict) and 
                   isinstance(click_pos.get('x'), (int, float)) and
                   isinstance(click_pos.get('y'), (int, float))):
                return False
                
            return True
        except:
            return False

    def _verify_move(self, data):  # 验证移动行为
        try:
            move_points = data.get('move_points', [])  # 移动轨迹点
            move_time = float(data.get('move_time', 0))  # 移动总时间
            
            # 检查移动时间是否合理（通常人类操作在0.3-3秒之间）
            if not (0.3 <= move_time <= 3.0):
                return False
                
            # 检查轨迹点数量（太少或太多都不合理）
            if not (5 <= len(move_points) <= 100):
                return False
                
            # 检查移动轨迹是否平滑（计算加速度变化）
            accelerations = []
            for i in range(2, len(move_points)):
                t1, t2 = move_points[i-2]['t'], move_points[i]['t']
                x1, x2 = move_points[i-2]['x'], move_points[i]['x']
                y1, y2 = move_points[i-2]['y'], move_points[i]['y']
                
                if t2 - t1 == 0:  # 避免除零
                    return False
                    
                acc = ((x2-x1)**2 + (y2-y1)**2)**0.5 / ((t2-t1)**2)
                accelerations.append(acc)
            
            # 计算加速度变化的标准差（人类操作的标准差通常在一定范围内）
            if accelerations:
                mean_acc = sum(accelerations) / len(accelerations)
                std_acc = (sum((a - mean_acc)**2 for a in accelerations) / len(accelerations))**0.5
                if not (1 <= std_acc <= 100):  # 经验值，可根据实际情况调整
                    return False
            
            return True
        except:
            return False

    def _generate_token(self, action, shape, timestamp):  # 生成验证token
        data = f"{action}{shape}{timestamp}{random.random()}"
        return hashlib.sha256(data.encode()).hexdigest()[:32] 