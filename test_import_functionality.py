#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试线索导入功能的脚本
"""

import os
import sys
import csv
import io
from datetime import datetime

def test_import_functionality():
    """测试导入功能"""
    try:
        # 确保在正确的目录
        if not os.path.isfile('app.py'):
            print("❌ 错误: 请在项目根目录下运行此脚本！")
            return False

        # 导入应用和模型
        from app import create_app
        from config import Config
        from models import db, Lead, User, Company
        from utils.lead_utils import _process_lead_import_data
        
        app = create_app(Config)
        
        with app.app_context():
            print("🧪 测试线索导入功能")
            print("="*60)
            
            # 1. 检查基础数据
            companies = Company.query.all()
            users = User.query.filter_by(status='active').all()
            
            if len(companies) < 2:
                print("❌ 需要至少2个公司来测试跨公司导入")
                return False
                
            if len(users) < 1:
                print("❌ 需要至少1个活跃用户来测试导入")
                return False
            
            test_company = companies[0]
            test_user = users[0]
            
            print(f"✅ 使用测试公司: {test_company.name}")
            print(f"✅ 使用测试用户: {test_user.name}")
            
            # 2. 创建测试CSV数据
            print("\n🔧 创建测试CSV数据...")
            test_data = [
                ['姓名', '电话', '省份', '城市', '备注'],
                ['测试客户1', '13800000001', '广东省', '深圳市', '测试导入1'],
                ['测试客户2', '13800000002', '北京市', '北京市', '测试导入2'],
                ['测试客户3', '13800000003', '上海市', '上海市', '测试导入3'],
                ['', '13800000004', '江苏省', '南京市', '姓名为空测试'],  # 应该失败
                ['测试客户5', '', '浙江省', '杭州市', '电话为空测试'],  # 应该失败
            ]
            
            # 创建CSV字符串
            csv_content = io.StringIO()
            writer = csv.writer(csv_content)
            for row in test_data:
                writer.writerow(row)
            csv_content.seek(0)
            
            print(f"   创建了包含 {len(test_data)-1} 行数据的测试CSV")
            
            # 3. 测试导入功能
            print("\n🧪 测试导入处理...")
            
            # 记录导入前的线索数量
            before_count = Lead.query.filter_by(company_id=test_company.id).count()
            print(f"   导入前线索数量: {before_count}")
            
            # 执行导入
            success_count, error_count, error_messages = _process_lead_import_data(
                csv_content, 
                test_company.id, 
                test_user.id
            )
            
            # 检查结果
            after_count = Lead.query.filter_by(company_id=test_company.id).count()
            print(f"   导入后线索数量: {after_count}")
            print(f"   成功导入: {success_count}")
            print(f"   失败数量: {error_count}")
            
            if error_messages:
                print("   错误信息:")
                for msg in error_messages[:3]:  # 只显示前3个错误
                    print(f"     - {msg}")
            
            # 4. 验证导入结果
            print("\n✅ 验证导入结果...")
            
            # 检查成功导入的线索
            imported_leads = Lead.query.filter(
                Lead.company_id == test_company.id,
                Lead.name.like('测试客户%'),
                Lead.is_self_created == False
            ).all()
            
            print(f"   找到 {len(imported_leads)} 条导入的测试线索:")
            for lead in imported_leads:
                print(f"     - {lead.name}: {lead.phone} ({lead.company_name})")
            
            # 5. 测试重复数据检查
            print("\n🔄 测试重复数据检查...")
            csv_content.seek(0)  # 重置文件指针
            
            # 再次导入相同数据
            success_count2, error_count2, error_messages2 = _process_lead_import_data(
                csv_content, 
                test_company.id, 
                test_user.id
            )
            
            print(f"   第二次导入 - 成功: {success_count2}, 失败: {error_count2}")
            
            if success_count2 == 0:
                print("   ✅ 重复数据检查正常工作")
            else:
                print("   ⚠️  重复数据检查可能有问题")
            
            # 6. 清理测试数据
            print("\n🧹 清理测试数据...")
            cleanup_count = 0
            for lead in imported_leads:
                db.session.delete(lead)
                cleanup_count += 1
            
            db.session.commit()
            print(f"   清理了 {cleanup_count} 条测试线索")
            
            # 7. 总结
            print("\n" + "="*60)
            print("📊 测试总结:")
            print("="*60)
            
            if success_count > 0 and error_count > 0:
                print("✅ 导入功能正常工作")
                print("✅ 数据验证正常工作")
                print("✅ 错误处理正常工作")
            elif success_count > 0:
                print("✅ 导入功能正常工作")
                print("⚠️  数据验证可能过于宽松")
            else:
                print("❌ 导入功能可能有问题")
                
            if success_count2 == 0:
                print("✅ 重复数据检查正常工作")
            else:
                print("⚠️  重复数据检查需要改进")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_import_functionality()
