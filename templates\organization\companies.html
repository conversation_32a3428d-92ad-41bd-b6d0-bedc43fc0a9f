{% extends 'base.html' %}

{% block title %}公司管理 - CRM系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>公司管理</h1>
    <div>
        <a href="{{ url_for('organization.add_company') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> 添加公司
        </a>
    </div>
</div>

<!-- 公司列表 -->
<div class="card shadow">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>公司名称</th>
                        <th>公司代码</th>
                        <th>类型</th>
                        <th>上级公司</th>
                        <th>部门数量</th>
                        <th>用户数量</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for company in companies %}
                    <tr>
                        <td>{{ company.name }}</td>
                        <td>{{ company.code }}</td>
                        <td>
                            {% if company.type == 'headquarters' %}
                            <span class="badge bg-primary">总公司</span>
                            {% else %}
                            <span class="badge bg-info">子公司</span>
                            {% endif %}
                        </td>
                        <td>{{ company.parent.name if company.parent else '-' }}</td>
                        <td>{{ company.departments|length }}</td>
                        <td>{{ company.users|length }}</td>
                        <td>{{ company.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            <a href="{{ url_for('organization.departments', company_id=company.id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-diagram-3"></i> 部门管理
                            </a>
                            <a href="{{ url_for('company.edit_company', company_id=company.id) }}" class="btn btn-sm btn-outline-warning">
                                <i class="bi bi-pencil"></i> 编辑
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteCompanyModal{{ company.id }}">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </td>
                    </tr>
                    <!-- 删除公司确认模态框 -->
                    <div class="modal fade" id="deleteCompanyModal{{ company.id }}" tabindex="-1" aria-labelledby="deleteCompanyModalLabel{{ company.id }}" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="deleteCompanyModalLabel{{ company.id }}">确认删除</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <p>确定要删除公司 <strong>{{ company.name }}</strong> 吗？此操作不可恢复。</p>
                                    <p class="text-danger">请输入公司名称 <strong>{{ company.name }}</strong> 以确认删除：</p>
                                    <form method="POST" action="{{ url_for('organization.delete_company', company_id=company.id) }}" id="deleteForm{{ company.id }}">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <div class="mb-3">
                                            <input type="text" class="form-control" name="confirm_name" required placeholder="请输入公司名称确认">
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                    <button type="submit" class="btn btn-danger" form="deleteForm{{ company.id }}">确认删除</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}