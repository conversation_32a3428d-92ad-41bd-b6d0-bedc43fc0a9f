/**
 * 统一组织管理页面交互逻辑
 */

class OrganizationManager {
    constructor() {
        this.currentNode = null;
        this.currentNodeType = null;
        this.currentNodeId = null;
        this.editMode = false;
        
        this.init();
    }
    
    init() {
        this.initTree();
        this.bindEvents();
        this.loadInitialData();
    }
    
    /**
     * 初始化组织架构树
     */
    initTree() {
        $('#organization-tree').jstree({
            'core': {
                'data': {
                    'url': '/organization/api/tree',
                    'dataType': 'json'
                },
                'check_callback': true,
                'themes': {
                    'responsive': true,
                    'variant': 'large'
                }
            },
            'plugins': ['contextmenu', 'search', 'types'],
            'types': {
                'company': {
                    'icon': 'fas fa-building text-primary'
                },
                'department': {
                    'icon': 'fas fa-sitemap text-success'
                },
                'user': {
                    'icon': 'fas fa-user text-info'
                }
            },
            'contextmenu': {
                'items': (node) => this.getContextMenu(node)
            },
            'search': {
                'case_insensitive': true,
                'show_only_matches': true
            }
        });
    }
    
    /**
     * 绑定事件处理器
     */
    bindEvents() {
        // 树节点选择事件
        $('#organization-tree').on('select_node.jstree', (e, data) => {
            this.onNodeSelect(data.node);
        });
        
        // 搜索功能
        let searchTimeout = null;
        $('#tree-search').on('keyup', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                const searchText = $(e.target).val();
                $('#organization-tree').jstree('search', searchText);
            }, 300);
        });
        
        // 快速操作按钮
        $('#add-company-btn').click(() => this.showAddForm('company'));
        $('#add-department-btn').click(() => this.showAddForm('department'));
        $('#add-employee-btn').click(() => this.showAddForm('user'));
        $('#refresh-tree-btn').click(() => this.refreshTree());
        
        // 标签切换事件
        $('.detail-tabs a').on('click', (e) => {
            e.preventDefault();
            const tabId = $(e.target).attr('href').substring(1);
            this.switchTab(tabId);
        });
    }
    
    /**
     * 加载初始数据
     */
    loadInitialData() {
        // 页面加载时显示默认内容
        this.showWelcomeMessage();
    }
    
    /**
     * 节点选择处理
     */
    onNodeSelect(node) {
        this.currentNode = node;
        this.currentNodeType = node.type;
        this.currentNodeId = node.id.split('_')[1]; // 假设ID格式为 "type_id"
        
        // 更新详情面板标题
        this.updateDetailTitle(node);
        
        // 加载当前标签内容
        const activeTab = $('.detail-tabs .nav-link.active').attr('href').substring(1);
        this.switchTab(activeTab);
    }
    
    /**
     * 更新详情面板标题
     */
    updateDetailTitle(node) {
        const typeIcons = {
            'company': 'fas fa-building',
            'department': 'fas fa-sitemap',
            'user': 'fas fa-user'
        };
        
        const typeNames = {
            'company': '公司',
            'department': '部门',
            'user': '员工'
        };
        
        const icon = typeIcons[node.type] || 'fas fa-circle';
        const typeName = typeNames[node.type] || '节点';
        
        $('#detail-title').html(`<i class="${icon}"></i> ${node.text} <small class="text-muted">(${typeName})</small>`);
        
        // 更新操作按钮
        this.updateDetailActions(node);
    }
    
    /**
     * 更新详情操作按钮
     */
    updateDetailActions(node) {
        let actions = '';
        
        if (this.hasPermission('edit', node.type)) {
            actions += `<button class="btn btn-primary btn-sm mr-2" onclick="orgManager.toggleEditMode()">
                <i class="fas fa-edit"></i> 编辑
            </button>`;
        }
        
        if (this.hasPermission('delete', node.type)) {
            actions += `<button class="btn btn-danger btn-sm" onclick="orgManager.deleteNode()">
                <i class="fas fa-trash"></i> 删除
            </button>`;
        }
        
        $('#detail-actions').html(actions);
    }
    
    /**
     * 切换标签
     */
    switchTab(tabId) {
        if (!this.currentNode) {
            this.showWelcomeMessage();
            return;
        }
        
        // 更新标签状态
        $('.detail-tabs .nav-link').removeClass('active');
        $(`#${tabId}-tab`).addClass('active');
        
        $('.tab-pane').removeClass('show active');
        $(`#${tabId}`).addClass('show active');
        
        // 加载标签内容
        this.loadTabContent(tabId);
    }
    
    /**
     * 加载标签内容
     */
    loadTabContent(tabId) {
        const contentDiv = $(`#${tabId}-content`);
        contentDiv.html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 加载中...</div>');
        
        // 根据标签类型和节点类型加载不同内容
        switch(tabId) {
            case 'overview':
                this.loadOverviewContent(contentDiv);
                break;
            case 'info':
                this.loadInfoContent(contentDiv);
                break;
            case 'departments':
                this.loadDepartmentsContent(contentDiv);
                break;
            case 'employees':
                this.loadEmployeesContent(contentDiv);
                break;
        }
    }
    
    /**
     * 加载概览内容
     */
    loadOverviewContent(contentDiv) {
        $.get(`/organization/api/${this.currentNodeType}/${this.currentNodeId}/overview`)
            .done((data) => {
                let html = `
                    <div class="info-card">
                        <h5><i class="fas fa-chart-pie"></i> 统计信息</h5>
                        <div class="row">
                            <div class="col-md-3 text-center">
                                <h4 class="text-primary">${data.stats.departments || 0}</h4>
                                <small>部门数量</small>
                            </div>
                            <div class="col-md-3 text-center">
                                <h4 class="text-success">${data.stats.employees || 0}</h4>
                                <small>员工数量</small>
                            </div>
                            <div class="col-md-3 text-center">
                                <h4 class="text-info">${data.stats.managers || 0}</h4>
                                <small>管理员</small>
                            </div>
                            <div class="col-md-3 text-center">
                                <h4 class="text-warning">${data.stats.active || 0}</h4>
                                <small>在线人数</small>
                            </div>
                        </div>
                    </div>
                `;
                
                if (data.recent_activities && data.recent_activities.length > 0) {
                    html += `
                        <div class="info-card">
                            <h5><i class="fas fa-history"></i> 最近活动</h5>
                            <ul class="list-unstyled">
                    `;
                    
                    data.recent_activities.forEach(activity => {
                        html += `
                            <li class="mb-2">
                                <i class="fas fa-circle text-primary" style="font-size: 0.5em;"></i>
                                ${activity.description}
                                <small class="text-muted">(${activity.time})</small>
                            </li>
                        `;
                    });
                    
                    html += '</ul></div>';
                }
                
                contentDiv.html(html);
            })
            .fail(() => {
                contentDiv.html('<div class="alert alert-danger">加载概览信息失败</div>');
            });
    }
    
    /**
     * 加载基本信息内容
     */
    loadInfoContent(contentDiv) {
        $.get(`/organization/api/${this.currentNodeType}/${this.currentNodeId}`)
            .done((data) => {
                const html = this.generateInfoForm(data);
                contentDiv.html(html);
                this.bindInfoFormEvents();
            })
            .fail(() => {
                contentDiv.html('<div class="alert alert-danger">加载基本信息失败</div>');
            });
    }
    
    /**
     * 生成信息表单
     */
    generateInfoForm(data) {
        // 根据节点类型生成不同的表单
        switch(this.currentNodeType) {
            case 'company':
                return this.generateCompanyForm(data);
            case 'department':
                return this.generateDepartmentForm(data);
            case 'user':
                return this.generateUserForm(data);
            default:
                return '<div class="alert alert-warning">未知的节点类型</div>';
        }
    }

    /**
     * 生成公司表单
     */
    generateCompanyForm(data) {
        return `
            <div class="info-card">
                <h5><i class="fas fa-building"></i> 公司信息</h5>
                <form id="company-info-form">
                    <div class="form-group-inline">
                        <label>公司名称:</label>
                        <input type="text" class="form-control" name="name" value="${data.name || ''}" ${this.editMode ? '' : 'readonly'}>
                    </div>
                    <div class="form-group-inline">
                        <label>公司编码:</label>
                        <input type="text" class="form-control" name="code" value="${data.code || ''}" ${this.editMode ? '' : 'readonly'}>
                    </div>
                    <div class="form-group-inline">
                        <label>公司类型:</label>
                        <select class="form-control" name="type" ${this.editMode ? '' : 'disabled'}>
                            <option value="headquarters" ${data.type === 'headquarters' ? 'selected' : ''}>总部</option>
                            <option value="subsidiary" ${data.type === 'subsidiary' ? 'selected' : ''}>子公司</option>
                        </select>
                    </div>
                    <div class="form-group-inline">
                        <label>管理员:</label>
                        <select class="form-control" name="manager_id" ${this.editMode ? '' : 'disabled'}>
                            <option value="">请选择管理员</option>
                            <!-- 这里需要动态加载管理员选项 -->
                        </select>
                    </div>
                    <div class="form-group-inline">
                        <label>描述:</label>
                        <textarea class="form-control" name="description" rows="3" ${this.editMode ? '' : 'readonly'}>${data.description || ''}</textarea>
                    </div>
                    ${this.editMode ? `
                        <div class="text-center mt-3">
                            <button type="submit" class="btn btn-success mr-2">
                                <i class="fas fa-save"></i> 保存
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="orgManager.toggleEditMode()">
                                <i class="fas fa-times"></i> 取消
                            </button>
                        </div>
                    ` : ''}
                </form>
            </div>
        `;
    }

    /**
     * 生成部门表单
     */
    generateDepartmentForm(data) {
        return `
            <div class="info-card">
                <h5><i class="fas fa-sitemap"></i> 部门信息</h5>
                <form id="department-info-form">
                    <div class="form-group-inline">
                        <label>部门名称:</label>
                        <input type="text" class="form-control" name="name" value="${data.name || ''}" ${this.editMode ? '' : 'readonly'}>
                    </div>
                    <div class="form-group-inline">
                        <label>部门编码:</label>
                        <input type="text" class="form-control" name="code" value="${data.code || ''}" ${this.editMode ? '' : 'readonly'}>
                    </div>
                    <div class="form-group-inline">
                        <label>所属公司:</label>
                        <input type="text" class="form-control" value="${data.company_name || ''}" readonly>
                    </div>
                    <div class="form-group-inline">
                        <label>上级部门:</label>
                        <select class="form-control" name="parent_id" ${this.editMode ? '' : 'disabled'}>
                            <option value="">无</option>
                            <!-- 这里需要动态加载部门选项 -->
                        </select>
                    </div>
                    <div class="form-group-inline">
                        <label>管理员:</label>
                        <select class="form-control" name="manager_id" ${this.editMode ? '' : 'disabled'}>
                            <option value="">请选择管理员</option>
                            <!-- 这里需要动态加载管理员选项 -->
                        </select>
                    </div>
                    <div class="form-group-inline">
                        <label>描述:</label>
                        <textarea class="form-control" name="description" rows="3" ${this.editMode ? '' : 'readonly'}>${data.description || ''}</textarea>
                    </div>
                    ${this.editMode ? `
                        <div class="text-center mt-3">
                            <button type="submit" class="btn btn-success mr-2">
                                <i class="fas fa-save"></i> 保存
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="orgManager.toggleEditMode()">
                                <i class="fas fa-times"></i> 取消
                            </button>
                        </div>
                    ` : ''}
                </form>
            </div>
        `;
    }

    /**
     * 生成用户表单
     */
    generateUserForm(data) {
        return `
            <div class="info-card">
                <h5><i class="fas fa-user"></i> 员工信息</h5>
                <form id="user-info-form">
                    <div class="form-group-inline">
                        <label>姓名:</label>
                        <input type="text" class="form-control" name="name" value="${data.name || ''}" ${this.editMode ? '' : 'readonly'}>
                    </div>
                    <div class="form-group-inline">
                        <label>用户名:</label>
                        <input type="text" class="form-control" name="username" value="${data.username || ''}" readonly>
                    </div>
                    <div class="form-group-inline">
                        <label>邮箱:</label>
                        <input type="email" class="form-control" name="email" value="${data.email || ''}" ${this.editMode ? '' : 'readonly'}>
                    </div>
                    <div class="form-group-inline">
                        <label>电话:</label>
                        <input type="tel" class="form-control" name="phone" value="${data.phone || ''}" ${this.editMode ? '' : 'readonly'}>
                    </div>
                    <div class="form-group-inline">
                        <label>所属公司:</label>
                        <input type="text" class="form-control" value="${data.company_name || ''}" readonly>
                    </div>
                    <div class="form-group-inline">
                        <label>所属部门:</label>
                        <select class="form-control" name="department_id" ${this.editMode ? '' : 'disabled'}>
                            <!-- 这里需要动态加载部门选项 -->
                        </select>
                    </div>
                    <div class="form-group-inline">
                        <label>角色:</label>
                        <select class="form-control" name="role_id" ${this.editMode ? '' : 'disabled'}>
                            <!-- 这里需要动态加载角色选项 -->
                        </select>
                    </div>
                    <div class="form-group-inline">
                        <label>状态:</label>
                        <select class="form-control" name="status" ${this.editMode ? '' : 'disabled'}>
                            <option value="active" ${data.status === 'active' ? 'selected' : ''}>在职</option>
                            <option value="inactive" ${data.status === 'inactive' ? 'selected' : ''}>离职</option>
                        </select>
                    </div>
                    ${this.editMode ? `
                        <div class="text-center mt-3">
                            <button type="submit" class="btn btn-success mr-2">
                                <i class="fas fa-save"></i> 保存
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="orgManager.toggleEditMode()">
                                <i class="fas fa-times"></i> 取消
                            </button>
                        </div>
                    ` : ''}
                </form>
            </div>
        `;
    }

    /**
     * 切换编辑模式
     */
    toggleEditMode() {
        this.editMode = !this.editMode;

        // 重新加载当前标签内容
        const activeTab = $('.detail-tabs .nav-link.active').attr('href').substring(1);
        if (activeTab === 'info') {
            this.loadTabContent('info');
        }

        // 更新操作按钮
        this.updateDetailActions(this.currentNode);
    }

    /**
     * 绑定信息表单事件
     */
    bindInfoFormEvents() {
        // 绑定表单提交事件
        $('#company-info-form, #department-info-form, #user-info-form').off('submit').on('submit', (e) => {
            e.preventDefault();
            this.saveNodeInfo($(e.target));
        });
    }

    /**
     * 保存节点信息
     */
    saveNodeInfo(form) {
        const formData = {};
        form.find('input, select, textarea').each(function() {
            const name = $(this).attr('name');
            const value = $(this).val();
            if (name && value !== '') {
                formData[name] = value;
            }
        });

        $.ajax({
            url: `/organization/api/node/${this.currentNodeType}/${this.currentNodeId}`,
            method: 'PUT',
            data: JSON.stringify(formData),
            contentType: 'application/json',
            success: (response) => {
                if (response.success) {
                    this.showMessage('保存成功', 'success');
                    this.toggleEditMode();
                    this.refreshTree();
                } else {
                    this.showMessage(response.error || '保存失败', 'danger');
                }
            },
            error: () => {
                this.showMessage('网络错误，请重试', 'danger');
            }
        });
    }
    
    /**
     * 显示欢迎消息
     */
    showWelcomeMessage() {
        $('#overview-content').html(`
            <div class="text-center text-muted py-5">
                <i class="fas fa-mouse-pointer fa-3x mb-3"></i>
                <h5>欢迎使用组织管理系统</h5>
                <p>请在左侧选择一个组织节点查看详细信息</p>
                <p>您可以管理公司、部门和员工信息，并指定相应的管理员</p>
            </div>
        `);
    }
    
    /**
     * 权限检查
     */
    hasPermission(action, nodeType) {
        // 这里应该根据实际的权限系统进行检查
        // 暂时返回true，后续需要完善
        return true;
    }
    
    /**
     * 获取上下文菜单
     */
    getContextMenu(node) {
        const items = {};
        
        if (this.hasPermission('edit', node.type)) {
            items.edit = {
                label: '编辑',
                icon: 'fas fa-edit',
                action: () => this.editNode(node)
            };
        }
        
        if (this.hasPermission('delete', node.type)) {
            items.delete = {
                label: '删除',
                icon: 'fas fa-trash',
                action: () => this.deleteNode(node)
            };
        }
        
        if (node.type === 'company' && this.hasPermission('create', 'department')) {
            items.addDepartment = {
                label: '添加部门',
                icon: 'fas fa-plus',
                action: () => this.showAddForm('department', node.id)
            };
        }
        
        if ((node.type === 'company' || node.type === 'department') && this.hasPermission('create', 'user')) {
            items.addUser = {
                label: '添加员工',
                icon: 'fas fa-user-plus',
                action: () => this.showAddForm('user', node.id)
            };
        }
        
        return items;
    }
    
    /**
     * 刷新树
     */
    refreshTree() {
        $('#organization-tree').jstree('refresh');
    }
    
    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        const alertClass = `alert-${type}`;
        const alert = $(`
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `);
        
        $('.container-fluid').prepend(alert);
        
        // 3秒后自动消失
        setTimeout(() => {
            alert.alert('close');
        }, 3000);
    }
}

// 全局实例
let orgManager;

// 页面加载完成后初始化
$(document).ready(function() {
    orgManager = new OrganizationManager();
});
