from flask import Blueprint, render_template, redirect, url_for, flash
from flask_login import login_required, current_user
from models import db, Notification
from sqlalchemy import desc

# 创建蓝图
notifications_bp = Blueprint('notifications', __name__)

@notifications_bp.route('/notifications')
@login_required
def view_notifications():
    """显示用户的通知列表，并将未读通知标记为已读"""
    try:
        # 查询当前用户的所有通知，按时间倒序
        notifications_query = Notification.query.filter_by(user_id=current_user.id).order_by(desc(Notification.created_at))
        
        # 可选：添加分页
        # page = request.args.get('page', 1, type=int)
        # per_page = 20
        # pagination = notifications_query.paginate(page, per_page, error_out=False)
        # notifications = pagination.items
        notifications = notifications_query.all()

        # 标记所有显示的未读通知为已读
        unread_notifications_in_view = [n for n in notifications if not n.is_read]
        if unread_notifications_in_view:
            for notification in unread_notifications_in_view:
                notification.is_read = True
            db.session.commit()

        return render_template('notifications.html', notifications=notifications)
                            # pagination=pagination)
    except Exception as e:
        db.session.rollback()
        # 使用 current_app logger (需要传递 app 或使用应用上下文)
        # current_app.logger.error(f"获取通知时出错: {e}", exc_info=True)
        flash('加载通知失败', 'danger')
        return redirect(url_for('index')) # 重定向到首页或仪表盘 