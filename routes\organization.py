from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import db, Company, Department, User, LeadPool, Role
from forms import CompanyForm, DepartmentForm, BaseForm
from sqlalchemy import and_
from utils.department_utils import get_child_departments, get_department_tree

bp = Blueprint('organization', __name__)

@bp.route('/')
@login_required
def index():
    companies = Company.query.all()
    return render_template('organization/index.html', companies=companies)

@bp.route('/companies')
@login_required
def companies():
    if not current_user.has_permission('company_manage'):
        flash('您没有权限访问公司管理', 'danger')
        return redirect(url_for('index'))
    
    companies = Company.query.all()
    return render_template('organization/companies.html', companies=companies)

@bp.route('/companies/add', methods=['GET', 'POST'])
@login_required
def add_company():
    if not current_user.has_permission('company_manage'):
        flash('您没有权限创建公司', 'danger')
        return redirect(url_for('index'))
    
    form = CompanyForm()
    # 设置上级公司选项
    headquarters = Company.query.filter_by(type='headquarters').all()
    form.parent_id.choices = [(0, '无')] + [(c.id, c.name) for c in headquarters]
    
    # 表单提交处理
    if request.method == 'POST':
        # 如果是总部类型，确保parent_id设为0
        if request.form.get('type') == 'headquarters':
            form.parent_id.data = 0

        print(f"表单数据: {request.form}")
        print(f"表单验证结果: {form.validate()}")
        if not form.validate():
            print(f"表单验证错误: {form.errors}")
            # 不要在这里return，让代码继续执行到form.validate_on_submit()
    
    if form.validate_on_submit():
        try:
            # 1. 创建公司
            company = Company(
                name=form.name.data,
                code=form.code.data,
                type=form.type.data,
                parent_id=form.parent_id.data if form.parent_id.data != 0 else None,
                is_default=form.is_default.data
            )
            
            # 如果设置为默认公司，需要将其他公司的默认标志取消
            if form.is_default.data:
                Company.query.filter_by(is_default=True).update({'is_default': False})
                
            db.session.add(company)
            db.session.flush()  # 先提交以获取company.id
            
            # 2. 自动创建公司的公海池
            public_sea = LeadPool(
                name=f"{company.name}公海池",
                description=f"{company.name}的默认公海池，用于存放无负责人的线索",
                company_id=company.id,
                is_public_sea=True
            )
            db.session.add(public_sea)
            
            # 最终提交
            db.session.commit()
            print(f"公司创建成功: {company.id}, {company.name}")
            flash(f'公司创建成功，并已自动创建公海池：{public_sea.name}', 'success')
            return redirect(url_for('organization.companies'))
        except Exception as e:
            db.session.rollback()
            print(f"公司创建失败，异常信息: {str(e)}")
            flash(f'公司创建失败: {str(e)}', 'danger')
    
    return render_template('organization/company_form.html', form=form)

@bp.route('/companies/<int:company_id>/departments')
@login_required
def departments(company_id):
    company = Company.query.get_or_404(company_id)
    form = BaseForm()
    
    # 修改权限检查逻辑，基于权限而不是角色
    if not current_user.has_permission('department_manage'):
        flash('您没有权限访问部门管理', 'danger')
        return redirect(url_for('index'))
    
    # 对于非超级管理员，只能查看自己公司的部门
    if current_user.role.code != 'super_admin' and current_user.company_id != company_id:
        flash('您只能管理自己公司的部门', 'danger')
        return redirect(url_for('index'))
    
    # 部门管理员只能查看自己的部门及子部门
    if current_user.role.code == 'department_admin':
        # 获取部门管理员管理的所有子部门ID
        department_ids = get_child_departments(current_user.department_id)
        
        departments = Department.query.filter(
            Department.company_id == company_id,
            Department.id.in_(department_ids)
        ).all()
    else:
        # 超级管理员和公司管理员可以查看公司所有部门
        departments = Department.query.filter_by(company_id=company_id).all()
    
    return render_template('organization/departments.html', 
                         company=company, 
                         departments=departments,
                         form=form)

@bp.route('/companies/<int:company_id>/departments/add', methods=['GET', 'POST'])
@login_required
def add_department(company_id):
    company = Company.query.get_or_404(company_id)
    
    # 基于权限检查
    if not current_user.has_permission('department_manage'):
        flash('您没有权限添加部门', 'danger')
        return redirect(url_for('index'))
    
    # 对于非超级管理员，只能在自己公司添加部门
    if current_user.role.code != 'super_admin' and current_user.company_id != company_id:
        flash('您只能在自己的公司添加部门', 'danger')
        return redirect(url_for('index'))
    
    form = DepartmentForm()
    # 设置选项
    form.company_id.choices = [(company.id, company.name)]  # 只有当前公司可选
    form.company_id.data = company.id  # 预设值为当前公司
    
    # 部门管理员只能看到自己管理的部门
    if current_user.role.code == 'department_admin':
        # 获取管理员的所有子部门ID和自己的部门
        department_ids = get_child_departments(current_user.department_id)
        departments = Department.query.filter(
            Department.company_id == company_id,
            Department.id.in_(department_ids)
        ).all()
    else:
        # 超级管理员和公司管理员可以看到所有部门
        departments = Department.query.filter_by(company_id=company_id).all()
    
    form.parent_id.choices = [(0, '无')] + [(d.id, d.name) for d in departments]
    
    if form.validate_on_submit():
        try:
            # 检查部门代码是否已存在
            existing_dept = Department.query.filter_by(
                company_id=company_id,
                code=form.code.data
            ).first()
            if existing_dept:
                flash('部门代码已存在', 'danger')
                return render_template('organization/department_form.html', 
                                    company=company,
                                    form=form)
            
            # 创建新部门，包括类型
            department = Department(
                name=form.name.data,
                code=form.code.data,
                company_id=company_id,
                parent_id=form.parent_id.data if form.parent_id.data != 0 else None,
                type=form.type.data  # 保存部门类型
            )
            
            db.session.add(department)
            db.session.commit()
            flash('部门创建成功', 'success')
            return redirect(url_for('organization.departments', company_id=company_id))
        except Exception as e:
            db.session.rollback()
            flash(f'部门创建失败: {str(e)}', 'danger')
    
    return render_template('organization/department_form.html', 
                         company=company,
                         form=form)

@bp.route('/companies/<int:company_id>/departments/<int:department_id>/users')
@login_required
def department_users(company_id, department_id):
    from utils.department_utils import get_child_departments
    
    company = Company.query.get_or_404(company_id)
    department = Department.query.get_or_404(department_id)
    
    # 基于权限的检查逻辑
    if not current_user.has_permission('department_user_manage'):
        flash('您没有权限访问部门用户管理', 'danger')
        return redirect(url_for('index'))
    
    # 部门管理员只能查看自己管理的部门
    if current_user.role.code == 'department_admin':
        # 获取管理员的所有子部门ID
        department_ids = get_child_departments(current_user.department_id)
        if department_id not in department_ids:
            flash('您只能管理自己部门及子部门下的用户', 'danger')
            return redirect(url_for('index'))
    
    # 公司管理员只能查看自己公司的部门
    if current_user.role.code != 'super_admin' and current_user.company_id != company_id:
        flash('您只能管理自己公司的部门用户', 'danger')
        return redirect(url_for('index'))
    
    # 根据用户角色和权限级别过滤用户列表
    if current_user.role.code == 'super_admin':
        # 超级管理员可以看到部门内的所有用户
        users = User.query.filter_by(department_id=department_id).all()
    elif current_user.role.code == 'company_admin':
        # 公司管理员只能看到本公司部门内的非超级管理员用户
        users = User.query.join(Role).filter(
            and_(
                User.department_id == department_id,
                User.company_id == current_user.company_id,
                Role.code != 'super_admin'
            )
        ).all()
    else:  # 部门管理员
        # 部门管理员只能看到自己管理的部门内的普通用户
        users = User.query.join(Role).filter(
            and_(
                User.department_id == department_id,
                User.company_id == current_user.company_id,
                ~Role.code.in_(['super_admin', 'company_admin', 'department_admin'])
            )
        ).all()
    
    return render_template('organization/department_users.html',
                         company=company,
                         department=department,
                         users=users)

@bp.route('/companies/<int:company_id>/delete', methods=['POST'])
@login_required
def delete_company(company_id):
    if not current_user.has_permission('company_manage'):
        flash('您没有权限删除公司', 'danger')
        return redirect(url_for('index'))
    
    company = Company.query.get_or_404(company_id)
    confirm_name = request.form.get('confirm_name', '')
    
    if confirm_name != company.name:
        flash('确认名称不匹配，删除失败', 'danger')
        return redirect(url_for('organization.companies'))
    
    try:
        # 删除公司下的所有数据
        Department.query.filter_by(company_id=company_id).delete()
        User.query.filter_by(company_id=company_id).delete()
        LeadPool.query.filter_by(company_id=company_id).delete()
        db.session.delete(company)
        db.session.commit()
        flash('公司及其所有相关数据已删除', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'删除公司失败: {str(e)}', 'danger')
    
    return redirect(url_for('organization.companies'))

@bp.route('/companies/<int:company_id>/departments/<int:department_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_department(company_id, department_id):
    company = Company.query.get_or_404(company_id)
    department = Department.query.get_or_404(department_id)
    
    if not current_user.has_permission('department_manage'):
        flash('您没有权限编辑部门', 'danger')
        return redirect(url_for('index'))
    
    form = DepartmentForm(obj=department)
    form.company_id.choices = [(company.id, company.name)]
    form.company_id.data = company.id
    
    # 获取可选的父部门列表（排除自己及其子部门）
    available_departments = Department.query.filter(
        Department.company_id == company_id,
        Department.id != department_id,
        ~Department.id.in_(get_child_departments(department_id))
    ).all()
    
    form.parent_id.choices = [(0, '无')] + [(d.id, d.name) for d in available_departments]
    
    if form.validate_on_submit():
        try:
            # 检查部门代码是否已存在（排除自己）
            existing_dept = Department.query.filter(
                Department.company_id == company_id,
                Department.code == form.code.data,
                Department.id != department_id
            ).first()
            
            if existing_dept:
                flash('部门代码已存在', 'danger')
                return render_template('organization/department_form.html', 
                                    company=company,
                                    form=form,
                                    department=department)
            
            department.name = form.name.data
            department.code = form.code.data
            department.parent_id = form.parent_id.data if form.parent_id.data != 0 else None
            department.type = form.type.data
            
            db.session.commit()
            flash('部门更新成功', 'success')
            return redirect(url_for('organization.departments', company_id=company_id))
        except Exception as e:
            db.session.rollback()
            flash(f'部门更新失败: {str(e)}', 'danger')
    
    return render_template('organization/department_form.html', 
                         company=company,
                         form=form,
                         department=department)

@bp.route('/companies/<int:company_id>/departments/<int:department_id>/delete', methods=['POST'])
@login_required
def delete_department(company_id, department_id):
    company = Company.query.get_or_404(company_id)
    department = Department.query.get_or_404(department_id)
    
    if not current_user.has_permission('department_manage'):
        flash('您没有权限删除部门', 'danger')
        return redirect(url_for('index'))
    
    confirm_name = request.form.get('confirm_name', '')
    if confirm_name.strip() != department.name.strip():
        flash('确认名称不匹配，删除失败', 'danger')
        return redirect(url_for('organization.departments', company_id=company_id))
    
    try:
        # 获取默认部门
        default_department = Department.query.filter_by(
            company_id=company_id,
            code='DEFAULT'
        ).first()
        
        if not default_department:
            # 如果默认部门不存在，创建一个
            default_department = Department(
                name='默认部门',
                code='DEFAULT',
                company_id=company_id,
                type='department'
            )
            db.session.add(default_department)
            db.session.flush()  # 获取新创建的部门ID
        
        if department.type == 'department':
            # 如果是部门，将其下的小组和用户都转移到默认部门
            Department.query.filter_by(parent_id=department_id).update({'parent_id': default_department.id})
            User.query.filter_by(department_id=department_id).update({'department_id': default_department.id})
        else:
            # 如果是小组，只需要转移用户
            User.query.filter_by(department_id=department_id).update({'department_id': default_department.id})
        
        db.session.delete(department)
        db.session.commit()
        flash(f'{"部门" if department.type == "department" else "小组"}删除成功，相关成员已转移到默认部门', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'删除失败: {str(e)}', 'danger')
    
    return redirect(url_for('organization.departments', company_id=company_id))