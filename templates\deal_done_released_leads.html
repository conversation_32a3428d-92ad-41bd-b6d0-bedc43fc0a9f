{% extends 'base.html' %}

{% block title %}我放出的签约线索{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>我放出的签约线索 <span class="badge bg-secondary">{{ total_count }}</span></h2>
        <a href="{{ url_for('dashboard') }}" class="btn btn-outline-primary btn-sm">返回仪表盘</a>
    </div>

    {% if leads %}
    <div class="card shadow-sm">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>线索ID</th>
                        <th>姓名</th>
                        <th>公司</th>
                        <th>电话</th>
                        <th>签约时间</th>
                        <th>被认领人</th>
                        <th>认领公司</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for lead in leads %}
                    <tr>
                        <td>{{ lead.id }}</td>
                        <td>{{ lead.name }}</td>
                        <td>{{ lead.company_name }}</td>
                        <td>{{ lead.phone }}</td>
                        <td>{{ lead.deal_date }}</td>
                        <td>{{ lead.claimed_by }}</td>
                        <td>{{ lead.claimed_company }}</td>
                        <td>
                            <a href="{{ url_for('leads.view_lead', lead_id=lead.id) }}" class="btn btn-sm btn-outline-primary">查看详情</a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% else %}
    <div class="alert alert-info">
        <i class="bi bi-info-circle-fill me-2"></i>您还没有放出的线索被签约。
    </div>
    {% endif %}
</div>
{% endblock %} 