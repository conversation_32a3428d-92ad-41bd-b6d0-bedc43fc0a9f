from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app as app
from flask_login import login_required, current_user
from models import db, Lead, User, Activity, LeadTransferHistory, LeadPool
from sqlalchemy import or_, and_
from datetime import datetime, timezone, timedelta
from utils.timezone_compat import get_shanghai_now
from flask_wtf.csrf import CSRFProtect

bp = Blueprint('lead_pool', __name__)

@bp.route('/')
@login_required
def index():
    """线索公海页面"""
    # 获取搜索参数
    search = request.args.get('search', '')
    page = request.args.get('page', 1, type=int)
    
    # 构建查询
    query = Lead.query.filter(Lead.is_in_public_sea == True)
    
    # 如果有搜索条件，添加过滤
    if search:
        query = query.filter(or_(
            Lead.name.ilike(f'%{search}%'),
            Lead.company_name.ilike(f'%{search}%'),
            Lead.email.ilike(f'%{search}%'),
            Lead.phone.ilike(f'%{search}%')
        ))
    
    # 分页
    pagination = query.order_by(Lead.public_sea_time.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    
    # 加密手机号码
    mask_phone_number = getattr(app, 'mask_phone_number', None)
    if mask_phone_number is None:
        # 如果找不到函数，使用默认实现
        def mask_phone_number(phone):
            if not phone or len(phone) < 7: return phone
            return phone[:3] + '*' * (len(phone) - 6) + phone[-3:]
    
    for lead in pagination.items:
        if lead.phone:
            lead.masked_phone = mask_phone_number(lead.phone)
    
    # 获取当前时间（不带时区信息，用于模板中计算）
    now = datetime.now(timezone.utc).replace(tzinfo=None)
    
    return render_template('lead_pool.html', 
                         leads=pagination.items,
                         pagination=pagination,
                         search=search,
                         now=now)

@bp.route('/move_to_pool/<int:lead_id>', methods=['POST'])
@login_required
def move_to_pool(lead_id):
    """将线索放入公海"""
    lead = Lead.query.get_or_404(lead_id)
    
    # 检查权限：只有线索的负责人或管理员可以将线索放入公海
    if not (current_user.id == lead.owner_id or 
            current_user.role.code in ['super_admin', 'company_admin', 'department_admin']):
        flash('您没有权限执行此操作', 'danger')
        return redirect(url_for('leads.leads_unified'))
    
    try:
        # 检查是否为异地线索
        is_cross_location_lead = (
            lead.owner_id == current_user.id and
            lead.current_processing_company_id == current_user.company_id and
            lead.company_id != current_user.company_id
        )

        if is_cross_location_lead:
            # 异地线索应该释放到异地池，而不是公海
            from utils.lead_utils import release_lead_to_pool
            release_lead_to_pool(lead_id, current_user)
            flash('异地线索已成功释放到异地池', 'success')
        else:
            # 普通线索释放到公海
            # 记录当前负责人
            lead.last_owner_id = lead.owner_id
            # 移除负责人
            lead.owner_id = None
            # 更新公海状态
            lead.is_in_public_sea = True
            lead.public_sea_time = get_shanghai_now()

            # 获取公海池ID
            from models.organization import LeadPool
            public_sea_pool = LeadPool.query.filter_by(
                company_id=current_user.company_id,
                is_public_sea=True
            ).first()
            if public_sea_pool:
                lead.pool_id = public_sea_pool.id

            # 添加操作记录
            activity = Activity(
                lead_id=lead.id,
                user_id=current_user.id,
                description=f'将线索放入公海'
            )
            db.session.add(activity)
            db.session.commit()

            flash('线索已成功放入公海', 'success')

        return redirect(url_for('leads.leads_unified'))
    except Exception as e:
        db.session.rollback()
        app.logger.error(f'将线索放入公海失败: {str(e)}')
        flash('操作失败，请稍后重试', 'danger')
        return redirect(url_for('leads.leads_unified'))

@bp.route('/claim/<int:lead_id>', methods=['POST'])
@login_required
def claim(lead_id):
    """认领公海中的线索"""
    lead = Lead.query.get_or_404(lead_id)
    
    # 检查线索是否在公海中
    if not lead.is_in_public_sea:
        flash('该线索不在公海中', 'danger')
        return redirect(url_for('lead_pool.index'))
    
    try:
        # 记录原公海池ID和状态
        from_pool_id = lead.pool_id
        
        # 更新线索状态
        lead.owner_id = current_user.id
        lead.company_id = current_user.company_id  # 更新公司ID
        lead.is_in_public_sea = False
        lead.public_sea_time = None
        
        # 记录转移历史
        transfer_history = LeadTransferHistory(
            lead_id=lead.id,
            from_user_id=None,
            to_user_id=current_user.id,
            from_pool_id=from_pool_id,
            to_pool_id=None,
            from_company_id=None,
            to_company_id=current_user.company_id,
            transfer_type='claim',
            created_by=current_user.id,
            notes='从公海认领线索'
        )
        db.session.add(transfer_history)
        
        # 设置保护期（50天）
        # 如果是自录入的线索，或者当前用户是原始创建者，设置保护期
        if lead.is_self_created or lead.last_owner_id == current_user.id:
            lead.protection_end_time = get_shanghai_now() + timedelta(days=50)
        
        # 添加操作记录
        activity = Activity(
            lead_id=lead.id,
            user_id=current_user.id,
            description=f'从公海认领线索'
        )
        db.session.add(activity)
        db.session.commit()
        
        flash('线索认领成功', 'success')
        return redirect(url_for('leads.leads_unified'))
    except Exception as e:
        db.session.rollback()
        app.logger.error(f'认领线索失败: {str(e)}')
        flash('操作失败，请稍后重试：' + str(e), 'danger')
        return redirect(url_for('lead_pool.index'))

@bp.route('/public-sea')
@login_required
def public_sea():
    page = request.args.get('page', 1, type=int)
    per_page = 10
    
    # 构建基础查询
    query = Lead.query.filter(
        and_(
            Lead.is_in_public_sea == True,  # 在公海中的线索
            Lead.company_id == current_user.company_id,  # 属于当前用户所在公司
            Lead.pool_id.in_(  # 在公海池中的线索
                db.session.query(LeadPool.id)
                .filter(
                    LeadPool.is_public_sea == True,
                    LeadPool.company_id == current_user.company_id
                )
            )
        )
    )
    
    # 获取总数
    total_count = query.count()
    
    # 分页
    pagination = query.order_by(Lead.public_sea_time.desc()).paginate(page=page, per_page=per_page)
    leads = pagination.items
    
    # 加密手机号码
    mask_phone_number = getattr(app, 'mask_phone_number', None)
    if mask_phone_number is None:
        # 如果找不到函数，使用默认实现
        def mask_phone_number(phone):
            if not phone or len(phone) < 7: return phone
            return phone[:3] + '*' * (len(phone) - 6) + phone[-3:]
    
    for lead in leads:
        if lead.phone:
            lead.masked_phone = mask_phone_number(lead.phone)
    
    return render_template('public_sea.html', 
                         leads=leads, 
                         pagination=pagination,
                         total_count=total_count) 