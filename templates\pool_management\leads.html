{% extends 'base.html' %}

{% block title %}{{ pool.name }} - 线索列表 - CRM系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2>{{ pool.name }}</h2>
            <p class="text-muted mb-0">{{ pool.description or '无描述' }}</p>
        </div>
        <a href="{{ url_for('pool_management.index') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> 返回线索池列表
        </a>
    </div>

    <!-- 搜索和批量操作 -->
    <div class="row mb-4">
        <div class="col-md-6">
            <form method="get" class="d-flex">
                <input type="text" 
                       class="form-control me-2" 
                       name="search" 
                       value="{{ search }}" 
                       placeholder="搜索线索...">
                <button type="submit" class="btn btn-primary">搜索</button>
            </form>
        </div>
        <div class="col-md-6 text-end">
            <button type="button" 
                    class="btn btn-success" 
                    id="assignButton"
                    {% if not pagination or not pagination.items %}disabled{% endif %}>
                <i class="bi bi-person-plus"></i> 批量分配
            </button>
        </div>
    </div>

    <!-- 线索列表 -->
    <div class="table-responsive">
        <form id="leadsForm">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" class="form-check-input" id="selectAll">
                        </th>
                        <th>名称</th>
                        <th>公司</th>
                        <th>联系方式</th>
                        <th>来源</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% if pagination %}
                    {% for lead in pagination.items %}
                    <tr>
                        <td>
                            <input type="checkbox" 
                                   class="form-check-input lead-checkbox" 
                                   name="lead_ids[]" 
                                   value="{{ lead.id }}">
                        </td>
                        <td>
                            <a href="{{ url_for('leads.view_lead', lead_id=lead.id) }}">{{ lead.name }}</a>
                        </td>
                        <td>{{ lead.company_name }}</td>
                        <td>
                            {% if lead.email %}
                            <div>{{ lead.email }}</div>
                            {% endif %}
                            {% if lead.phone %}
                            <div>{{ lead.phone }}</div>
                            {% endif %}
                        </td>
                        <td>{{ lead.source }}</td>
                        <td>{{ lead.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            <div class="btn-group">
                                <button type="button" 
                                        class="btn btn-sm btn-primary"
                                        onclick="showAssignModal([{{ lead.id }}])">
                                    <i class="bi bi-person-plus"></i> 分配
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center">暂无线索</td>
                    </tr>
                    {% endfor %}
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center">加载线索列表时出错或无数据</td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </form>
    </div>

    <!-- 分页 -->
    {% if pagination and pagination.pages > 1 %}
    <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center">
            {% for page in pagination.iter_pages() %}
                {% if page %}
                    <li class="page-item {% if page == pagination.page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('pool_management.pool_leads', pool_id=pool.id, page=page, search=search) }}">
                            {{ page }}
                        </a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                {% endif %}
            {% endfor %}
        </ul>
    </nav>
    {% endif %}
</div>

<!-- 分配线索对话框 -->
<div class="modal fade" id="assignModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">分配线索</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="{{ url_for('pool_management.assign_leads', pool_id=pool.id) }}" method="post" id="assignForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="assign_user" class="form-label">选择用户:</label>
                        <select class="form-select" id="assign_user" name="user_id" required>
                            <option value="">请选择...</option>
                            {% for user in users %}
                            <option value="{{ user.id }}">{{ user.username }} - {{ user.company.name if user.company else '无公司' }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div id="lead_ids_container"></div>
                    <div class="text-end">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">确认分配</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 全选/取消全选
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.getElementsByClassName('lead-checkbox');
        Array.from(checkboxes).forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // 显示分配对话框
    window.showAssignModal = function(leadIds) {
        // 清空之前的输入
        $('#lead_ids_container').empty();
        
        // 如果没有传递leadIds，则获取所有选中的复选框
        if (!leadIds) {
            leadIds = $('.lead-checkbox:checked').map(function() {
                return $(this).val();
            }).get();
        }
        
        if (leadIds && leadIds.length > 0) {
            // 添加选中的线索ID
            leadIds.forEach(function(leadId) {
                $('#lead_ids_container').append('<input type="hidden" name="lead_ids[]" value="' + leadId + '">');
            });
        } else {
            alert('请选择要分配的线索');
            return;
        }
        
        const assignModal = new bootstrap.Modal(document.getElementById('assignModal'));
        assignModal.show();
    };

    // 表单验证
    document.getElementById('assignForm').addEventListener('submit', function(event) {
        if (!this.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        this.classList.add('was-validated');
    });

    // 处理批量分配按钮点击
    $('#assignButton').click(function() {
        const selectedLeads = $('.lead-checkbox:checked').map(function() {
            return $(this).val();
        }).get();
        
        if (selectedLeads.length === 0) {
            alert('请至少选择一条线索');
            return;
        }
        
        showAssignModal(selectedLeads);
    });

    // 处理表单提交验证
    $('#assignForm').submit(function(e) {
        const userId = $('#assign_user').val();
        if (!userId) {
            e.preventDefault();
            alert('请选择要分配的目标用户');
        }
    });
});
</script>
{% endblock %}