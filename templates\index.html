{% extends 'base.html' %}

{% block title %}首页 - CRM系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题区域 -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">
                    <i class="fas fa-chart-line me-2"></i>销售仪表盘
                </h1>
                <p class="page-subtitle">欢迎回来，{{ current_user.name or current_user.username }}！今天也要加油哦～</p>
            </div>
            <div class="text-end">
                <div class="d-flex align-items-center gap-3">
                    <div class="text-muted">
                        <small>最后更新：<span id="lastUpdateTime"></span></small>
                    </div>
                    <button class="btn btn-theme-info" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-1"></i>刷新数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 总体概览统计卡片 -->
    <!-- 顶部统计卡片 - 参考线索管理样式 -->
    <div class="row mb-4">
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card border-primary h-100">
                <div class="card-body text-center py-3">
                    <i class="fas fa-users text-primary" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-primary mt-2 mb-1">总线索数</h6>
                    <h4 class="mb-1">{{ overview_data.total_leads }}</h4>
                    <small class="text-muted">较上月增长</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card border-success h-100">
                <div class="card-body text-center py-3">
                    <i class="fas fa-user-check text-success" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-success mt-2 mb-1">我的线索</h6>
                    <h4 class="mb-1">{{ overview_data.my_leads }}</h4>
                    <small class="text-muted">本周新增</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card border-info h-100">
                <div class="card-body text-center py-3">
                    <i class="fas fa-plus-circle text-info" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-info mt-2 mb-1">本周新增</h6>
                    <h4 class="mb-1">{{ overview_data.weekly_new }}</h4>
                    <small class="text-muted">环比上周</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card border-warning h-100">
                <div class="card-body text-center py-3">
                    <i class="fas fa-clock text-warning" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-warning mt-2 mb-1">待跟进</h6>
                    <h4 class="mb-1">{{ overview_data.pending_follow }}</h4>
                    <small class="text-muted">需要关注</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card border-primary h-100">
                <div class="card-body text-center py-3">
                    <i class="fas fa-handshake text-primary" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-primary mt-2 mb-1">整体签约率</h6>
                    <h4 class="mb-1">{{ overview_data.overall_conversion }}%</h4>
                    <small class="text-muted">{{ '表现良好' if overview_data.overall_conversion >= 20 else '需要提升' }}</small>
                </div>
            </div>
        </div>
    </div>



    <!-- 销售漏斗统计区 -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="card-title mb-0">
                    <i class="fas fa-funnel-dollar me-2"></i>销售漏斗统计
                    <small class="text-muted">（点击卡片查看对应阶段的详细线索）</small>
                </h5>
                <div class="d-flex align-items-center">
                    <small class="text-muted me-2">阶段转化率</small>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="percentageToggle" style="cursor: pointer;">
                        <label class="form-check-label" for="percentageToggle" style="cursor: pointer;">
                            <small class="text-muted">总体占比</small>
                        </label>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="card h-100" style="cursor: pointer;" data-stage="called">
                        <div class="card-body text-center py-3">
                            <i class="fas fa-phone text-info" style="font-size: 2.5rem;"></i>
                            <h6 class="card-title text-info mt-2 mb-1">已拨电话</h6>
                            <h4 class="mb-1">{{ funnel_stats.called }}</h4>
                            <small class="text-muted percentage-display"
                                   data-step-percent="{{ "%.1f"|format(funnel_stats.called / funnel_stats.total * 100 if funnel_stats.total > 0 else 0) }}"
                                   data-total-percent="{{ "%.1f"|format(funnel_stats.called / funnel_stats.total * 100 if funnel_stats.total > 0 else 0) }}">
                                {{ "%.1f"|format(funnel_stats.called / funnel_stats.total * 100 if funnel_stats.total > 0 else 0) }}%
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="card h-100" style="cursor: pointer;" data-stage="connected">
                        <div class="card-body text-center py-3">
                            <i class="fas fa-phone-volume text-warning" style="font-size: 2.5rem;"></i>
                            <h6 class="card-title text-warning mt-2 mb-1">接通电话</h6>
                            <h4 class="mb-1">{{ funnel_stats.connected }}</h4>
                            <small class="text-muted percentage-display"
                                   data-step-percent="{{ "%.1f"|format(funnel_stats.connected / funnel_stats.called * 100 if funnel_stats.called > 0 else 0) }}"
                                   data-total-percent="{{ "%.1f"|format(funnel_stats.connected / funnel_stats.total * 100 if funnel_stats.total > 0 else 0) }}">
                                {{ "%.1f"|format(funnel_stats.connected / funnel_stats.called * 100 if funnel_stats.called > 0 else 0) }}%
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="card h-100" style="cursor: pointer;" data-stage="valid_call">
                        <div class="card-body text-center py-3">
                            <i class="fas fa-check-circle text-success" style="font-size: 2.5rem;"></i>
                            <h6 class="card-title text-success mt-2 mb-1">有效通话</h6>
                            <h4 class="mb-1">{{ funnel_stats.valid_call }}</h4>
                            <small class="text-muted percentage-display"
                                   data-step-percent="{{ "%.1f"|format(funnel_stats.valid_call / funnel_stats.connected * 100 if funnel_stats.connected > 0 else 0) }}"
                                   data-total-percent="{{ "%.1f"|format(funnel_stats.valid_call / funnel_stats.total * 100 if funnel_stats.total > 0 else 0) }}">
                                {{ "%.1f"|format(funnel_stats.valid_call / funnel_stats.connected * 100 if funnel_stats.connected > 0 else 0) }}%
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="card h-100" style="cursor: pointer;" data-stage="wechat_added">
                        <div class="card-body text-center py-3">
                            <i class="fab fa-weixin text-primary" style="font-size: 2.5rem;"></i>
                            <h6 class="card-title text-primary mt-2 mb-1">添加微信</h6>
                            <h4 class="mb-1">{{ funnel_stats.wechat_added }}</h4>
                            <small class="text-muted percentage-display"
                                   data-step-percent="{{ "%.1f"|format(funnel_stats.wechat_added / funnel_stats.valid_call * 100 if funnel_stats.valid_call > 0 else 0) }}"
                                   data-total-percent="{{ "%.1f"|format(funnel_stats.wechat_added / funnel_stats.total * 100 if funnel_stats.total > 0 else 0) }}">
                                {{ "%.1f"|format(funnel_stats.wechat_added / funnel_stats.valid_call * 100 if funnel_stats.valid_call > 0 else 0) }}%
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="card h-100" style="cursor: pointer;" data-stage="intentional">
                        <div class="card-body text-center py-3">
                            <i class="fas fa-heart text-info" style="font-size: 2.5rem;"></i>
                            <h6 class="card-title text-info mt-2 mb-1">意向客户</h6>
                            <h4 class="mb-1">{{ funnel_stats.intentional }}</h4>
                            <small class="text-muted percentage-display"
                                   data-step-percent="{{ "%.1f"|format(funnel_stats.intentional / funnel_stats.wechat_added * 100 if funnel_stats.wechat_added > 0 else 0) }}"
                                   data-total-percent="{{ "%.1f"|format(funnel_stats.intentional / funnel_stats.total * 100 if funnel_stats.total > 0 else 0) }}">
                                {{ "%.1f"|format(funnel_stats.intentional / funnel_stats.wechat_added * 100 if funnel_stats.wechat_added > 0 else 0) }}%
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="card h-100" style="cursor: pointer;" data-stage="visited">
                        <div class="card-body text-center py-3">
                            <i class="fas fa-handshake text-primary" style="font-size: 2.5rem;"></i>
                            <h6 class="card-title text-primary mt-2 mb-1">确认到面</h6>
                            <h4 class="mb-1">{{ funnel_stats.visited }}</h4>
                            <small class="text-muted percentage-display"
                                   data-step-percent="{{ "%.1f"|format(funnel_stats.visited / funnel_stats.intentional * 100 if funnel_stats.intentional > 0 else 0) }}"
                                   data-total-percent="{{ "%.1f"|format(funnel_stats.visited / funnel_stats.total * 100 if funnel_stats.total > 0 else 0) }}">
                                {{ "%.1f"|format(funnel_stats.visited / funnel_stats.intentional * 100 if funnel_stats.intentional > 0 else 0) }}%
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="card h-100" style="cursor: pointer;" data-stage="compliant">
                        <div class="card-body text-center py-3">
                            <i class="fas fa-clipboard-check text-warning" style="font-size: 2.5rem;"></i>
                            <h6 class="card-title text-warning mt-2 mb-1">信息合规</h6>
                            <h4 class="mb-1">{{ funnel_stats.compliant }}</h4>
                            <small class="text-muted percentage-display"
                                   data-step-percent="{{ "%.1f"|format(funnel_stats.compliant / funnel_stats.visited * 100 if funnel_stats.visited > 0 else 0) }}"
                                   data-total-percent="{{ "%.1f"|format(funnel_stats.compliant / funnel_stats.total * 100 if funnel_stats.total > 0 else 0) }}">
                                {{ "%.1f"|format(funnel_stats.compliant / funnel_stats.visited * 100 if funnel_stats.visited > 0 else 0) }}%
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="card h-100" style="cursor: pointer;" data-stage="deal_done">
                        <div class="card-body text-center py-3">
                            <i class="fas fa-file-signature text-secondary" style="font-size: 2.5rem;"></i>
                            <h6 class="card-title text-secondary mt-2 mb-1">完成签约</h6>
                            <h4 class="mb-1">{{ funnel_stats.deal_done }}</h4>
                            <small class="text-muted percentage-display"
                                   data-step-percent="{{ "%.1f"|format(funnel_stats.deal_done / funnel_stats.compliant * 100 if funnel_stats.compliant > 0 else 0) }}"
                                   data-total-percent="{{ "%.1f"|format(funnel_stats.deal_done / funnel_stats.total * 100 if funnel_stats.total > 0 else 0) }}">
                                {{ "%.1f"|format(funnel_stats.deal_done / funnel_stats.compliant * 100 if funnel_stats.compliant > 0 else 0) }}%
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                    <div class="card h-100" style="cursor: pointer;" data-stage="car_selected">
                        <div class="card-body text-center py-3">
                            <i class="fas fa-car text-success" style="font-size: 2.5rem;"></i>
                            <h6 class="card-title text-success mt-2 mb-1">完成提车</h6>
                            <h4 class="mb-1">{{ funnel_stats.car_selected }}</h4>
                            <small class="text-muted percentage-display"
                                   data-step-percent="{{ "%.1f"|format(funnel_stats.car_selected / funnel_stats.deal_done * 100 if funnel_stats.deal_done > 0 else 0) }}"
                                   data-total-percent="{{ "%.1f"|format(funnel_stats.car_selected / funnel_stats.total * 100 if funnel_stats.total > 0 else 0) }}">
                                <i class="fas fa-trophy"></i>
                                {{ "%.1f"|format(funnel_stats.car_selected / funnel_stats.deal_done * 100 if funnel_stats.deal_done > 0 else 0) }}%
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- 筛选和排序控制区 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="filter-panel">
                <h5 class="mb-3">
                    <i class="fas fa-filter me-2"></i>数据筛选
                </h5>
                <form method="GET">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label for="company_id" class="form-label">公司筛选</label>
                            <select name="company_id" id="company_id" class="form-select">
                                <option value="">全部公司</option>
                                {% for company in filter_options.companies %}
                                <option value="{{ company.id }}" {% if company_filter == company.id %}selected{% endif %}>
                                    {{ company.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="department_id" class="form-label">部门筛选</label>
                            <select name="department_id" id="department_id" class="form-select">
                                <option value="">全部部门</option>
                                {% for department in filter_options.departments %}
                                <option value="{{ department.id }}" {% if department_filter == department.id %}selected{% endif %}>
                                    {{ department.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="sort_by" class="form-label">排序方式</label>
                            <select name="sort_by" id="sort_by" class="form-select">
                                <option value="deal_done" {% if sort_by == 'deal_done' %}selected{% endif %}>按签约数</option>
                                <option value="total_leads" {% if sort_by == 'total_leads' %}selected{% endif %}>按线索总数</option>
                                <option value="conversion_rate" {% if sort_by == 'conversion_rate' %}selected{% endif %}>按转化率</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="time_range" class="form-label">时间范围</label>
                            <select name="time_range" id="time_range" class="form-select">
                                <option value="all" {% if time_range == 'all' %}selected{% endif %}>全部时间</option>
                                <option value="week" {% if time_range == 'week' %}selected{% endif %}>最近一周</option>
                                <option value="month" {% if time_range == 'month' %}selected{% endif %}>最近一月</option>
                                <option value="quarter" {% if time_range == 'quarter' %}selected{% endif %}>最近三月</option>
                            </select>
                        </div>
                        <div class="filter-actions">
                            <button type="submit" class="btn btn-theme-primary">
                                <i class="fas fa-search me-1"></i>筛选
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="window.location.href='{{ url_for('index') }}'">
                                <i class="fas fa-undo me-1"></i>重置
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 公司统计排行榜 -->
    {% if company_stats and current_user.role.code == 'super_admin' %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-building-flag"></i> 公司统计排行榜
                        <small class="text-muted">（按{{ sort_by_name }}排序）</small>
                    </h5>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th width="4%">排名</th>
                                    <th width="12%">公司名称</th>
                                    <th width="5%">员工</th>
                                    <th width="5%">部门</th>
                                    <th width="6%">总线索</th>
                                    <th width="6%">已拨</th>
                                    <th width="6%">接通</th>
                                    <th width="6%">有效</th>
                                    <th width="6%">微信</th>
                                    <th width="6%">意向</th>
                                    <th width="6%">到面</th>
                                    <th width="6%">合规</th>
                                    <th width="6%">签约</th>
                                    <th width="6%">签约率</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for company in company_stats %}
                                <tr class="{% if company.rank <= 3 %}table-success{% elif company.rank <= 5 %}table-warning{% endif %}">
                                    <td>
                                        <span class="badge {% if company.rank == 1 %}bg-warning{% elif company.rank == 2 %}bg-secondary{% elif company.rank == 3 %}bg-info{% else %}bg-light text-dark{% endif %}">
                                            {% if company.rank == 1 %}🥇{% elif company.rank == 2 %}🥈{% elif company.rank == 3 %}🥉{% else %}{{ company.rank }}{% endif %}
                                        </span>
                                    </td>
                                    <td><strong>{{ company.company_name }}</strong></td>
                                    <td><span class="badge bg-info">{{ company.employee_count }}</span></td>
                                    <td><span class="badge bg-secondary">{{ company.department_count }}</span></td>
                                    <td>{{ company.total_leads }}</td>
                                    <td>{{ company.called }}</td>
                                    <td>{{ company.connected }}</td>
                                    <td>{{ company.valid_call }}</td>
                                    <td>{{ company.wechat_added }}</td>
                                    <td>{{ company.intentional }}</td>
                                    <td>{{ company.visited }}</td>
                                    <td>{{ company.compliant }}</td>
                                    <td><strong class="text-danger">{{ company.deal_done }}</strong></td>
                                    <td>
                                        <span class="badge {% if company.conversion_rate >= 30 %}bg-success{% elif company.conversion_rate >= 20 %}bg-warning{% else %}bg-danger{% endif %}">
                                            {{ company.conversion_rate }}%
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 分部门统计表格 -->
    {% if department_stats %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-building"></i> 部门统计排行榜
                        <small class="text-muted">（按{{ sort_by_name }}排序）</small>
                    </h5>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th width="5%">排名</th>
                                    <th width="15%">部门名称</th>
                                    <th width="10%">公司</th>
                                    <th width="8%">总线索</th>
                                    <th width="8%">已拨电话</th>
                                    <th width="8%">接通电话</th>
                                    <th width="8%">有效通话</th>
                                    <th width="8%">添加微信</th>
                                    <th width="8%">意向客户</th>
                                    <th width="8%">确认到面</th>
                                    <th width="8%">信息合规</th>
                                    <th width="8%">完成签约</th>
                                    <th width="8%">签约率</th>
                                    <th width="5%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for dept in department_stats %}
                                <tr class="{% if dept.rank <= 3 %}table-success{% elif dept.rank <= 5 %}table-warning{% endif %}">
                                    <td>
                                        <span class="badge {% if dept.rank == 1 %}bg-warning{% elif dept.rank == 2 %}bg-secondary{% elif dept.rank == 3 %}bg-info{% else %}bg-light text-dark{% endif %}">
                                            {% if dept.rank == 1 %}🥇{% elif dept.rank == 2 %}🥈{% elif dept.rank == 3 %}🥉{% else %}{{ dept.rank }}{% endif %}
                                        </span>
                                    </td>
                                    <td><strong>{{ dept.name }}</strong></td>
                                    <td><small class="text-muted">{{ dept.company_name }}</small></td>
                                    <td>{{ dept.total_leads }}</td>
                                    <td>{{ dept.called }}</td>
                                    <td>{{ dept.connected }}</td>
                                    <td>{{ dept.valid_call }}</td>
                                    <td>{{ dept.wechat_added }}</td>
                                    <td>{{ dept.intentional }}</td>
                                    <td>{{ dept.visited }}</td>
                                    <td>{{ dept.compliant }}</td>
                                    <td><strong class="text-danger">{{ dept.deal_done }}</strong></td>
                                    <td>
                                        <span class="badge {% if dept.conversion_rate >= 30 %}bg-success{% elif dept.conversion_rate >= 20 %}bg-warning{% else %}bg-danger{% endif %}">
                                            {{ dept.conversion_rate }}%
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('leads.leads_unified', department_id=dept.id) }}" class="btn btn-sm btn-theme-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 员工跟进详情列表 -->
    {% if employee_stats %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-users"></i> 员工跟进详情排行榜
                        <small class="text-muted">（按{{ sort_by_name }}排序）</small>
                    </h5>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th width="5%">排名</th>
                                    <th width="12%">员工姓名</th>
                                    <th width="10%">部门</th>
                                    <th width="8%">总线索</th>
                                    <th width="25%">跟进详情</th>
                                    <th width="8%">签约数</th>
                                    <th width="8%">转化率</th>
                                    <th width="8%">待跟进</th>
                                    <th width="10%">最后跟进</th>
                                    <th width="6%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for emp in employee_stats %}
                                <tr class="{% if emp.rank <= 3 %}table-success{% elif emp.rank <= 5 %}table-warning{% endif %}">
                                    <td>
                                        <span class="badge {% if emp.rank == 1 %}bg-warning{% elif emp.rank == 2 %}bg-secondary{% elif emp.rank == 3 %}bg-info{% else %}bg-light text-dark{% endif %}">
                                            {% if emp.rank == 1 %}🥇{% elif emp.rank == 2 %}🥈{% elif emp.rank == 3 %}🥉{% else %}{{ emp.rank }}{% endif %}
                                        </span>
                                    </td>
                                    <td><strong>{{ emp.name }}</strong></td>
                                    <td><small class="text-muted">{{ emp.department_name }}</small></td>
                                    <td>{{ emp.total_leads }}</td>
                                    <td>
                                        <div class="progress-container" style="height: 20px; background-color: #e9ecef; border-radius: 0.375rem; overflow: hidden; position: relative;">
                                            {% set total = emp.total_leads %}
                                            {% if total > 0 %}
                                                {% set called_pct = (emp.called / total * 100)|round(1) %}
                                                {% set connected_pct = (emp.connected / total * 100)|round(1) %}
                                                {% set intentional_pct = (emp.intentional / total * 100)|round(1) %}
                                                {% set deal_done_pct = (emp.deal_done / total * 100)|round(1) %}

                                                <!-- 已拨电话 -->
                                                {% if called_pct > 0 %}
                                                <div class="progress-segment" style="position: absolute; left: 0; top: 0; height: 100%; width: {{ called_pct }}%; background-color: #17a2b8; opacity: 0.7;" title="已拨电话: {{ emp.called }} ({{ called_pct }}%)"></div>
                                                {% endif %}

                                                <!-- 接通电话 -->
                                                {% if connected_pct > 0 %}
                                                <div class="progress-segment" style="position: absolute; left: 0; top: 0; height: 100%; width: {{ connected_pct }}%; background-color: #ffc107; opacity: 0.8;" title="接通电话: {{ emp.connected }} ({{ connected_pct }}%)"></div>
                                                {% endif %}

                                                <!-- 意向客户 -->
                                                {% if intentional_pct > 0 %}
                                                <div class="progress-segment" style="position: absolute; left: 0; top: 0; height: 100%; width: {{ intentional_pct }}%; background-color: #007bff; opacity: 0.9;" title="意向客户: {{ emp.intentional }} ({{ intentional_pct }}%)"></div>
                                                {% endif %}

                                                <!-- 完成签约 -->
                                                {% if deal_done_pct > 0 %}
                                                <div class="progress-segment" style="position: absolute; left: 0; top: 0; height: 100%; width: {{ deal_done_pct }}%; background-color: #28a745;" title="完成签约: {{ emp.deal_done }} ({{ deal_done_pct }}%)"></div>
                                                {% endif %}
                                            {% endif %}
                                        </div>
                                        <small class="text-muted">拨{{ emp.called }} 通{{ emp.connected }} 意{{ emp.intentional }} 签{{ emp.deal_done }}</small>
                                    </td>
                                    <td><strong class="text-danger">{{ emp.deal_done }}</strong></td>
                                    <td>
                                        <span class="badge {% if emp.conversion_rate >= 30 %}bg-success{% elif emp.conversion_rate >= 20 %}bg-warning{% else %}bg-danger{% endif %}">
                                            {{ emp.conversion_rate }}%
                                        </span>
                                    </td>
                                    <td>
                                        {% if emp.pending_leads > 0 %}
                                        <span class="badge bg-warning">{{ emp.pending_leads }}</span>
                                        {% else %}
                                        <span class="badge bg-success">0</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="{% if emp.pending_leads > 5 %}text-danger{% elif emp.pending_leads > 2 %}text-warning{% else %}text-success{% endif %}">
                                            {{ emp.last_follow_display }}
                                        </small>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('leads.leads_unified', owner_id=emp.id) }}" class="btn btn-sm btn-theme-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<style>
.alert {
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}
.alert:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.alert strong {
    font-size: 1.5rem;
    display: block;
    margin: 0.5rem 0;
}
.alert small {
    color: rgba(0,0,0,0.6);
}
.progress-container {
    position: relative;
    display: block;
}
.progress-segment {
    transition: all 0.3s ease;
}
.progress-segment:hover {
    opacity: 1 !important;
    z-index: 10;
}
.card {
    transition: all 0.3s ease;
}
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,0.1);
}
.badge {
    font-size: 0.75em;
}
.table th {
    font-size: 0.85em;
    padding: 0.5rem 0.3rem;
    white-space: nowrap;
}
.table td {
    font-size: 0.85em;
    padding: 0.5rem 0.3rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 设置最后更新时间
    const now = new Date();
    const timeString = now.getFullYear() + '-' +
                      String(now.getMonth() + 1).padStart(2, '0') + '-' +
                      String(now.getDate()).padStart(2, '0') + ' ' +
                      String(now.getHours()).padStart(2, '0') + ':' +
                      String(now.getMinutes()).padStart(2, '0');
    document.getElementById('lastUpdateTime').textContent = timeString;

    // 自动提交表单当筛选条件改变时
    const filterForm = document.querySelector('form');
    const selects = filterForm.querySelectorAll('select');

    selects.forEach(select => {
        select.addEventListener('change', function() {
            // 延迟提交，避免快速切换时多次提交
            setTimeout(() => {
                filterForm.submit();
            }, 300);
        });
    });

    // 漏斗阶段点击事件
    const funnelCards = document.querySelectorAll('[data-stage]');
    funnelCards.forEach(card => {
        card.addEventListener('click', function() {
            const stage = this.getAttribute('data-stage');

            if (stage) {
                // 构建URL参数
                const currentParams = new URLSearchParams(window.location.search);
                const leadsUrl = new URL('{{ url_for("leads.leads_unified") }}', window.location.origin);
                leadsUrl.searchParams.set('stage', stage);

                // 保留当前的公司和部门筛选
                if (currentParams.get('company_id')) {
                    leadsUrl.searchParams.set('company_id', currentParams.get('company_id'));
                }
                if (currentParams.get('department_id')) {
                    leadsUrl.searchParams.set('department_id', currentParams.get('department_id'));
                }

                // 跳转到线索列表页面
                window.open(leadsUrl.toString(), '_blank');
            }
        });
    });

    // 工具提示初始化
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 进度条工具提示
    const progressSegments = document.querySelectorAll('.progress-segment');
    progressSegments.forEach(segment => {
        if (segment.title) {
            new bootstrap.Tooltip(segment);
        }
    });

    // 百分比显示模式切换
    const percentageToggle = document.getElementById('percentageToggle');
    if (percentageToggle) {
        percentageToggle.addEventListener('change', function() {
            const mode = this.checked ? 'total' : 'step';
            updatePercentageDisplay(mode);
        });
    }

    // 定时刷新数据（可选）
    // setInterval(() => {
    //     location.reload();
    // }, 300000); // 5分钟刷新一次
});

// 更新百分比显示
function updatePercentageDisplay(mode) {
    const percentageElements = document.querySelectorAll('.percentage-display');

    percentageElements.forEach(element => {
        const stepPercent = element.getAttribute('data-step-percent');
        const totalPercent = element.getAttribute('data-total-percent');

        if (mode === 'step') {
            // 阶段转化率模式
            if (element.querySelector('.fas.fa-trophy')) {
                // 完成提车卡片保持图标
                element.innerHTML = `<i class="fas fa-trophy"></i> ${stepPercent}%`;
            } else {
                element.textContent = `${stepPercent}%`;
            }
        } else if (mode === 'total') {
            // 总体占比模式
            if (element.querySelector('.fas.fa-trophy')) {
                // 完成提车卡片保持图标
                element.innerHTML = `<i class="fas fa-trophy"></i> ${totalPercent}%`;
            } else {
                element.textContent = `${totalPercent}%`;
            }
        }
    });
}

// 导出功能
function exportData(type) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', type);
    window.open(`${window.location.pathname}?${params.toString()}`);
}

// 快速筛选功能
function quickFilter(filterType, value) {
    const form = document.querySelector('form');
    const input = form.querySelector(`[name="${filterType}"]`);
    if (input) {
        input.value = value;
        form.submit();
    }
}
</script>
{% endblock %}