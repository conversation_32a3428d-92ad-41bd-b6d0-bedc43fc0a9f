# -*- coding: utf-8 -*-
from models import Department
from flask import current_app


def get_child_departments(department_id, include_self=True):
    """递归获取部门及其所有子部门ID列表
    
    Args:
        department_id: 部门ID
        include_self: 返回结果是否包含自身
        
    Returns:
        包含所有子部门ID的列表
    """
    if not department_id:
        return []
        
    result = [department_id] if include_self else []
    
    try:
        # 获取直接子部门
        children = Department.query.filter_by(parent_id=department_id).all()
        
        # 递归获取所有子部门的ID
        for child in children:
            child_ids = get_child_departments(child.id, include_self=True)
            for id in child_ids:
                if id not in result:
                    result.append(id)
        
        current_app.logger.info(f"获取部门{department_id}的所有子部门: {result}")
        return result
    except Exception as e:
        current_app.logger.error(f"获取子部门出错: {str(e)}")
        return result


def get_department_tree(company_id=None, parent_id=None, max_depth=None, current_depth=0):
    """获取部门树形结构
    
    Args:
        company_id: 公司ID，如果指定则只返回该公司的部门
        parent_id: 父部门ID，如果指定则只返回该父部门下的子部门
        max_depth: 最大递归深度，None表示不限制
        current_depth: 当前递归深度，内部使用
        
    Returns:
        部门树形结构的列表
    """
    # 检查是否达到最大递归深度
    if max_depth is not None and current_depth >= max_depth:
        return []
    
    # 构建查询条件
    query_conditions = {}
    if company_id is not None:
        query_conditions['company_id'] = company_id
    if parent_id is not None:
        query_conditions['parent_id'] = parent_id
    else:
        # 如果未指定parent_id，则查询顶级部门
        query_conditions['parent_id'] = None
    
    # 查询部门
    departments = Department.query.filter_by(**query_conditions).all()
    
    # 构建结果
    result = []
    for dept in departments:
        # 递归获取子部门
        children = get_department_tree(
            company_id=company_id,
            parent_id=dept.id,
            max_depth=max_depth,
            current_depth=current_depth + 1
        )
        
        # 构建部门节点
        dept_node = {
            'id': dept.id,
            'name': dept.name,
            'code': dept.code,
            'type': dept.type,
            'children': children
        }
        
        result.append(dept_node)
    
    return result 