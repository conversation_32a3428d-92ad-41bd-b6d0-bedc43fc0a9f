# -*- coding: utf-8 -*-
from flask import render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from models import db, User, Company, Department, Role, Lead, Activity, LeadStatusHistory, LeadTransferHistory
from utils.auth_utils import validate_password
from werkzeug.security import generate_password_hash
from sqlalchemy import or_, and_
from forms.__init__ import UserForm

def get_user_list(page=1, per_page=20):
    """获取用户列表并渲染模板"""
    query = User.query
    
    # 权限检查 - 确保只有管理员可以查看所有用户
    if current_user.role.code == 'super_admin':
        pass
    elif current_user.has_permission('user_manage'):
        if current_user.role.code == 'company_admin':
            # 公司管理员查看本公司所有用户
            query = query.filter_by(company_id=current_user.company_id)
        elif current_user.role.code == 'department_admin':
            # 部门管理员查看本部门及子部门用户
            from utils.department_utils import get_child_departments
            department_ids = get_child_departments(current_user.department_id)
            department_ids.append(current_user.department_id) # 包括自己的部门
            query = query.filter(User.department_id.in_(department_ids))
    else:
        # 如果没有任何管理权限，普通用户只能看到自己
        query = query.filter_by(id=current_user.id)
        
    # 按创建时间倒序排序
    query = query.order_by(User.created_at.desc())
     
    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    users = pagination.items
     
    return render_template('users.html', users=users, pagination=pagination)

def handle_add_user():
    # 验证用户是否有添加用户的权限
    if not current_user.has_permission('user_manage'):
        flash('您没有权限添加用户', 'danger')
        return redirect(url_for('users'))
    
    form = UserForm()
    
    # 根据用户角色设置公司选项
    if current_user.role.code == 'super_admin':
        form.company_id.choices = [(c.id, c.name) for c in Company.query.all()]
    else:
        # 公司管理员只能添加本公司的用户
        form.company_id.choices = [(current_user.company_id, current_user.company.name)]
        form.company_id.data = current_user.company_id
    
    # 根据用户权限设置部门选项
    if current_user.role.code == 'super_admin':
        # 超级管理员可以选择所有部门
        form.department_id.choices = [(0, '无')] + [(d.id, d.name) for d in Department.query.all()]
    elif current_user.role.code == 'company_admin':
        # 公司管理员可以选择本公司的部门
        form.department_id.choices = [(0, '无')] + [(d.id, d.name) for d in Department.query.filter_by(company_id=current_user.company_id).all()]
    elif current_user.role.code == 'department_admin':
        # 部门管理员只能选择自己的部门
        form.department_id.choices = [(current_user.department_id, Department.query.get(current_user.department_id).name if current_user.department_id else '无')]
        form.department_id.data = current_user.department_id
    else:
        # 其他用户无法选择部门
        form.department_id.choices = [(0, '无')]
    
    # 根据用户权限设置角色选项
    if current_user.role.code == 'super_admin':
        # 超级管理员可以选择所有角色
        form.role_id.choices = [(r.id, r.name) for r in Role.query.all()]
    elif current_user.role.code == 'company_admin':
        # 公司管理员只能选择本公司或系统预设的非超级管理员角色
        form.role_id.choices = [(r.id, r.name) for r in Role.query.filter(
            and_(
                or_(
                    Role.company_id == current_user.company_id,
                    Role.is_system == True
                ),
                Role.code != 'super_admin'
            )
        ).all()]
    elif current_user.role.code == 'department_admin':
        # 部门管理员只能分配普通用户角色
        form.role_id.choices = [(r.id, r.name) for r in Role.query.filter(
            and_(
                Role.is_system == True,
                Role.code.in_(['employee', 'sales'])
            )
        ).all()]
    else:
        # 其他用户无法选择角色
        form.role_id.choices = []
    
    if form.validate_on_submit():
        try:
            # 检查用户是否已存在
            if User.query.filter_by(username=form.username.data).first():
                flash('用户名已存在', 'danger')
                return render_template('user_form.html', form=form, title='添加用户', action='add')
            
            # 检查邮箱是否已存在
            if User.query.filter_by(email=form.email.data).first():
                flash('邮箱已被使用', 'danger')
                return render_template('user_form.html', form=form, title='添加用户', action='add')
            
            # 检查手机号是否已存在（如果提供了手机号）
            if form.phone.data and User.query.filter_by(phone=form.phone.data).first():
                flash('手机号已被使用', 'danger')
                return render_template('user_form.html', form=form, title='添加用户', action='add')
            
            # 检查选择的角色是否为超级管理员
            selected_role = Role.query.get(form.role_id.data)
            if selected_role.code == 'super_admin' and current_user.role.code != 'super_admin':
                flash('您没有权限创建超级管理员账号', 'danger')
                return render_template('user_form.html', form=form, title='添加用户', action='add')
            
            # 创建新用户
            user = User(
                username=form.username.data,
                email=form.email.data,
                phone=form.phone.data,
                role_id=form.role_id.data,
                company_id=form.company_id.data,
                department_id=form.department_id.data if form.department_id.data != 0 else None
            )
            user.set_password(form.password.data)
            
            db.session.add(user)
            db.session.commit()
            flash('用户创建成功', 'success')
            return redirect(url_for('users'))
        except Exception as e:
            db.session.rollback()
            flash(f'用户创建失败: {str(e)}', 'danger')
            current_app.logger.error(f'添加用户失败: {e}', exc_info=True)
    
    return render_template('user_form.html', form=form, title='添加用户', action='add')

def handle_edit_user(user_id):
    # 验证用户是否有编辑用户的权限
    if not current_user.has_permission('user_manage'):
        flash('您没有权限编辑用户', 'danger')
        return redirect(url_for('users'))
    
    user = User.query.get_or_404(user_id)
    
    # 权限检查：
    # 1. 超级管理员可以编辑任何用户
    # 2. 公司管理员只能编辑本公司的用户，且不能编辑超级管理员
    # 3. 部门管理员只能编辑本部门的用户
    if current_user.role.code == 'super_admin':
        pass # 超级管理员可以编辑任何人
    elif current_user.role.code == 'company_admin':
        if user.company_id != current_user.company_id or user.role.code == 'super_admin':
            flash('您只能编辑本公司的非超级管理员用户', 'danger')
            return redirect(url_for('users'))
    elif current_user.role.code == 'department_admin':
        if user.department_id != current_user.department_id or user.role.code in ['super_admin', 'company_admin']:
            flash('您只能编辑本部门的普通用户', 'danger')
            return redirect(url_for('users'))
    else:
        # 其他有 'user_manage' 权限但不是以上角色的情况（理论上不应发生），或无权限
        flash('您没有足够的权限编辑此用户', 'danger')
        return redirect(url_for('users'))
    
    form = UserForm(obj=user)
    
    # 根据用户角色设置公司选项
    if current_user.role.code == 'super_admin':
        form.company_id.choices = [(c.id, c.name) for c in Company.query.all()]
    else:
        # 公司管理员只能编辑本公司的用户
        form.company_id.choices = [(current_user.company_id, current_user.company.name)]
        form.company_id.data = current_user.company_id
    
    # 根据用户权限设置部门选项
    if current_user.role.code == 'super_admin':
        # 超级管理员可以选择所有部门
        form.department_id.choices = [(0, '无')] + [(d.id, d.name) for d in Department.query.all()]
    elif current_user.role.code == 'company_admin':
        # 公司管理员可以选择本公司的部门
        form.department_id.choices = [(0, '无')] + [(d.id, d.name) for d in Department.query.filter_by(company_id=current_user.company_id).all()]
    elif current_user.role.code == 'department_admin':
        # 部门管理员只能选择自己的部门
        form.department_id.choices = [(current_user.department_id, Department.query.get(current_user.department_id).name if current_user.department_id else '无')]
        form.department_id.data = current_user.department_id
    else:
        # 其他用户无法选择部门
        form.department_id.choices = [(0, '无')]
    
    # 根据用户权限设置角色选项
    if current_user.role.code == 'super_admin':
        # 超级管理员可以选择所有角色
        form.role_id.choices = [(r.id, r.name) for r in Role.query.all()]
    elif current_user.role.code == 'company_admin':
        # 公司管理员只能选择本公司或系统预设的非超级管理员角色
        form.role_id.choices = [(r.id, r.name) for r in Role.query.filter(
            and_(
                or_(
                    Role.company_id == current_user.company_id,
                    Role.is_system == True
                ),
                Role.code != 'super_admin'
            )
        ).all()]
    elif current_user.role.code == 'department_admin':
        # 部门管理员只能分配普通用户角色
        form.role_id.choices = [(r.id, r.name) for r in Role.query.filter(
            and_(
                Role.is_system == True,
                Role.code.in_(['employee', 'sales'])
            )
        ).all()]
    else:
        # 其他用户无法选择角色
        form.role_id.choices = []
    
    if form.validate_on_submit():
        try:
            # 检查用户名是否已被其他用户使用
            existing_user = User.query.filter(
                User.username == form.username.data,
                User.id != user_id
            ).first()
            if existing_user:
                flash('用户名已存在', 'danger')
                return render_template('user_form.html', form=form, title='编辑用户', action='edit', user_id=user_id)
            
            # 检查手机号是否已被其他用户使用（如果提供了手机号）
            if form.phone.data:
                existing_user = User.query.filter(
                    User.phone == form.phone.data,
                    User.id != user_id
                ).first()
                if existing_user:
                    flash('手机号已被使用', 'danger')
                    return render_template('user_form.html', form=form, title='编辑用户', action='edit', user_id=user_id)
            
            # 检查选择的角色是否为超级管理员
            selected_role = Role.query.get(form.role_id.data)
            if selected_role.code == 'super_admin' and current_user.role.code != 'super_admin':
                flash('您没有权限将用户设置为超级管理员', 'danger')
                return render_template('user_form.html', form=form, title='编辑用户', action='edit', user_id=user_id)
            
            # 更新用户信息
            user.username = form.username.data
            user.email = form.email.data
            user.phone = form.phone.data
            if form.password.data:
                user.set_password(form.password.data)
            user.role_id = form.role_id.data
            user.company_id = form.company_id.data
            user.department_id = form.department_id.data if form.department_id.data != 0 else None
            
            db.session.commit()
            flash('用户更新成功', 'success')
            return redirect(url_for('users'))
        except Exception as e:
            db.session.rollback()
            flash(f'用户更新失败: {str(e)}', 'danger')
    
    return render_template('user_form.html', form=form, title='编辑用户', action='edit', user_id=user_id)

def handle_delete_user(user_id):
    """处理删除用户"""
    # 检查用户是否有删除权限
    if not current_user.has_permission('user_manage'):
        flash('您没有权限删除用户', 'danger')
        return redirect(url_for('users'))
    
    user = User.query.get_or_404(user_id)
    
    # 权限检查：
    # 1. 超级管理员可以删除任何用户（除了自己）
    # 2. 公司管理员只能删除本公司的普通用户
    # 3. 部门管理员只能删除本部门的普通用户
    if current_user.role.code == 'super_admin':
        pass # 超级管理员可以删除任何人
    elif current_user.role.code == 'company_admin':
        if user.company_id != current_user.company_id or user.role.code in ['super_admin', 'company_admin']:
            flash('您只能删除本公司的普通用户', 'danger')
            return redirect(url_for('users'))
    elif current_user.role.code == 'department_admin':
        if user.department_id != current_user.department_id or user.role.code in ['super_admin', 'company_admin', 'department_admin']:
            flash('您只能删除本部门的普通用户', 'danger')
            return redirect(url_for('users'))
    else:
        # 其他有 'user_manage' 权限但不是以上角色的情况（理论上不应发生），或无权限
        flash('您没有足够的权限删除此用户', 'danger')
        return redirect(url_for('users'))
    
    # 不能删除自己
    if user.id == current_user.id:
        flash('不能删除当前登录用户', 'danger')
        return redirect(url_for('users'))
    
    try:
        current_app.logger.info(f"开始删除用户 ID: {user_id}, 用户名: {user.username}")
        
        # 删除用户的所有线索的所有权
        leads_deleted = Lead.query.filter_by(owner_id=user_id).update({'owner_id': None})
        current_app.logger.info(f"已更新用户拥有的线索数量: {leads_deleted}")
        
        # 删除用户的所有活动记录
        activities_deleted = Activity.query.filter_by(user_id=user_id).delete()
        current_app.logger.info(f"已删除用户的活动记录数量: {activities_deleted}")
        
        # 更新线索转移历史表中的用户引用
        from_transfers = LeadTransferHistory.query.filter_by(from_user_id=user_id).update({'from_user_id': None})
        current_app.logger.info(f"已更新转移历史中的来源用户: {from_transfers}条")
        
        to_transfers = LeadTransferHistory.query.filter_by(to_user_id=user_id).update({'to_user_id': None})
        current_app.logger.info(f"已更新转移历史中的目标用户: {to_transfers}条")
        
        creator_transfers = LeadTransferHistory.query.filter_by(created_by=user_id).update({'created_by': current_user.id})
        current_app.logger.info(f"已更新转移历史中的创建者: {creator_transfers}条")
        
        # 更新状态历史中的创建者
        status_history = LeadStatusHistory.query.filter_by(created_by=user_id).update({'created_by': current_user.id})
        current_app.logger.info(f"已更新状态历史中的创建者: {status_history}条")
        
        # 删除用户本身
        db.session.delete(user)
        db.session.commit()
        
        flash('用户已成功删除', 'success')
        current_app.logger.info(f"用户 {user_id} 删除成功")
    except Exception as e:
        db.session.rollback()
        error_msg = f"删除用户 {user_id} 时出错: {str(e)}"
        current_app.logger.error(error_msg)
        flash(f'删除用户失败: {str(e)}', 'danger')
    
    return redirect(url_for('users'))

def handle_profile():
    """处理个人资料"""
    if request.method == 'POST':
        try:
            # 获取表单数据
            username = request.form.get('username', '').strip()
            email = request.form.get('email', '').strip()
            phone = request.form.get('phone', '').strip()
            password = request.form.get('password', '').strip()

            # 基本验证
            if not username:
                flash('用户名不能为空', 'danger')
                return render_template('profile.html')

            if not email:
                flash('邮箱不能为空', 'danger')
                return render_template('profile.html')

            # 验证邮箱格式
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, email):
                flash('邮箱格式不正确', 'danger')
                return render_template('profile.html')

            # 验证手机号格式（如果提供）
            if phone and not re.match(r'^1[3-9]\d{9}$', phone):
                flash('手机号格式不正确', 'danger')
                return render_template('profile.html')

            # 检查用户名是否已被其他用户使用
            if username != current_user.username:
                existing_user = User.query.filter(
                    User.username == username,
                    User.id != current_user.id
                ).first()
                if existing_user:
                    flash('用户名已被其他用户使用', 'danger')
                    return render_template('profile.html')

            # 检查邮箱是否已被其他用户使用
            if email != current_user.email:
                existing_user = User.query.filter(
                    User.email == email,
                    User.id != current_user.id
                ).first()
                if existing_user:
                    flash('邮箱已被其他用户使用', 'danger')
                    return render_template('profile.html')

            # 检查手机号是否已被其他用户使用
            if phone and phone != current_user.phone:
                existing_user = User.query.filter(
                    User.phone == phone,
                    User.id != current_user.id
                ).first()
                if existing_user:
                    flash('手机号已被其他用户使用', 'danger')
                    return render_template('profile.html')

            # 开始更新用户信息
            current_user.username = username
            current_user.email = email
            current_user.phone = phone if phone else None

            # 更新时间戳会自动处理（onupdate=get_shanghai_now）

            # 如果提供了新密码，则更新密码
            if password:
                if len(password) < 6:
                    flash('密码长度至少6位', 'danger')
                    return render_template('profile.html')

                # 简化的密码验证 - 只检查长度，不强制复杂度
                if len(password) > 50:
                    flash('密码长度不能超过50位', 'danger')
                    return render_template('profile.html')

                current_user.set_password(password)

            # 提交到数据库
            db.session.commit()

            # 成功消息
            flash('个人资料更新成功！', 'success')
            return redirect(url_for('profile'))

        except Exception as e:
            # 回滚事务
            db.session.rollback()
            current_app.logger.error(f'更新个人资料失败: {str(e)}', exc_info=True)
            flash('保存失败，请稍后重试', 'danger')
            return render_template('profile.html')

    # GET请求，显示表单
    return render_template('profile.html')

# 步骤 14: 获取公司管理员的辅助函数
def get_company_admins(company_id):
    """获取指定公司的所有公司管理员用户对象"""
    # 假设管理员角色代码为 'company_admin' 和 'super_admin' (超级管理员也视为公司管理员)
    # 如果超级管理员不应接收公司特定通知，则移除 'super_admin'
    admin_role_codes = ['company_admin', 'super_admin'] 
    admin_roles = Role.query.filter(Role.code.in_(admin_role_codes)).all()
    admin_role_ids = [role.id for role in admin_roles]

    if not admin_role_ids:
        current_app.logger.warning(f"系统中未找到角色代码为 {admin_role_codes} 的角色")
        return []

    admins = User.query.filter(
        User.company_id == company_id, 
        User.role_id.in_(admin_role_ids),
        User.status == 'active' # 只查找活动用户
    ).all()
    
    return admins 

@login_required
def add_user():
    """处理添加新用户的页面和逻辑"""
    # 权限检查：只有公司管理员或以上才能添加用户
    if not (current_user.has_permission('user_manage')):
        flash('您没有权限添加用户', 'danger')
        return redirect(url_for('users.users'))
    
    form = UserForm()
    
    # 动态设置部门选择
    if current_user.role.code == 'super_admin':
        form.department_id.choices = [(0, '无')] + [(d.id, d.name) for d in Department.query.all()]
    elif current_user.role.code == 'company_admin':
        form.department_id.choices = [(0, '无')] + [(d.id, d.name) for d in Department.query.filter_by(company_id=current_user.company_id).all()]
    elif current_user.role.code == 'department_admin':
        form.department_id.choices = [(current_user.department_id, Department.query.get(current_user.department_id).name if current_user.department_id else '无')]
        form.department_id.data = current_user.department_id
    else:
        form.department_id.choices = [(0, '无')]
    
    if form.validate_on_submit():
        try:
            # 检查用户是否已存在
            if User.query.filter_by(username=form.username.data).first():
                flash('用户名已存在', 'danger')
                return render_template('user_form.html', form=form)
            
            # 检查邮箱是否已存在
            if User.query.filter_by(email=form.email.data).first():
                flash('邮箱已被使用', 'danger')
                return render_template('user_form.html', form=form)
            
            # 检查手机号是否已存在（如果提供了手机号）
            if form.phone.data and User.query.filter_by(phone=form.phone.data).first():
                flash('手机号已被使用', 'danger')
                return render_template('user_form.html', form=form)
            
            # 检查选择的角色是否为超级管理员
            selected_role = Role.query.get(form.role_id.data)
            if selected_role.code == 'super_admin' and current_user.role.code != 'super_admin':
                flash('您没有权限创建超级管理员账号', 'danger')
                return render_template('user_form.html', form=form)
            
            # 创建新用户
            user = User(
                username=form.username.data,
                email=form.email.data,
                phone=form.phone.data,
                role_id=form.role_id.data,
                company_id=form.company_id.data,
                department_id=form.department_id.data if form.department_id.data != 0 else None
            )
            user.set_password(form.password.data)
            
            db.session.add(user)
            db.session.commit()
            flash('用户创建成功', 'success')
            return redirect(url_for('users'))
        except Exception as e:
            db.session.rollback()
            flash(f'添加用户失败: {str(e)}', 'danger')
            current_app.logger.error(f'添加用户失败: {e}', exc_info=True)
    
    return render_template('user_form.html', form=form) 