#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查当前系统实际使用的数据库和数据
"""

import os
import sys
import sqlite3

def check_current_database():
    """检查当前系统实际使用的数据库"""
    try:
        print("🔍 检查当前系统实际使用的数据库")
        print("="*60)
        
        # 1. 检查配置文件中的数据库设置
        print("📋 数据库配置:")
        from config import Config
        config = Config()
        print(f"   SQLALCHEMY_DATABASE_URI: {config.SQLALCHEMY_DATABASE_URI}")
        
        # 2. 检查当前目录下的数据库文件
        print(f"\n📁 当前目录下的数据库文件:")
        db_files = []
        for file in os.listdir('.'):
            if file.endswith('.db'):
                size = os.path.getsize(file)
                modified = os.path.getmtime(file)
                import datetime
                mod_time = datetime.datetime.fromtimestamp(modified).strftime('%Y-%m-%d %H:%M:%S')
                db_files.append((file, size, mod_time))
                print(f"   {file}: {size} 字节, 修改时间: {mod_time}")
        
        # 3. 通过应用检查实际使用的数据库
        print(f"\n🔧 通过应用检查实际数据:")
        from app import create_app
        from config import Config
        from models import db, Lead, User, Company
        
        app = create_app(Config)
        
        with app.app_context():
            # 检查数据库连接信息
            print(f"   实际数据库URL: {db.engine.url}")
            
            # 检查线索数据
            total_leads = Lead.query.count()
            print(f"   总线索数: {total_leads}")
            
            # 检查具体的线索
            leads = Lead.query.limit(10).all()
            print(f"\n📊 前10条线索:")
            for i, lead in enumerate(leads, 1):
                print(f"   {i}. {lead.name} - {lead.phone} - 创建时间: {lead.created_at}")
            
            # 检查测试线索
            test_leads = Lead.query.filter(Lead.name.like('测试客户%')).all()
            print(f"\n🧪 测试线索 (名称包含'测试客户'):")
            print(f"   数量: {len(test_leads)}")
            for lead in test_leads[:5]:  # 只显示前5条
                print(f"   - {lead.name}: 已拨电话={lead.is_called}, 接通={lead.is_connected}")
            
            # 检查用户数据
            users = User.query.all()
            print(f"\n👥 用户数据:")
            for user in users:
                user_leads = Lead.query.filter_by(owner_id=user.id).count()
                print(f"   {user.username} ({user.name}): {user_leads} 条线索")
            
            # 检查公司数据
            companies = Company.query.all()
            print(f"\n🏢 公司数据:")
            for company in companies:
                company_leads = Lead.query.filter_by(company_id=company.id).count()
                print(f"   {company.name}: {company_leads} 条线索")
        
        # 4. 直接检查SQLite文件
        print(f"\n💾 直接检查SQLite文件:")
        if os.path.exists('crm.db'):
            conn = sqlite3.connect('crm.db')
            cursor = conn.cursor()
            
            # 检查线索表
            cursor.execute("SELECT COUNT(*) FROM lead")
            sqlite_lead_count = cursor.fetchone()[0]
            print(f"   SQLite中的线索数: {sqlite_lead_count}")
            
            # 检查最近的线索
            cursor.execute("SELECT name, phone, created_at FROM lead ORDER BY created_at DESC LIMIT 5")
            recent_leads = cursor.fetchall()
            print(f"   最近的5条线索:")
            for lead in recent_leads:
                print(f"     - {lead[0]}: {lead[1]} ({lead[2]})")
            
            # 检查测试线索
            cursor.execute("SELECT COUNT(*) FROM lead WHERE name LIKE '测试客户%'")
            sqlite_test_count = cursor.fetchone()[0]
            print(f"   SQLite中的测试线索数: {sqlite_test_count}")
            
            conn.close()
        else:
            print("   ❌ crm.db 文件不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_homepage_data_flow():
    """检查首页数据流"""
    try:
        print(f"\n🏠 检查首页数据流")
        print("="*50)
        
        from app import create_app
        from config import Config
        from models import db, User
        from flask_login import login_user
        
        app = create_app(Config)
        
        with app.app_context():
            # 获取admin用户
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                print("❌ 未找到admin用户")
                return False
            
            print(f"✅ 找到admin用户: {admin_user.username}")
            
            # 模拟登录并测试数据获取
            with app.test_request_context():
                login_user(admin_user)
                
                # 测试各个数据获取函数
                from utils.homepage_utils import get_overview_data, get_funnel_stats
                
                print(f"\n📊 测试数据获取函数:")
                
                # 测试概览数据
                try:
                    overview_data = get_overview_data()
                    print(f"   概览数据: {overview_data}")
                except Exception as e:
                    print(f"   ❌ 概览数据获取失败: {e}")
                
                # 测试漏斗数据
                try:
                    funnel_stats = get_funnel_stats()
                    print(f"   漏斗数据: {funnel_stats}")
                except Exception as e:
                    print(f"   ❌ 漏斗数据获取失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_minimal_test_data():
    """创建最小测试数据"""
    try:
        print(f"\n🔧 创建最小测试数据")
        print("="*50)
        
        from app import create_app
        from config import Config
        from models import db, Lead, User, Company, LeadPool
        from datetime import datetime
        
        app = create_app(Config)
        
        with app.app_context():
            # 获取基础数据
            admin_user = User.query.filter_by(username='admin').first()
            company = Company.query.first()
            
            if not admin_user:
                print("❌ 未找到admin用户")
                return False
            
            if not company:
                print("❌ 未找到公司")
                return False
            
            print(f"✅ 使用用户: {admin_user.username}")
            print(f"✅ 使用公司: {company.name}")
            
            # 获取或创建线索池
            lead_pool = LeadPool.query.filter_by(company_id=company.id).first()
            if not lead_pool:
                lead_pool = LeadPool(
                    name=f'{company.name}线索池',
                    company_id=company.id,
                    description='测试线索池'
                )
                db.session.add(lead_pool)
                db.session.commit()
                print(f"✅ 创建线索池: {lead_pool.name}")
            
            # 删除现有的最小测试数据
            Lead.query.filter(Lead.name.like('最小测试%')).delete()
            db.session.commit()
            
            # 创建5条最小测试数据，确保有明显的转化漏斗
            test_data = [
                {'name': '最小测试01', 'stages': ['called', 'connected', 'valid_call', 'wechat_added', 'intentional', 'visited', 'compliant', 'deal_done', 'car_selected']},
                {'name': '最小测试02', 'stages': ['called', 'connected', 'valid_call', 'wechat_added', 'intentional']},
                {'name': '最小测试03', 'stages': ['called', 'connected', 'valid_call']},
                {'name': '最小测试04', 'stages': ['called']},
                {'name': '最小测试05', 'stages': []},
            ]
            
            for data in test_data:
                lead = Lead(
                    name=data['name'],
                    company_name='测试公司',
                    email=f'{data["name"].lower()}@test.com',
                    phone=f'*********{data["name"][-2:]}',
                    notes=f'{data["name"]}的备注',
                    status='new',
                    owner_id=admin_user.id,
                    company_id=company.id,
                    original_company_id=company.id,
                    pool_id=lead_pool.id,
                    is_self_created=True,
                    created_at=datetime.now()
                )
                
                # 设置阶段状态
                if 'called' in data['stages']:
                    lead.is_called = True
                if 'connected' in data['stages']:
                    lead.is_connected = True
                if 'valid_call' in data['stages']:
                    lead.is_valid_call = True
                if 'wechat_added' in data['stages']:
                    lead.is_wechat_added = True
                if 'intentional' in data['stages']:
                    lead.is_intentional = True
                if 'visited' in data['stages']:
                    lead.is_visited = True
                if 'compliant' in data['stages']:
                    lead.is_compliant = True
                if 'deal_done' in data['stages']:
                    lead.is_deal_done = True
                if 'car_selected' in data['stages']:
                    lead.is_car_selected = True
                
                db.session.add(lead)
            
            db.session.commit()
            
            # 验证创建结果
            created_leads = Lead.query.filter(Lead.name.like('最小测试%')).all()
            print(f"✅ 创建了 {len(created_leads)} 条最小测试线索")
            
            # 显示各阶段统计
            base_query = Lead.query.filter(Lead.name.like('最小测试%'))
            stages = {
                'total': base_query.count(),
                'called': base_query.filter(Lead.is_called == True).count(),
                'connected': base_query.filter(Lead.is_connected == True).count(),
                'valid_call': base_query.filter(Lead.is_valid_call == True).count(),
                'wechat_added': base_query.filter(Lead.is_wechat_added == True).count(),
                'intentional': base_query.filter(Lead.is_intentional == True).count(),
                'visited': base_query.filter(Lead.is_visited == True).count(),
                'compliant': base_query.filter(Lead.is_compliant == True).count(),
                'deal_done': base_query.filter(Lead.is_deal_done == True).count(),
                'car_selected': base_query.filter(Lead.is_car_selected == True).count()
            }
            
            print(f"📊 各阶段统计:")
            for stage, count in stages.items():
                print(f"   {stage}: {count}")
            
            return True
            
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    # 检查当前数据库
    check_current_database()
    
    # 检查首页数据流
    check_homepage_data_flow()
    
    # 询问是否创建最小测试数据
    print("\n" + "="*60)
    choice = input("是否创建最小测试数据？(y/N): ").lower().strip()
    if choice in ['y', 'yes']:
        create_minimal_test_data()
        print("\n🎯 请刷新首页查看数据")
