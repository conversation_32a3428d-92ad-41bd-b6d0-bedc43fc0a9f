{% extends 'base.html' %}

{% block title %}用户管理 - CRM系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>用户管理</h1>
    <div>
        <a href="{{ url_for('add_user') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> 添加用户
        </a>
    </div>
</div>

<!-- 用户列表 -->
<div class="card shadow">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>用户名</th>
                        <th>邮箱</th>
                        <th>手机号</th>
                        <th>所属公司</th>
                        <th>所属部门</th>
                        <th>角色</th>
                        <th>创建时间</th>
                        <th>线索数量</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.username }}</td>
                        <td>{{ user.email }}</td>
                        <td>{{ user.phone or '-' }}</td>
                        <td>{{ user.company.name if user.company else '-' }}</td>
                        <td>{{ user.department.name if user.department else '-' }}</td>
                        <td>
                            {% if user.role.code == 'super_admin' %}
                            <span class="badge bg-danger">超级管理员</span>
                            <div class="small text-muted mt-1">
                                <i class="bi bi-check-circle-fill text-success"></i> 系统管理
                                <i class="bi bi-check-circle-fill text-success"></i> 公司管理
                                <i class="bi bi-check-circle-fill text-success"></i> 用户管理
                                <i class="bi bi-check-circle-fill text-success"></i> 线索管理
                            </div>
                            {% elif user.role.code == 'company_admin' %}
                            <span class="badge bg-danger">公司管理员</span>
                            <div class="small text-muted mt-1">
                                <i class="bi bi-check-circle-fill text-success"></i> 公司管理
                                <i class="bi bi-check-circle-fill text-success"></i> 部门管理
                                <i class="bi bi-check-circle-fill text-success"></i> 用户管理
                                <i class="bi bi-check-circle-fill text-success"></i> 线索管理
                            </div>
                            {% elif user.role.code == 'department_admin' %}
                            <span class="badge bg-primary">部门管理员</span>
                            <div class="small text-muted mt-1">
                                <i class="bi bi-check-circle-fill text-success"></i> 部门线索查看
                                <i class="bi bi-check-circle-fill text-success"></i> 部门用户管理
                                <i class="bi bi-check-circle-fill text-success"></i> 线索分配
                            </div>
                            {% elif user.role.code == 'sales' %}
                            <span class="badge bg-success">销售</span>
                            <div class="small text-muted mt-1">
                                <i class="bi bi-check-circle-fill text-success"></i> 个人线索管理
                                <i class="bi bi-check-circle-fill text-success"></i> 线索跟进
                            </div>
                            {% else %}
                            <span class="badge bg-info">{{ user.role.name }}</span>
                            <div class="small text-muted mt-1">
                                <i class="bi bi-check-circle-fill text-success"></i> {{ user.role.description or '自定义角色' }}
                            </div>
                            {% endif %}
                        </td>
                        <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>{{ user.leads|length }}</td>
                        <td>
                            <a href="{{ url_for('edit_user', user_id=user.id) }}" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-pencil-square"></i> 编辑
                            </a>
                            {% if current_user.id != user.id %}
                                <form action="{{ url_for('delete_user', user_id=user.id) }}" method="post" class="d-inline ms-1" onsubmit="return confirm('确定要删除此用户吗？此操作不可撤销。')">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <button type="submit" class="btn btn-outline-danger btn-sm">
                                        <i class="bi bi-trash"></i> 删除
                                    </button>
                                </form>
                            {% endif %}
                            {% if user.role.code in ['super_admin', 'company_admin', 'department_admin'] %}
                                <span class="badge bg-secondary ms-1">系统用户</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

{% if pagination.pages > 1 %}
<nav class="mt-4">
    <ul class="pagination justify-content-center">
        {% if pagination.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('users', page=pagination.prev_num) }}">&laquo; 上一页</a>
        </li>
        {% else %}
        <li class="page-item disabled">
            <span class="page-link">&laquo; 上一页</span>
        </li>
        {% endif %}
        
        {% for page in pagination.iter_pages() %}
            {% if page %}
                {% if page != pagination.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('users', page=page) }}">{{ page }}</a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page }}</span>
                </li>
                {% endif %}
            {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            {% endif %}
        {% endfor %}
        
        {% if pagination.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('users', page=pagination.next_num) }}">下一页 &raquo;</a>
        </li>
        {% else %}
        <li class="page-item disabled">
            <span class="page-link">下一页 &raquo;</span>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% endblock %}