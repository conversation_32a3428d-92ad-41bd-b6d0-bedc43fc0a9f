{% extends 'base.html' %}

{% block title %}公司管理 - CRM系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>公司管理</h1>
    <div>
        <a href="{{ url_for('organization.add_company') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> 添加公司
        </a>
    </div>
</div>

<!-- 公司列表 -->
<div class="card shadow">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>公司名称</th>
                        <th>公司代码</th>
                        <th>类型</th>
                        <th>上级公司</th>
                        <th>部门数量</th>
                        <th>用户数量</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for company in companies %}
                    <tr>
                        <td>{{ company.name }}</td>
                        <td>{{ company.code }}</td>
                        <td>
                            {% if company.type == 'headquarters' %}
                            <span class="badge bg-primary">总部</span>
                            {% else %}
                            <span class="badge bg-info">子公司</span>
                            {% endif %}
                        </td>
                        <td>{{ company.parent.name if company.parent else '-' }}</td>
                        <td>{{ company.departments|length }}</td>
                        <td>{{ company.users|length }}</td>
                        <td>{{ company.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ url_for('organization.departments', company_id=company.id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-diagram-3"></i> 部门管理
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteCompanyModal{{ company.id }}">
                                    <i class="bi bi-trash"></i> 删除
                                </button>
                            </div>
                        </td>
                    </tr>
                    
                    <!-- 删除确认模态框 -->
                    <div class="modal fade" id="deleteCompanyModal{{ company.id }}" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">确认删除公司</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <p>您确定要删除公司 "{{ company.name }}" 吗？</p>
                                    <p class="text-danger">
                                        <i class="bi bi-exclamation-triangle"></i> 
                                        警告：删除公司将同时删除该公司下的所有部门、小组、用户和业务数据！此操作不可恢复！
                                    </p>
                                    <form action="{{ url_for('organization.delete_company', company_id=company.id) }}" method="post">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />
                                        <div class="mb-3">
                                            <label for="confirm_name{{ company.id }}" class="form-label">请输入公司名称以确认删除</label>
                                            <input type="text" class="form-control" id="confirm_name{{ company.id }}" name="confirm_name" required 
                                                   placeholder="请输入: {{ company.name }}" />
                                        </div>
                                        <div class="d-grid gap-2">
                                            <button type="submit" class="btn btn-danger">确认删除</button>
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %} 