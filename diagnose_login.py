#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录问题诊断脚本
"""

from app import create_app
from config import Config
from models import db, User, Role
from werkzeug.security import check_password_hash, generate_password_hash

def diagnose_login():
    app = create_app(Config)
    with app.app_context():
        print("🔍 CRM系统登录诊断")
        print("="*60)
        
        # 1. 检查数据库连接
        try:
            user_count = User.query.count()
            print(f"✅ 数据库连接正常，共有 {user_count} 个用户")
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return
        
        # 2. 检查admin用户
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            print("❌ Admin用户不存在，正在创建...")
            # 创建admin用户
            super_admin_role = Role.query.filter_by(code='super_admin').first()
            if super_admin_role:
                admin_user = User(
                    username='admin',
                    name='系统管理员',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('admin123'),
                    role_id=super_admin_role.id,
                    status='active'
                )
                db.session.add(admin_user)
                db.session.commit()
                print("✅ Admin用户已创建")
            else:
                print("❌ 超级管理员角色不存在")
                return
        
        # 3. 详细检查admin用户信息
        print(f"\n📋 Admin用户详情:")
        print(f"   ID: {admin_user.id}")
        print(f"   用户名: {admin_user.username}")
        print(f"   姓名: {admin_user.name}")
        print(f"   邮箱: {admin_user.email}")
        print(f"   状态: {admin_user.status}")
        print(f"   是否活跃: {admin_user.is_active}")
        print(f"   角色ID: {admin_user.role_id}")
        print(f"   角色: {admin_user.role.name if admin_user.role else '无角色'}")
        print(f"   公司ID: {admin_user.company_id}")
        print(f"   部门ID: {admin_user.department_id}")
        
        # 4. 测试密码验证
        print(f"\n🔐 密码验证测试:")
        test_password = 'admin123'
        if check_password_hash(admin_user.password_hash, test_password):
            print(f"✅ 密码 '{test_password}' 验证成功")
        else:
            print(f"❌ 密码 '{test_password}' 验证失败")
            # 重置密码
            print("🔧 正在重置密码...")
            admin_user.password_hash = generate_password_hash(test_password)
            db.session.commit()
            print(f"✅ 密码已重置为: {test_password}")
        
        # 5. 检查角色权限
        if admin_user.role:
            print(f"\n🛡️ 角色权限检查:")
            print(f"   角色代码: {admin_user.role.code}")
            print(f"   权限数量: {len(admin_user.role.permissions)}")
            if admin_user.role.permissions:
                print("   权限列表:")
                for perm in admin_user.role.permissions[:5]:  # 只显示前5个
                    print(f"     - {perm.name} ({perm.code})")
                if len(admin_user.role.permissions) > 5:
                    print(f"     ... 还有 {len(admin_user.role.permissions) - 5} 个权限")
        
        # 6. 检查Flask-Login相关
        print(f"\n🔑 Flask-Login检查:")
        print(f"   get_id(): {admin_user.get_id()}")
        print(f"   is_authenticated: {admin_user.is_authenticated}")
        print(f"   is_active: {admin_user.is_active}")
        print(f"   is_anonymous: {admin_user.is_anonymous}")
        
        # 7. 生成登录建议
        print(f"\n💡 登录建议:")
        print(f"   🌐 访问地址: http://localhost:5000/login")
        print(f"   👤 用户名: admin")
        print(f"   🔑 密码: admin123")
        print(f"   📧 或使用邮箱: <EMAIL>")
        
        # 8. 检查可能的问题
        print(f"\n⚠️ 可能的问题检查:")
        issues = []
        
        if admin_user.status != 'active':
            issues.append(f"用户状态不是active: {admin_user.status}")
        
        if not admin_user.role:
            issues.append("用户没有分配角色")
        elif admin_user.role.code != 'super_admin':
            issues.append(f"用户角色不是超级管理员: {admin_user.role.code}")
        
        if not admin_user.is_active:
            issues.append("用户is_active属性为False")
        
        if issues:
            for issue in issues:
                print(f"   ❌ {issue}")
        else:
            print("   ✅ 未发现明显问题")
        
        print("\n" + "="*60)
        print("🎯 诊断完成！")
        
        if not issues:
            print("✅ Admin用户配置正常，应该可以正常登录")
            print("如果仍然无法登录，请检查:")
            print("   1. 浏览器是否启用了JavaScript")
            print("   2. 是否有网络连接问题")
            print("   3. 服务器是否正常运行")
            print("   4. 是否有防火墙阻止")
        else:
            print("⚠️ 发现问题，请根据上述提示进行修复")

if __name__ == '__main__':
    diagnose_login()
