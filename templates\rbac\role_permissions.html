{% extends 'base.html' %}

{% block title %}角色权限分配 - CRM系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题区域 -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">
                    <i class="fas fa-cogs me-2"></i>角色权限分配
                </h1>
                <p class="page-subtitle">批量配置系统角色的权限分配，控制不同角色的功能访问权限</p>
            </div>
            <div class="text-end">
                <div class="btn-group-theme">
                    <a href="{{ url_for('rbac.roles') }}" class="btn btn-outline-primary">
                        <i class="fas fa-user-shield me-1"></i>角色管理
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-primary">{{ roles|length }}</h3>
                    <p class="mb-0">角色数量</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-success">{{ permissions|length }}</h3>
                    <p class="mb-0">权限数量</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    {% set total_assignments = role_permissions_map.values()|map('length')|sum %}
                    <h3 class="text-info">{{ total_assignments }}</h3>
                    <p class="mb-0">权限分配数</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    {% set avg_permissions = (total_assignments / roles|length)|round(1) if roles|length > 0 else 0 %}
                    <h3 class="text-warning">{{ avg_permissions }}</h3>
                    <p class="mb-0">平均权限数</p>
                </div>
            </div>
        </div>
    </div>

    {% if roles and permissions %}
    <!-- 权限分配表格 -->
    <div class="card shadow">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>权限分配矩阵</h5>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="selectAllPermissions()">
                        <i class="fas fa-check-square me-1"></i>全选
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearAllPermissions()">
                        <i class="fas fa-square me-1"></i>清空
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <form action="{{ url_for('rbac.update_role_permissions') }}" method="POST" data-skip-validation="true">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th width="25%">权限名称</th>
                                <th width="30%">权限描述</th>
                                {% for role in roles %}
                                <th class="text-center" width="{{ (45 / roles|length)|round(0) }}%">
                                    <div>
                                        <strong>{{ role[1] }}</strong>
                                        <br><small class="text-muted">{{ role[2] }}</small>
                                    </div>
                                </th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for permission in permissions %}
                            <tr>
                                <td>
                                    <strong>{{ permission[1] }}</strong>
                                    <br><small class="text-muted">{{ permission[2] }}</small>
                                </td>
                                <td>{{ permission[3] or '无描述' }}</td>
                                {% for role in roles %}
                                <td class="text-center">
                                    {% set role_id = role[0] %}
                                    {% set permission_id = permission[0] %}
                                    {% set is_checked = permission_id in role_permissions_map.get(role_id, []) %}
                                    {% set is_super_admin = role[2] == 'super_admin' %}
                                    
                                    <input type="checkbox" 
                                           name="permissions[{{ role_id }}][]" 
                                           value="{{ permission_id }}"
                                           {% if is_super_admin or is_checked %}checked{% endif %}
                                           {% if is_super_admin %}disabled{% endif %}
                                           class="form-check-input permission-checkbox"
                                           data-role="{{ role_id }}"
                                           data-permission="{{ permission_id }}">
                                </td>
                                {% endfor %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <div class="text-end mt-3">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-2"></i>保存权限设置
                    </button>
                </div>
            </form>
        </div>
    </div>

    {% else %}
    <!-- 无数据提示 -->
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h5 class="text-muted">数据加载问题</h5>
            <p class="text-muted">无法加载角色或权限数据。请检查：</p>
            <ul class="list-unstyled text-muted">
                <li>数据库连接是否正常</li>
                <li>角色和权限数据是否存在</li>
                <li>当前用户是否有访问权限</li>
            </ul>
            <div class="mt-3">
                <p><strong>角色数量：</strong>{{ roles|length if roles else 0 }}</p>
                <p><strong>权限数量：</strong>{{ permissions|length if permissions else 0 }}</p>
            </div>
            <div class="mt-4">
                <a href="{{ url_for('rbac.roles') }}" class="btn btn-primary me-2">
                    <i class="fas fa-user-shield me-1"></i>角色管理
                </a>
                <a href="{{ url_for('rbac.permissions') }}" class="btn btn-info">
                    <i class="fas fa-shield-alt me-1"></i>权限管理
                </a>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 操作说明 -->
    <div class="mt-4">
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>操作说明</h6>
            <ul class="mb-0">
                <li><strong>超级管理员：</strong>自动拥有所有权限，复选框为禁用状态</li>
                <li><strong>权限分配：</strong>勾选复选框为角色分配对应权限</li>
                <li><strong>批量操作：</strong>使用"全选"和"清空"按钮快速操作</li>
                <li><strong>保存设置：</strong>修改后点击"保存权限设置"按钮生效</li>
            </ul>
        </div>
    </div>
</div>

<script>
function selectAllPermissions() {
    const checkboxes = document.querySelectorAll('.permission-checkbox:not([disabled])');
    checkboxes.forEach(checkbox => checkbox.checked = true);
}

function clearAllPermissions() {
    const checkboxes = document.querySelectorAll('.permission-checkbox:not([disabled])');
    checkboxes.forEach(checkbox => checkbox.checked = false);
}

// 角色列权限操作
function selectRolePermissions(roleId) {
    const checkboxes = document.querySelectorAll(`.permission-checkbox[data-role="${roleId}"]:not([disabled])`);
    checkboxes.forEach(checkbox => checkbox.checked = true);
}

function clearRolePermissions(roleId) {
    const checkboxes = document.querySelectorAll(`.permission-checkbox[data-role="${roleId}"]:not([disabled])`);
    checkboxes.forEach(checkbox => checkbox.checked = false);
}
</script>
{% endblock %}
