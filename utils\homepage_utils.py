#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
首页数据处理工具
"""

from datetime import datetime, timedelta
from sqlalchemy import func, and_, or_
from models import db, Lead, User, Department, Company, Activity, Role
from flask_login import current_user
from utils.department_utils import get_child_departments

def get_time_filter(time_range):
    """根据时间范围获取过滤条件"""
    now = datetime.now()
    
    if time_range == 'week':
        start_date = now - timedelta(days=7)
    elif time_range == 'month':
        start_date = now - timedelta(days=30)
    elif time_range == 'quarter':
        start_date = now - timedelta(days=90)
    else:
        start_date = None
    
    return start_date

def get_base_query(company_filter=None, department_filter=None, time_range='all'):
    """获取基础查询对象"""
    base_query = Lead.query
    
    # 权限过滤
    if current_user and current_user.role and current_user.role.code != 'super_admin':
        if current_user.role.code == 'company_admin':
            base_query = base_query.filter(Lead.company_id == current_user.company_id)
        elif current_user.role.code == 'department_admin':
            dept_ids = get_child_departments(current_user.department_id) + [current_user.department_id]
            dept_user_ids = [u.id for u in User.query.filter(User.department_id.in_(dept_ids)).all()]
            base_query = base_query.filter(Lead.owner_id.in_(dept_user_ids))
        else:
            # 普通用户只能看到本部门数据
            dept_user_ids = [u.id for u in User.query.filter_by(department_id=current_user.department_id).all()]
            base_query = base_query.filter(Lead.owner_id.in_(dept_user_ids))
    
    # 公司筛选
    if company_filter:
        base_query = base_query.filter(Lead.company_id == company_filter)
    
    # 部门筛选
    if department_filter:
        dept_ids = get_child_departments(department_filter) + [department_filter]
        dept_user_ids = [u.id for u in User.query.filter(User.department_id.in_(dept_ids)).all()]
        base_query = base_query.filter(Lead.owner_id.in_(dept_user_ids))
    
    # 时间筛选
    start_date = get_time_filter(time_range)
    if start_date:
        base_query = base_query.filter(Lead.created_at >= start_date)
    
    return base_query

def get_overview_data(company_filter=None, department_filter=None, time_range='all'):
    """获取总体概览数据"""
    base_query = get_base_query(company_filter, department_filter, time_range)
    
    # 计算统计数据
    total_leads = base_query.count()
    my_leads = base_query.filter(Lead.owner_id == current_user.id).count()
    
    # 本周新增
    week_start = datetime.now() - timedelta(days=7)
    weekly_new = base_query.filter(Lead.created_at >= week_start).count()
    
    # 待跟进（超过3天未有活动的线索）
    three_days_ago = datetime.now() - timedelta(days=3)
    recent_activity_lead_ids = db.session.query(Activity.lead_id).filter(
        Activity.created_at >= three_days_ago
    ).distinct().subquery()
    
    pending_follow = base_query.filter(
        ~Lead.id.in_(recent_activity_lead_ids)
    ).count()
    
    # 签约率
    signed_leads = base_query.filter(Lead.is_deal_done == True).count()
    overall_conversion = (signed_leads / total_leads * 100) if total_leads > 0 else 0
    
    # 平均转化率（各阶段平均）
    stages = ['is_called', 'is_connected', 'is_valid_call', 'is_wechat_added', 
              'is_intentional', 'is_visited', 'is_compliant', 'is_deal_done']
    
    stage_counts = []
    prev_count = total_leads
    
    for stage in stages:
        count = base_query.filter(getattr(Lead, stage) == True).count()
        if prev_count > 0:
            stage_counts.append(count / prev_count * 100)
        prev_count = count
    
    avg_conversion = sum(stage_counts) / len(stage_counts) if stage_counts else 0
    
    return {
        'total_leads': total_leads,
        'my_leads': my_leads,
        'weekly_new': weekly_new,
        'pending_follow': pending_follow,
        'overall_conversion': round(overall_conversion, 1),
        'avg_conversion': round(avg_conversion, 1)
    }

def get_funnel_stats(company_filter=None, department_filter=None, time_range='all'):
    """获取漏斗统计数据"""
    base_query = get_base_query(company_filter, department_filter, time_range)
    
    # 获取各阶段统计
    total = base_query.count()
    
    funnel_data = {
        'total': total,
        'called': base_query.filter(Lead.is_called == True).count(),
        'connected': base_query.filter(Lead.is_connected == True).count(),
        'valid_call': base_query.filter(Lead.is_valid_call == True).count(),
        'wechat_added': base_query.filter(Lead.is_wechat_added == True).count(),
        'intentional': base_query.filter(Lead.is_intentional == True).count(),
        'visited': base_query.filter(Lead.is_visited == True).count(),
        'compliant': base_query.filter(Lead.is_compliant == True).count(),
        'deal_done': base_query.filter(Lead.is_deal_done == True).count(),
        'car_selected': base_query.filter(Lead.is_car_selected == True).count()
    }
    
    return funnel_data

def get_department_stats(company_filter=None, sort_by='deal_done', sort_order='desc'):
    """获取部门统计数据"""
    # 确定查询范围
    if current_user.role.code == 'super_admin':
        if company_filter:
            companies = Company.query.filter_by(id=company_filter).all()
        else:
            companies = Company.query.filter(Company.code != 'DEFAULT').all()
    else:
        companies = [current_user.company]
    
    department_stats = []
    
    for company in companies:
        # 获取公司的顶级部门
        departments = Department.query.filter_by(company_id=company.id, parent_id=None).all()
        
        for dept in departments:
            # 获取部门及其子部门的所有用户
            dept_ids = get_child_departments(dept.id) + [dept.id]
            dept_users = User.query.filter(User.department_id.in_(dept_ids)).all()
            dept_user_ids = [u.id for u in dept_users]
            
            if not dept_user_ids:
                continue
            
            # 构建部门线索查询
            dept_leads = Lead.query.filter(Lead.owner_id.in_(dept_user_ids))
            
            # 计算各项统计
            total_leads = dept_leads.count()
            if total_leads == 0:
                continue
            
            stats = {
                'id': dept.id,
                'name': dept.name,
                'company_name': company.name,
                'total_leads': total_leads,
                'called': dept_leads.filter(Lead.is_called == True).count(),
                'connected': dept_leads.filter(Lead.is_connected == True).count(),
                'valid_call': dept_leads.filter(Lead.is_valid_call == True).count(),
                'wechat_added': dept_leads.filter(Lead.is_wechat_added == True).count(),
                'intentional': dept_leads.filter(Lead.is_intentional == True).count(),
                'visited': dept_leads.filter(Lead.is_visited == True).count(),
                'compliant': dept_leads.filter(Lead.is_compliant == True).count(),
                'deal_done': dept_leads.filter(Lead.is_deal_done == True).count(),
                'car_selected': dept_leads.filter(Lead.is_car_selected == True).count(),
                'user_count': len(dept_users)
            }
            
            # 计算转化率
            stats['conversion_rate'] = round(stats['deal_done'] / total_leads * 100, 1) if total_leads > 0 else 0
            
            department_stats.append(stats)
    
    # 排序
    reverse = sort_order == 'desc'
    if sort_by in ['deal_done', 'total_leads', 'conversion_rate']:
        department_stats.sort(key=lambda x: x[sort_by], reverse=reverse)
    
    # 添加排名
    for i, dept in enumerate(department_stats, 1):
        dept['rank'] = i
    
    return department_stats

def get_employee_stats(company_filter=None, department_filter=None, sort_by='deal_done', sort_order='desc'):
    """获取员工统计数据"""
    # 构建用户查询
    user_query = User.query
    
    # 权限过滤
    if current_user.role.code == 'super_admin':
        if company_filter:
            user_query = user_query.filter(User.company_id == company_filter)
    elif current_user.role.code == 'company_admin':
        user_query = user_query.filter(User.company_id == current_user.company_id)
    elif current_user.role.code == 'department_admin':
        dept_ids = get_child_departments(current_user.department_id) + [current_user.department_id]
        user_query = user_query.filter(User.department_id.in_(dept_ids))
    else:
        # 普通用户只能看到本部门的员工
        user_query = user_query.filter(User.department_id == current_user.department_id)
    
    # 部门筛选
    if department_filter:
        dept_ids = get_child_departments(department_filter) + [department_filter]
        user_query = user_query.filter(User.department_id.in_(dept_ids))

    # 排除管理员（不参与业绩排名）
    # 1. 排除角色级别的管理员
    user_query = user_query.join(Role).filter(
        ~Role.code.in_(['super_admin', 'company_admin', 'department_admin'])
    )

    # 2. 排除被设置为公司负责人的员工
    company_manager_ids = db.session.query(Company.manager_id).filter(Company.manager_id.isnot(None)).subquery()
    user_query = user_query.filter(~User.id.in_(company_manager_ids))

    # 3. 排除被设置为部门负责人的员工
    department_manager_ids = db.session.query(Department.manager_id).filter(Department.manager_id.isnot(None)).subquery()
    user_query = user_query.filter(~User.id.in_(department_manager_ids))

    # 只获取有线索的用户
    users = user_query.join(Lead, User.id == Lead.owner_id).distinct().all()
    
    employee_stats = []
    
    for user in users:
        # 获取用户的线索统计
        user_leads = Lead.query.filter(Lead.owner_id == user.id)
        
        total_leads = user_leads.count()
        if total_leads == 0:
            continue
        
        # 获取最近跟进时间
        last_activity = Activity.query.filter_by(user_id=user.id).order_by(Activity.created_at.desc()).first()
        last_follow_time = last_activity.created_at if last_activity else None
        
        # 计算待跟进线索数
        three_days_ago = datetime.now() - timedelta(days=3)
        recent_activity_lead_ids = db.session.query(Activity.lead_id).filter(
            Activity.user_id == user.id,
            Activity.created_at >= three_days_ago
        ).distinct().subquery()
        
        pending_leads = user_leads.filter(
            ~Lead.id.in_(recent_activity_lead_ids)
        ).count()
        
        stats = {
            'id': user.id,
            'name': user.name or user.username,
            'username': user.username,
            'department_name': user.department.name if user.department else '',
            'company_name': user.company.name if user.company else '',
            'total_leads': total_leads,
            'called': user_leads.filter(Lead.is_called == True).count(),
            'connected': user_leads.filter(Lead.is_connected == True).count(),
            'valid_call': user_leads.filter(Lead.is_valid_call == True).count(),
            'wechat_added': user_leads.filter(Lead.is_wechat_added == True).count(),
            'intentional': user_leads.filter(Lead.is_intentional == True).count(),
            'visited': user_leads.filter(Lead.is_visited == True).count(),
            'compliant': user_leads.filter(Lead.is_compliant == True).count(),
            'deal_done': user_leads.filter(Lead.is_deal_done == True).count(),
            'car_selected': user_leads.filter(Lead.is_car_selected == True).count(),
            'pending_leads': pending_leads,
            'last_follow_time': last_follow_time
        }
        
        # 计算转化率
        stats['conversion_rate'] = round(stats['deal_done'] / total_leads * 100, 1) if total_leads > 0 else 0
        
        # 格式化最后跟进时间
        if last_follow_time:
            time_diff = datetime.now() - last_follow_time
            if time_diff.days > 0:
                stats['last_follow_display'] = "{}天前".format(time_diff.days)
            elif time_diff.seconds > 3600:
                stats['last_follow_display'] = "{}小时前".format(time_diff.seconds // 3600)
            else:
                stats['last_follow_display'] = "{}分钟前".format(time_diff.seconds // 60)
        else:
            stats['last_follow_display'] = "无记录"
        
        employee_stats.append(stats)
    
    # 排序
    reverse = sort_order == 'desc'
    if sort_by in ['deal_done', 'total_leads', 'conversion_rate']:
        employee_stats.sort(key=lambda x: x[sort_by], reverse=reverse)
    
    # 添加排名
    for i, emp in enumerate(employee_stats, 1):
        emp['rank'] = i
    
    return employee_stats

def get_company_stats(sort_by='deal_done', sort_order='desc'):
    """获取公司统计数据"""
    # 只有超级管理员可以查看所有公司的排名
    if current_user.role.code != 'super_admin':
        return []

    # 获取所有公司（排除默认公司）
    companies = Company.query.filter(Company.code != 'DEFAULT').all()

    company_stats = []
    for company in companies:
        # 获取该公司的所有线索
        company_leads = Lead.query.filter_by(company_id=company.id)

        # 计算统计数据
        total_leads = company_leads.count()
        deal_done = company_leads.filter(Lead.is_deal_done == True).count()
        called = company_leads.filter(Lead.is_called == True).count()
        connected = company_leads.filter(Lead.is_connected == True).count()
        valid_call = company_leads.filter(Lead.is_valid_call == True).count()
        wechat_added = company_leads.filter(Lead.is_wechat_added == True).count()
        intentional = company_leads.filter(Lead.is_intentional == True).count()
        visited = company_leads.filter(Lead.is_visited == True).count()
        compliant = company_leads.filter(Lead.is_compliant == True).count()
        car_selected = company_leads.filter(Lead.is_car_selected == True).count()

        # 计算转化率
        conversion_rate = (deal_done / total_leads * 100) if total_leads > 0 else 0

        # 获取公司员工数量
        employee_count = User.query.filter_by(company_id=company.id).count()

        # 获取公司部门数量
        department_count = Department.query.filter_by(company_id=company.id).count()

        stats = {
            'company_id': company.id,
            'company_name': company.name,
            'company_code': company.code,
            'total_leads': total_leads,
            'deal_done': deal_done,
            'called': called,
            'connected': connected,
            'valid_call': valid_call,
            'wechat_added': wechat_added,
            'intentional': intentional,
            'visited': visited,
            'compliant': compliant,
            'car_selected': car_selected,
            'conversion_rate': round(conversion_rate, 1),
            'employee_count': employee_count,
            'department_count': department_count
        }

        company_stats.append(stats)

    # 排序
    reverse = sort_order == 'desc'
    if sort_by in ['deal_done', 'total_leads', 'conversion_rate', 'employee_count', 'department_count']:
        company_stats.sort(key=lambda x: x[sort_by], reverse=reverse)

    # 添加排名
    for i, company in enumerate(company_stats, 1):
        company['rank'] = i

    return company_stats

def get_filter_options():
    """获取筛选选项"""
    # 获取公司列表
    if current_user.role.code == 'super_admin':
        companies = Company.query.filter(Company.code != 'DEFAULT').all()
    else:
        companies = [current_user.company]

    # 获取部门列表
    if current_user.role.code == 'super_admin':
        departments = Department.query.join(Company).filter(Company.code != 'DEFAULT').all()
    elif current_user.role.code == 'company_admin':
        departments = Department.query.filter_by(company_id=current_user.company_id).all()
    elif current_user.role.code == 'department_admin':
        dept_ids = get_child_departments(current_user.department_id) + [current_user.department_id]
        departments = Department.query.filter(Department.id.in_(dept_ids)).all()
    else:
        departments = [current_user.department]

    return {
        'companies': companies,
        'departments': departments
    }
