{% extends 'base.html' %}

{% block title %}权限管理 - CRM系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题区域 -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">
                    <i class="fas fa-shield-alt me-2"></i>权限管理
                </h1>
                <p class="page-subtitle">查看系统中所有权限的详细信息和使用情况</p>
            </div>
            <div class="text-end">
                <div class="btn-group-theme">
                    <a href="{{ url_for('rbac.roles') }}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-user-shield me-1"></i>角色管理
                    </a>
                    <a href="{{ url_for('rbac.role_permissions') }}" class="btn btn-outline-warning">
                        <i class="fas fa-cogs me-1"></i>权限分配
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-primary">{{ permissions|length }}</h3>
                    <p class="mb-0">总权限数</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    {% set company_perms = [] %}
                    {% for perm in permissions %}
                        {% if 'company' in perm.code %}
                            {% set _ = company_perms.append(perm) %}
                        {% endif %}
                    {% endfor %}
                    <h3 class="text-success">{{ company_perms|length }}</h3>
                    <p class="mb-0">公司管理权限</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    {% set user_perms = [] %}
                    {% for perm in permissions %}
                        {% if 'user' in perm.code %}
                            {% set _ = user_perms.append(perm) %}
                        {% endif %}
                    {% endfor %}
                    <h3 class="text-info">{{ user_perms|length }}</h3>
                    <p class="mb-0">用户管理权限</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    {% set lead_perms = [] %}
                    {% for perm in permissions %}
                        {% if 'lead' in perm.code %}
                            {% set _ = lead_perms.append(perm) %}
                        {% endif %}
                    {% endfor %}
                    <h3 class="text-warning">{{ lead_perms|length }}</h3>
                    <p class="mb-0">线索管理权限</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 权限列表 -->
    <div class="card shadow">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-list me-2"></i>权限列表</h5>
        </div>
        <div class="card-body">
            {% if permissions %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th width="8%">序号</th>
                            <th width="20%">权限名称</th>
                            <th width="20%">权限代码</th>
                            <th width="35%">权限描述</th>
                            <th width="10%">使用角色数</th>
                            <th width="7%">分类</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for permission in permissions %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>
                                <strong>{{ permission.name }}</strong>
                            </td>
                            <td>
                                <code class="text-primary">{{ permission.code }}</code>
                            </td>
                            <td>{{ permission.description or '无描述' }}</td>
                            <td>
                                <span class="badge bg-info">{{ permission.role_count }}</span>
                            </td>
                            <td>
                                {% if 'company' in permission.code %}
                                    <span class="badge bg-success">公司</span>
                                {% elif 'department' in permission.code %}
                                    <span class="badge bg-warning">部门</span>
                                {% elif 'user' in permission.code %}
                                    <span class="badge bg-info">用户</span>
                                {% elif 'lead' in permission.code %}
                                    <span class="badge bg-primary">线索</span>
                                {% elif 'organization' in permission.code %}
                                    <span class="badge bg-secondary">组织</span>
                                {% else %}
                                    <span class="badge bg-light text-dark">其他</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-shield-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无权限数据</h5>
                <p class="text-muted">系统中还没有定义任何权限。</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 权限分类详情 -->
    {% if permissions %}
    <div class="mt-4">
        <h5><i class="fas fa-tags me-2"></i>权限分类</h5>
        <div class="row">
            <!-- 公司管理权限 -->
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-building me-2"></i>公司管理权限
                        </h6>
                    </div>
                    <div class="card-body">
                        {% set company_permissions = [] %}
                        {% for perm in permissions %}
                            {% if 'company' in perm.code %}
                                {% set _ = company_permissions.append(perm) %}
                            {% endif %}
                        {% endfor %}
                        {% if company_permissions %}
                            {% for perm in company_permissions %}
                            <div class="mb-2">
                                <strong>{{ perm.name }}</strong>
                                <br><small class="text-muted">{{ perm.description }}</small>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted mb-0">暂无公司管理相关权限</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- 用户管理权限 -->
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-users me-2"></i>用户管理权限
                        </h6>
                    </div>
                    <div class="card-body">
                        {% set user_permissions = [] %}
                        {% for perm in permissions %}
                            {% if 'user' in perm.code %}
                                {% set _ = user_permissions.append(perm) %}
                            {% endif %}
                        {% endfor %}
                        {% if user_permissions %}
                            {% for perm in user_permissions %}
                            <div class="mb-2">
                                <strong>{{ perm.name }}</strong>
                                <br><small class="text-muted">{{ perm.description }}</small>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted mb-0">暂无用户管理相关权限</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- 线索管理权限 -->
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-user-tie me-2"></i>线索管理权限
                        </h6>
                    </div>
                    <div class="card-body">
                        {% set lead_permissions = permissions|selectattr('code', 'search', 'lead')|list %}
                        {% if lead_permissions %}
                            {% for perm in lead_permissions %}
                            <div class="mb-2">
                                <strong>{{ perm.name }}</strong>
                                <br><small class="text-muted">{{ perm.description }}</small>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted mb-0">暂无线索管理相关权限</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- 其他权限 -->
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-cog me-2"></i>其他权限
                        </h6>
                    </div>
                    <div class="card-body">
                        {% set other_permissions = [] %}
                        {% for perm in permissions %}
                            {% if not ('company' in perm.code or 'user' in perm.code or 'lead' in perm.code) %}
                                {% set _ = other_permissions.append(perm) %}
                            {% endif %}
                        {% endfor %}
                        {% if other_permissions %}
                            {% for perm in other_permissions %}
                            <div class="mb-2">
                                <strong>{{ perm.name }}</strong>
                                <br><small class="text-muted">{{ perm.description }}</small>
                            </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted mb-0">暂无其他权限</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 操作提示 -->
    <div class="mt-4">
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>权限说明</h6>
            <ul class="mb-0">
                <li><strong>权限代码：</strong>系统内部使用的唯一标识符，不能重复</li>
                <li><strong>使用角色数：</strong>当前拥有该权限的角色数量</li>
                <li><strong>权限分配：</strong>可在"权限分配"页面为角色批量分配权限</li>
                <li><strong>系统权限：</strong>由系统预定义，不能删除或修改</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}
