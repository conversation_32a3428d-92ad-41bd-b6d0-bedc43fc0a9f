{% extends 'base.html' %}
{% from '_formhelpers.html' import render_field %}

{% block title %}邮件服务配置 - 管理后台{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题区域 -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">
                    <i class="fas fa-envelope-open-text me-2"></i>邮件配置
                </h1>
                <p class="page-subtitle">配置系统邮件服务，包括SMTP服务器设置和邮件发送测试</p>
            </div>
            <div class="text-end">
                <div class="btn-group-theme">
                    <button type="button" class="btn btn-theme-info" id="testEmailBtn">
                        <i class="fas fa-paper-plane me-1"></i>测试发送
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card">
        <div class="card-body">
            <p class="text-muted">在此配置用于发送系统邮件（如通知）的SMTP服务器信息。</p>

            <form method="POST" action="{{ url_for('admin_settings.configure_email') }}" data-skip-validation="true">
                {{ form.hidden_tag() }} {# Includes CSRF token #}
                
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label">{{ form.mail_server.label }}</label>
                    <div class="col-sm-9">
                        {{ form.mail_server(class='form-control', placeholder='例如: smtp.qq.com') }}
                        {% if form.mail_server.errors %}
                            <div class="invalid-feedback d-block">{{ form.mail_server.errors[0] }}</div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label">{{ form.mail_port.label }}</label>
                    <div class="col-sm-9">
                        {{ form.mail_port(class='form-control', placeholder='例如: 465 或 587') }}
                         {% if form.mail_port.errors %}
                            <div class="invalid-feedback d-block">{{ form.mail_port.errors[0] }}</div>
                        {% endif %}
                    </div>
                </div>
                
                 <div class="row mb-3">
                    <label class="col-sm-3 col-form-label">{{ form.mail_username.label }}</label>
                    <div class="col-sm-9">
                        {{ form.mail_username(class='form-control', placeholder='您的邮箱登录名') }}
                         {% if form.mail_username.errors %}
                            <div class="invalid-feedback d-block">{{ form.mail_username.errors[0] }}</div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label">{{ form.mail_password.label }}</label>
                    <div class="col-sm-9">
                        {{ form.mail_password(class='form-control', placeholder='留空则不修改当前密码') }}
                        <small class="form-text text-muted">{{ form.mail_password.description }} 许多邮箱服务（如QQ、163）需要使用授权码而非登录密码。</small>
                         {% if form.mail_password.errors %}
                            <div class="invalid-feedback d-block">{{ form.mail_password.errors[0] }}</div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <label class="col-sm-3 col-form-label">加密方式</label>
                    <div class="col-sm-9">
                        <div class="form-check form-check-inline">
                            {{ form.mail_use_tls(class='form-check-input') }}
                            {{ form.mail_use_tls.label(class='form-check-label') }}
                        </div>
                         <div class="form-check form-check-inline">
                            {{ form.mail_use_ssl(class='form-check-input') }}
                            {{ form.mail_use_ssl.label(class='form-check-label') }}
                        </div>
                         {% if form.mail_use_tls.errors %}
                            <div class="invalid-feedback d-block">{{ form.mail_use_tls.errors[0] }}</div>
                        {% endif %}
                         {% if form.mail_use_ssl.errors %}
                            <div class="invalid-feedback d-block">{{ form.mail_use_ssl.errors[0] }}</div>
                        {% endif %}
                        <small class="form-text text-muted d-block">
                            注意：QQ邮箱通常使用 SSL+465端口 或 TLS+587端口。不要同时勾选SSL和TLS。
                        </small>
                    </div>
                </div>
                
                 <div class="row mb-3">
                    <label class="col-sm-3 col-form-label">{{ form.mail_default_sender.label }}</label>
                    <div class="col-sm-9">
                        {{ form.mail_default_sender(class='form-control', placeholder='邮件中显示的发件人地址') }}
                        <small class="form-text text-muted">通常与邮箱用户名相同，或您有权限使用的地址。</small>
                         {% if form.mail_default_sender.errors %}
                            <div class="invalid-feedback d-block">{{ form.mail_default_sender.errors[0] }}</div>
                        {% endif %}
                    </div>
                </div>

                <div class="row">
                    <div class="col-sm-9 offset-sm-3">
                        {{ form.submit(class='btn btn-theme-primary') }}
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 测试结果显示区域 -->
    <div class="card mt-4" id="testResultCard" style="display: none;">
        <div class="card-header" id="testResultHeader">
            <h5 class="mb-0">
                <i class="bi bi-envelope-check"></i> 邮件测试结果
            </h5>
        </div>
        <div class="card-body">
            <div id="testResultContent"></div>
        </div>
    </div>
</div>

<!-- 测试邮件模态框 -->
<div class="modal fade" id="testEmailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-envelope-check"></i> 发送测试邮件
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="testEmailForm" data-skip-validation="true">
                    <div class="mb-3">
                        <label for="testEmailAddress" class="form-label">收件人邮箱地址</label>
                        <input type="email" class="form-control" id="testEmailAddress"
                               placeholder="请输入有效的邮箱地址" required>
                        <div class="form-text">测试邮件将使用当前页面的配置发送</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-theme-info" id="sendTestEmailBtn">
                    <i class="bi bi-send"></i> 发送测试邮件
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 测试邮件按钮点击事件
    $('#testEmailBtn').click(function() {
        $('#testEmailModal').modal('show');
    });

    // 发送测试邮件
    $('#sendTestEmailBtn').click(function() {
        const email = $('#testEmailAddress').val();
        if (!email) {
            alert('请输入收件人邮箱地址');
            return;
        }

        // 获取当前表单的配置
        const formData = {
            recipient_email: email,
            mail_server: $('#mail_server').val(),
            mail_port: $('#mail_port').val(),
            mail_username: $('#mail_username').val(),
            mail_password: $('#mail_password').val(),
            mail_use_tls: $('#mail_use_tls').is(':checked'),
            mail_use_ssl: $('#mail_use_ssl').is(':checked'),
            mail_default_sender: $('#mail_default_sender').val(),
            csrf_token: $('input[name="csrf_token"]').val()
        };

        // 显示加载状态
        const $btn = $('#sendTestEmailBtn');
        const originalText = $btn.html();
        $btn.html('<span class="spinner-border spinner-border-sm me-2"></span>发送中...').prop('disabled', true);

        // 发送AJAX请求
        $.post('{{ url_for("admin_settings.test_email_send") }}', formData)
            .done(function(response) {
                showTestResult(response);
                $('#testEmailModal').modal('hide');
            })
            .fail(function(xhr) {
                let errorMsg = '发送失败';
                try {
                    const response = JSON.parse(xhr.responseText);
                    errorMsg = response.message || errorMsg;
                } catch (e) {
                    errorMsg = '网络错误或服务器异常';
                }
                showTestResult({
                    success: false,
                    message: errorMsg,
                    details: ['请检查网络连接和服务器状态']
                });
                $('#testEmailModal').modal('hide');
            })
            .always(function() {
                $btn.html(originalText).prop('disabled', false);
            });
    });

    // 显示测试结果
    function showTestResult(result) {
        const $card = $('#testResultCard');
        const $header = $('#testResultHeader');
        const $content = $('#testResultContent');

        // 设置卡片样式
        $card.removeClass('border-success border-danger');
        if (result.success) {
            $card.addClass('border-success');
            $header.removeClass('bg-danger text-white').addClass('bg-success text-white');
        } else {
            $card.addClass('border-danger');
            $header.removeClass('bg-success text-white').addClass('bg-danger text-white');
        }

        // 设置内容
        let html = `
            <div class="alert ${result.success ? 'alert-success' : 'alert-danger'}">
                <h6><i class="bi bi-${result.success ? 'check-circle' : 'exclamation-triangle'}"></i> ${result.message}</h6>
            </div>
        `;

        if (result.details && result.details.length > 0) {
            html += '<h6>详细信息：</h6>';
            html += '<div class="bg-light p-3 border rounded" style="max-height: 300px; overflow-y: auto;">';
            html += '<pre class="mb-0">' + result.details.join('\n') + '</pre>';
            html += '</div>';
        }

        if (!result.success) {
            html += `
                <div class="alert alert-info mt-3">
                    <h6>常见问题解决方案：</h6>
                    <ul class="mb-0">
                        <li>确认SMTP服务器地址和端口号是否正确</li>
                        <li>确认为QQ邮箱使用的是授权码而非登录密码</li>
                        <li>检查网络环境是否限制了端口连接 (465/587)</li>
                        <li>QQ邮箱通常使用 SSL+465端口组合 或 TLS+587端口组合</li>
                        <li>确认邮箱账号没有被封禁或限制</li>
                    </ul>
                </div>
            `;
        }

        $content.html(html);
        $card.show();

        // 滚动到结果区域
        $('html, body').animate({
            scrollTop: $card.offset().top - 20
        }, 500);
    }

    // 回车键发送测试邮件
    $('#testEmailAddress').keypress(function(e) {
        if (e.which === 13) {
            $('#sendTestEmailBtn').click();
        }
    });
});
</script>
{% endblock %}