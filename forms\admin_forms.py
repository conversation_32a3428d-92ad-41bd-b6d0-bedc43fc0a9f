from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, IntegerField, PasswordField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Optional, NumberRange, Email

class EmailSettingsForm(FlaskForm):
    mail_server = StringField('SMTP 服务器地址', validators=[DataRequired()])
    mail_port = IntegerField('端口', validators=[DataRequired(), NumberRange(min=1, max=65535)])
    mail_username = StringField('邮箱用户名', validators=[DataRequired(), Email()])
    mail_password = PasswordField('密码/授权码', 
                                validators=[Optional()], 
                                description='仅在需要更新密码时填写')
    mail_use_tls = BooleanField('使用 TLS')
    mail_use_ssl = BooleanField('使用 SSL')
    mail_default_sender = StringField('默认发件人邮箱', validators=[DataRequired(), Email()])
    submit = SubmitField('保存配置') 