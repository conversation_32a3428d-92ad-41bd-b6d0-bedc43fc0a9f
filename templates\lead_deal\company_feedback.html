{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <h2>线索签约反馈</h2>
    
    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">总线索数</h5>
                    <h2 class="card-text" id="total-leads">-</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">签约数</h5>
                    <h2 class="card-text" id="dealt-leads">-</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">签约率</h5>
                    <h2 class="card-text"><span id="deal-rate">-</span>%</h2>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 最近签约记录 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">最近签约记录</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table" id="recent-deals-table">
                    <thead>
                        <tr>
                            <th>线索名称</th>
                            <th>签约状态</th>
                            <th>签约时间</th>
                            <th>签约金额</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- 所有释放线索列表 -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">所有释放线索</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>线索名称</th>
                            <th>公司名称</th>
                            <th>当前所属公司</th>
                            <th>签约状态</th>
                            <th>签约反馈时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for lead in leads %}
                        <tr>
                            <td>{{ lead.name }}</td>
                            <td>{{ lead.company_name }}</td>
                            <td>{{ lead.current_company.name }}</td>
                            <td>
                                {% if lead.deal_status == 'won' %}
                                <span class="badge bg-success">签约</span>
                                {% elif lead.deal_status == 'lost' %}
                                <span class="badge bg-danger">未签约</span>
                                {% else %}
                                <span class="badge bg-warning">待定</span>
                                {% endif %}
                            </td>
                            <td>{{ lead.deal_feedback_time.strftime('%Y-%m-%d %H:%M') if lead.deal_feedback_time else '-' }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
// 获取统计数据
function loadStatistics() {
    fetch('/api/deal-statistics')
        .then(response => response.json())
        .then(data => {
            document.getElementById('total-leads').textContent = data.total_leads;
            document.getElementById('dealt-leads').textContent = data.dealt_leads;
            document.getElementById('deal-rate').textContent = data.deal_rate;
            
            // 更新最近签约记录表格
            const tbody = document.querySelector('#recent-deals-table tbody');
            tbody.innerHTML = '';
            data.recent_deals.forEach(deal => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${deal.lead_name}</td>
                    <td>${formatDealStatus(deal.deal_status)}</td>
                    <td>${deal.deal_time || '-'}</td>
                    <td>${deal.deal_amount ? '¥' + deal.deal_amount.toFixed(2) : '-'}</td>
                `;
                tbody.appendChild(tr);
            });
        });
}

// 格式化签约状态
function formatDealStatus(status) {
    const statusMap = {
        'won': '<span class="badge bg-success">签约</span>',
        'lost': '<span class="badge bg-danger">未签约</span>',
        'pending': '<span class="badge bg-warning">待定</span>'
    };
    return statusMap[status] || status;
}

// 页面加载完成后加载统计数据
document.addEventListener('DOMContentLoaded', loadStatistics);
</script>
{% endblock %}
{% endblock %}