<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}{% endblock %}</title>

    <!-- 应用系统主题 -->
    <script>
        (function() {
            // 优先使用系统设置的主题，如果没有则使用本地存储，最后默认橙色
            const systemTheme = '{{ system_theme if system_theme else "orange" }}';
            const savedTheme = localStorage.getItem('crm-theme') || systemTheme;
            document.documentElement.setAttribute('data-theme', savedTheme);
        })();
    </script>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/components.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/button-themes.css') }}">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- Select2 中文语言包 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/i18n/zh-CN.js"></script>
    <style>
        /* 橙色主题 */
        [data-theme="orange"] .theme-navbar {
            background-color: #ff6b00 !important;
        }
        [data-theme="orange"] .theme-color {
            color: #ff6b00 !important;
        }
        [data-theme="orange"] .theme-btn {
            background-color: #ff6b00 !important;
            border-color: #ff6b00 !important;
        }
        [data-theme="orange"] .btn-primary {
            background-color: #ff6b00 !important;
            border-color: #ff6b00 !important;
        }
        [data-theme="orange"] .bg-primary {
            background-color: #ff6b00 !important;
        }

        /* 蓝色主题 */
        [data-theme="blue"] .theme-navbar {
            background-color: #0d6efd !important;
        }
        [data-theme="blue"] .theme-color {
            color: #0d6efd !important;
        }
        [data-theme="blue"] .theme-btn {
            background-color: #0d6efd !important;
            border-color: #0d6efd !important;
        }
        [data-theme="blue"] .btn-primary {
            background-color: #0d6efd !important;
            border-color: #0d6efd !important;
        }
        [data-theme="blue"] .bg-primary {
            background-color: #0d6efd !important;
        }

        /* 绿色主题 */
        [data-theme="green"] .theme-navbar {
            background-color: #198754 !important;
        }
        [data-theme="green"] .theme-color {
            color: #198754 !important;
        }
        [data-theme="green"] .theme-btn {
            background-color: #198754 !important;
            border-color: #198754 !important;
        }
        [data-theme="green"] .btn-primary {
            background-color: #198754 !important;
            border-color: #198754 !important;
        }
        [data-theme="green"] .bg-primary {
            background-color: #198754 !important;
        }

        /* 紫色主题 */
        [data-theme="purple"] .theme-navbar {
            background-color: #6f42c1 !important;
        }
        [data-theme="purple"] .theme-color {
            color: #6f42c1 !important;
        }
        [data-theme="purple"] .theme-btn {
            background-color: #6f42c1 !important;
            border-color: #6f42c1 !important;
        }
        [data-theme="purple"] .btn-primary {
            background-color: #6f42c1 !important;
            border-color: #6f42c1 !important;
        }
        [data-theme="purple"] .bg-primary {
            background-color: #6f42c1 !important;
        }

        /* 深色主题 */
        [data-theme="dark"] .theme-navbar {
            background-color: #212529 !important;
        }
        [data-theme="dark"] .theme-color {
            color: #212529 !important;
        }
        [data-theme="dark"] .theme-btn {
            background-color: #212529 !important;
            border-color: #212529 !important;
        }
        [data-theme="dark"] .btn-primary {
            background-color: #212529 !important;
            border-color: #212529 !important;
        }
        [data-theme="dark"] .bg-primary {
            background-color: #212529 !important;
        }

        /* 紫色主题 */
        [data-theme="purple"] .theme-navbar {
            background-color: #6f42c1 !important;
        }
        [data-theme="purple"] .theme-color {
            color: #6f42c1 !important;
        }
        [data-theme="purple"] .theme-btn {
            background-color: #6f42c1 !important;
            border-color: #6f42c1 !important;
        }
        [data-theme="purple"] .btn-primary {
            background-color: #6f42c1 !important;
            border-color: #6f42c1 !important;
        }
        [data-theme="purple"] .bg-primary {
            background-color: #6f42c1 !important;
        }

        /* 深色主题 */
        [data-theme="dark"] .theme-navbar {
            background-color: #212529 !important;
        }
        [data-theme="dark"] .theme-color {
            color: #212529 !important;
        }
        [data-theme="dark"] .theme-btn {
            background-color: #212529 !important;
            border-color: #212529 !important;
        }
        [data-theme="dark"] .btn-primary {
            background-color: #212529 !important;
            border-color: #212529 !important;
        }
        [data-theme="dark"] .bg-primary {
            background-color: #212529 !important;
        }

        /* 按钮悬停效果 */
        .btn-primary:hover, .theme-btn:hover {
            opacity: 0.9;
        }

        .navbar-theme .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.85);
        }
        .navbar-theme .navbar-nav .nav-link:hover {
            color: #ffffff;
        }
        .navbar-theme .navbar-brand {
            color: #ffffff;
        }
        
        /* 主题预览样式 */
        .theme-preview {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            vertical-align: middle;
        }
        .theme-preview.orange { background-color: #ff6b00; }
        .theme-preview.blue { background-color: #0d6efd; }
        .theme-preview.green { background-color: #198754; }
        .theme-preview.purple { background-color: #6f42c1; }
        .theme-preview.dark { background-color: #212529; }

        /* 防止主题切换时的闪烁 */
        html {
            transition: none !important;
        }
        html.theme-loaded {
            transition: all 0.3s ease;
        }

        /* 页面加载时的平滑过渡 */
        body {
            opacity: 0;
            transition: opacity 0.2s ease-in-out;
        }
        body.loaded {
            opacity: 1;
        }

        /* 页面布局优化 */
        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .main-content {
            flex: 1;
            padding-top: var(--spacing-lg);
        }

        /* 用户头像样式 */
        .user-avatar {
            font-size: 1.25rem;
        }

        /* 导航链接活跃状态 */
        .nav-link.active {
            font-weight: var(--font-weight-semibold);
            position: relative;
        }

        /* 导航栏固定样式 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1030;
        }

        /* 调整主内容区域的顶部边距，避免被固定导航栏遮挡 */
        .main-content {
            margin-top: 56px; /* 导航栏的高度 */
        }

        /* 下拉菜单优化 */
        .dropdown-menu {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border-radius: 0.375rem;
            z-index: 1050; /* 确保下拉菜单在最上层 */
        }

        .dropdown-header {
            font-weight: var(--font-weight-semibold);
            color: var(--dark-color);
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* 警告框容器 */
        .alert-container {
            position: relative;
            z-index: 1000;
        }

        /* 统一页面标题样式 */
        .page-header {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 20px 30px;
            margin: 0 -30px 30px -30px;
        }

        .page-title {
            font-size: 1.75rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            color: #495057;
        }

        .page-title i {
            margin-right: 12px;
            font-size: 1.5rem;
            color: #6c757d;
        }

        .page-subtitle {
            margin: 8px 0 0 0;
            font-size: 0.95rem;
            color: #6c757d;
            font-weight: 400;
        }

        /* 确保内容区域有适当的边距 */
        .main-content .container-fluid {
            padding: 20px 30px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark theme-navbar navbar-theme">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('index') }}">
                <i class="fas fa-chart-line me-2"></i>
                <span class="fw-semibold">CRM系统</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint and request.endpoint == 'index' }}" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint and 'leads' in request.endpoint }}" href="{{ url_for('leads.leads_unified') }}">
                            <i class="fas fa-users me-1"></i>线索管理
                        </a>
                    </li>
                    {% if current_user.has_permission('department_manage') or current_user.has_permission('view_users') or current_user.role.code == 'super_admin' %}
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint and 'organization' in request.endpoint }}" href="{{ url_for('organization_unified.index') }}">
                            <i class="fas fa-sitemap me-1"></i>组织管理
                        </a>
                    </li>
                    {% endif %}
                    {% if current_user.role.code == 'super_admin' %}
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint and 'rbac' in request.endpoint }}" href="{{ url_for('rbac.roles') }}">
                            <i class="fas fa-user-shield me-1"></i>角色管理
                        </a>
                    </li>
                    {% endif %}
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                    {% if current_user.role.code == 'super_admin' %}
                    <!-- 系统设置下拉菜单 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="settingsDropdown" role="button" data-bs-toggle="dropdown" title="系统设置">
                            <i class="fas fa-cog me-1"></i>设置
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow">
                            <li><h6 class="dropdown-header">系统配置</h6></li>
                            <li><a class="dropdown-item" href="{{ url_for('admin_settings.configure_email') }}">
                                <i class="fas fa-envelope me-2"></i>邮件配置
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header">主题设置</h6></li>
                            <li><a class="dropdown-item" href="#" onclick="changeTheme('orange')">
                                <span class="badge me-2" style="background-color: #ff6b00; width: 12px; height: 12px;"></span>橙色主题
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeTheme('blue')">
                                <span class="badge me-2" style="background-color: #0d6efd; width: 12px; height: 12px;"></span>蓝色主题
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeTheme('green')">
                                <span class="badge me-2" style="background-color: #198754; width: 12px; height: 12px;"></span>绿色主题
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeTheme('purple')">
                                <span class="badge me-2" style="background-color: #6f42c1; width: 12px; height: 12px;"></span>紫色主题
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeTheme('dark')">
                                <span class="badge me-2" style="background-color: #212529; width: 12px; height: 12px;"></span>深色主题
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <!-- 非管理员的主题切换 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="themeDropdown" role="button" data-bs-toggle="dropdown" title="切换主题">
                            <i class="fas fa-palette"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow">
                            <li><h6 class="dropdown-header">选择主题</h6></li>
                            <li><a class="dropdown-item" href="#" onclick="changeTheme('orange')">
                                <span class="badge me-2" style="background-color: #ff6b00; width: 12px; height: 12px;"></span>橙色主题
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeTheme('blue')">
                                <span class="badge me-2" style="background-color: #0d6efd; width: 12px; height: 12px;"></span>蓝色主题
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeTheme('green')">
                                <span class="badge me-2" style="background-color: #198754; width: 12px; height: 12px;"></span>绿色主题
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeTheme('purple')">
                                <span class="badge me-2" style="background-color: #6f42c1; width: 12px; height: 12px;"></span>紫色主题
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeTheme('dark')">
                                <span class="badge me-2" style="background-color: #212529; width: 12px; height: 12px;"></span>深色主题
                            </a></li>
                        </ul>
                    </li>
                    {% endif %}
                    <!-- 消息通知 -->
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="{{ url_for('notifications.view_notifications') }}" title="消息通知">
                            <i class="fas fa-bell"></i>
                            {% if unread_notifications > 0 %}
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                {{ unread_notifications if unread_notifications < 100 else '99+' }}
                            </span>
                            {% endif %}
                        </a>
                    </li>
                    <!-- 用户菜单 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="user-avatar me-2">
                                <i class="fas fa-user-circle"></i>
                            </div>
                            <span class="d-none d-md-inline">{{ current_user.name or current_user.username }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow">
                            <li><h6 class="dropdown-header">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-user-circle me-2"></i>
                                    <div>
                                        <div class="fw-semibold">{{ current_user.name or current_user.username }}</div>
                                        <small class="text-muted">{{ current_user.role.name }}</small>
                                    </div>
                                </div>
                            </h6></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('profile') }}">
                                <i class="fas fa-user me-2"></i>个人资料
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>退出登录
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('login') }}">
                            <i class="fas fa-sign-in-alt me-1"></i>登录
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <div class="container-fluid">
            <!-- 消息提示区域 -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <div class="alert-container mt-3">
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-triangle' if category == 'warning' else 'times-circle' if category == 'danger' else 'info-circle' }} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endwith %}

            <!-- 页面内容 -->
            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="footer mt-auto py-3 bg-light border-top">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <span class="text-muted">&copy; 2024 CRM系统. 保留所有权利.</span>
                </div>
                <div class="col-md-6 text-end">
                    <span class="text-muted">版本 1.0.0</span>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/ui-enhancements.js') }}"></script>
    <script>
        // 主题切换功能
        function changeTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('crm-theme', theme);

            // 同时保存到数据库（仅管理员可以设置系统主题）
            {% if current_user.is_authenticated and current_user.role.code == 'super_admin' %}
            fetch('/api/set-system-theme', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ theme: theme })
            }).then(response => {
                if (response.ok) {
                    console.log('系统主题已更新:', theme);
                } else {
                    console.error('系统主题更新失败');
                }
            }).catch(error => {
                console.error('系统主题更新错误:', error);
            });
            {% endif %}
        }

        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            // 标记主题已加载，启用过渡效果
            document.documentElement.classList.add('theme-loaded');

            // 显示页面内容
            document.body.classList.add('loaded');
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>