{% extends 'base.html' %}

{% block title %}线索管理 - CRM系统{% endblock %}

{% block content %}
<style>
/* Select2 自定义样式 */
.select2-container--bootstrap-5 .select2-selection {
    min-height: 38px;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
}

.select2-container--bootstrap-5 .select2-selection--single {
    height: 38px;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
    line-height: 36px;
    padding-left: 12px;
}

.select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
    height: 36px;
    right: 10px;
}

.select2-container--bootstrap-5.select2-container--focus .select2-selection {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.select2-container--bootstrap-5 .select2-dropdown {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
}

.select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.375rem 0.75rem;
}

.select2-container--bootstrap-5 .select2-results__option--highlighted {
    background-color: #0d6efd;
    color: white;
}

/* 禁用状态样式 */
.select2-container--bootstrap-5.select2-container--disabled .select2-selection {
    background-color: #e9ecef;
    border-color: #ced4da;
    color: #6c757d;
}

/* 限制下拉框高度 */
.select2-dropdown-short .select2-results__options {
    max-height: 200px !important;
    overflow-y: auto;
}

/* 确保搜索框可见和可用 */
.select2-dropdown .select2-search {
    padding: 8px;
}

.select2-dropdown .select2-search .select2-search__field {
    width: 100% !important;
    box-sizing: border-box;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
}

.select2-dropdown .select2-search .select2-search__field:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 确保在模态框中正确显示 */
.modal .select2-container {
    z-index: 1060;
}

.modal .select2-dropdown {
    z-index: 1061;
}

/* 自定义下拉框样式 */
.dropdown-menu {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    z-index: 1070;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    color: #212529;
    text-decoration: none;
    background-color: transparent;
    border: 0;
    display: block;
    width: 100%;
    clear: both;
    font-weight: 400;
    line-height: 1.5;
    white-space: nowrap;
}

.dropdown-item:hover,
.dropdown-item:focus {
    color: #1e2125;
    background-color: #e9ecef;
}

.dropdown-item:active {
    color: #fff;
    background-color: #0d6efd;
}

/* 搜索框样式 */
.province-search,
.city-search {
    cursor: pointer;
}

.province-search:focus,
.city-search:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 城市省份输入选择框样式 */
.position-relative .dropdown-menu {
    display: none;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    background-color: #fff;
    margin-top: 2px;
    z-index: 1050;
}

.position-relative .dropdown-menu.show {
    display: block;
}

.position-relative .dropdown-item {
    padding: 0.5rem 1rem;
    color: #212529;
    text-decoration: none;
    cursor: pointer;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.position-relative .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #1e2125;
}

.position-relative .dropdown-item:focus {
    background-color: #e9ecef;
    color: #1e2125;
    outline: none;
}

.position-relative .dropdown-item.text-muted {
    color: #6c757d !important;
    cursor: default;
}

.position-relative .dropdown-item.text-muted:hover {
    background-color: transparent;
}

/* 模态框中的下拉框z-index */
.modal .position-relative .dropdown-menu {
    z-index: 1060;
}
</style>
<div class="container-fluid">
    <!-- 页面标题区域 -->
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="page-title">
                            <i class="fas fa-users me-2"></i>线索管理
                        </h1>
                        <p class="page-subtitle">统一管理所有线索，包括我的线索、团队线索、公海线索和异地到店线索</p>
                    </div>
                    <div class="text-end">
                        <div class="btn-group-theme">
                            <button type="button" class="btn btn-theme-primary" data-bs-toggle="modal" data-bs-target="#createLeadModal" onclick="showCreateLeadModal()">
                                <i class="fas fa-plus me-1"></i>新增线索
                            </button>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="fas fa-download me-1"></i>导出
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="exportLeads('csv')">
                                        <i class="fas fa-file-csv me-2"></i>导出CSV
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportLeads('excel')">
                                        <i class="fas fa-file-excel me-2"></i>导出Excel
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportLeads('json')">
                                        <i class="fas fa-file-code me-2"></i>导出JSON
                                    </a></li>
                                </ul>
                            </div>
                            <button type="button" class="btn btn-theme-success" onclick="showImportModal()">
                                <i class="fas fa-upload me-1"></i>导入
                            </button>
                            <button type="button" class="btn btn-theme-info" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-person-check text-primary" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-primary mt-2 mb-1">我的线索</h6>
                    <h4 class="mb-1">{{ stats.my_leads_count }}</h4>
                    <small class="text-muted">待跟进: {{ stats.my_pending_count }}</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-water text-info" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-info mt-2 mb-1">公海线索</h6>
                    <h4 class="mb-1">{{ stats.public_sea_count }}</h4>
                    <small class="text-muted">可认领</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-building text-warning" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-warning mt-2 mb-1">异地到店</h6>
                    <h4 class="mb-1">{{ stats.cross_location_pending_count }}</h4>
                    <small class="text-muted">待认领</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-check-circle text-success" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-success mt-2 mb-1">本月签约</h6>
                    <h4 class="mb-1">{{ stats.monthly_deals_count }}</h4>
                    <small class="text-muted">转化率: {{ stats.conversion_rate }}%</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg col-md-4 col-sm-6 mb-3">
            <div class="card h-100">
                <div class="card-body text-center py-3">
                    <i class="bi bi-truck text-secondary" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-secondary mt-2 mb-1">本月提车</h6>
                    <h4 class="mb-1">{{ stats.monthly_deliveries_count }}</h4>
                    <small class="text-muted">已完成</small>
                </div>
            </div>
        </div>
    </div>

    <!-- 标签页导航 -->
    <div class="card">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" id="leadsTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="my-leads-tab" data-bs-toggle="tab" data-bs-target="#my-leads" type="button" role="tab">
                        <i class="bi bi-person-check"></i> 我的线索 <span class="badge bg-primary ms-1">{{ stats.my_leads_count }}</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="team-leads-tab" data-bs-toggle="tab" data-bs-target="#team-leads" type="button" role="tab">
                        <i class="bi bi-people"></i> 团队线索
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="public-sea-tab" data-bs-toggle="tab" data-bs-target="#public-sea" type="button" role="tab">
                        <i class="bi bi-water"></i> 线索公海 <span class="badge bg-info ms-1">{{ stats.public_sea_count }}</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="cross-location-tab" data-bs-toggle="tab" data-bs-target="#cross-location" type="button" role="tab">
                        <i class="bi bi-building"></i> 异地到店 <span class="badge bg-warning ms-1">{{ stats.cross_location_pending_count }}</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="my-cross-location-tab" data-bs-toggle="tab" data-bs-target="#my-cross-location" type="button" role="tab">
                        <i class="bi bi-person-workspace"></i> 我的异地线索 <span class="badge bg-info ms-1" id="myCrossLocationCount">0</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="my-pushed-cross-location-tab" data-bs-toggle="tab" data-bs-target="#my-pushed-cross-location" type="button" role="tab">
                        <i class="bi bi-send"></i> 我推送的异地线索 <span class="badge bg-warning ms-1" id="myPushedCrossLocationCount">0</span>
                    </button>
                </li>
                {# 线索池管理标签页已隐藏 - 系统自动管理，无需手动操作 #}
            </ul>
        </div>
        
        <div class="card-body">
            <div class="tab-content" id="leadsTabContent">
                <!-- 我的线索 -->
                <div class="tab-pane fade show active" id="my-leads" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">我的线索</h5>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-primary" id="myLeadsBatchAssignBtn" onclick="showBatchAssignModal('my')" style="display: none;">
                                <i class="bi bi-person-plus"></i> 批量分配
                            </button>
                            <button class="btn btn-sm btn-outline-warning" id="myLeadsBatchReleaseBtn" onclick="batchOperation('release')" style="display: none;">
                                <i class="bi bi-water"></i> 批量释放到公海
                            </button>
                            <button class="btn btn-sm btn-outline-danger" id="myLeadsBatchDeleteBtn" onclick="batchDeleteLeads()" style="display: none;">
                                <i class="bi bi-trash"></i> 批量删除
                            </button>
                            <select class="form-select form-select-sm" id="myLeadsStageFilter" style="width: auto;">
                                <option value="">全部阶段</option>
                                <option value="new">新线索</option>
                                <option value="called">已拨电话</option>
                                <option value="connected">已接通</option>
                                <option value="valid_call">有效通话</option>
                                <option value="wechat_added">已加微信</option>
                                <option value="intentional">意向客户</option>
                                <option value="visited">已到面</option>
                                <option value="compliant">信息合规</option>
                                <option value="deal_done">已签约</option>
                                <option value="car_selected">已提车</option>
                            </select>
                            <input type="text" class="form-control form-control-sm" placeholder="搜索线索..." id="myLeadsSearch" style="width: 200px;">
                        </div>
                    </div>
                    <div id="myLeadsContent">
                        <!-- 动态加载内容 -->
                        <div class="text-center py-4">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 团队线索 -->
                <div class="tab-pane fade" id="team-leads" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">团队线索</h5>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-primary" id="teamLeadsBatchAssignBtn" onclick="showBatchAssignModal('team')" style="display: none;">
                                <i class="bi bi-person-plus"></i> 批量分配
                            </button>
                            <button class="btn btn-sm btn-outline-warning" id="teamLeadsBatchReleaseBtn" onclick="batchOperation('release')" style="display: none;">
                                <i class="bi bi-water"></i> 批量释放到公海
                            </button>
                            <button class="btn btn-sm btn-outline-danger" id="teamLeadsBatchDeleteBtn" onclick="batchDeleteLeads()" style="display: none;">
                                <i class="bi bi-trash"></i> 批量删除
                            </button>
                            <select class="form-select form-select-sm" id="teamLeadsOwnerFilter" style="width: auto;">
                                <option value="">全部负责人</option>
                                <!-- 动态加载负责人选项 -->
                            </select>
                            <select class="form-select form-select-sm" id="teamLeadsStageFilter" style="width: auto;">
                                <option value="">全部阶段</option>
                                <option value="new">新线索</option>
                                <option value="called">已拨电话</option>
                                <option value="connected">已接通</option>
                                <option value="valid_call">有效通话</option>
                                <option value="wechat_added">已加微信</option>
                                <option value="intentional">意向客户</option>
                                <option value="visited">已到面</option>
                                <option value="compliant">信息合规</option>
                                <option value="deal_done">已签约</option>
                                <option value="car_selected">已提车</option>
                            </select>
                        </div>
                    </div>
                    <div id="teamLeadsContent">
                        <!-- 动态加载内容 -->
                    </div>
                </div>

                <!-- 线索公海 -->
                <div class="tab-pane fade" id="public-sea" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">线索公海</h5>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-primary" onclick="batchOperation('claim')">
                                <i class="bi bi-check2-square"></i> 批量认领
                            </button>
                            <input type="text" class="form-control form-control-sm" placeholder="搜索公海线索..." id="publicSeaSearch" style="width: 200px;">
                        </div>
                    </div>
                    <div id="publicSeaContent">
                        <!-- 动态加载内容 -->
                    </div>
                </div>

                <!-- 异地到店 -->
                <div class="tab-pane fade" id="cross-location" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">异地到店线索</h5>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-success" onclick="batchOperation('claim_cross_location')">
                                <i class="bi bi-check2-square"></i> 批量认领
                            </button>
                            <select class="form-select form-select-sm" id="crossLocationSourceFilter" style="width: auto;">
                                <option value="">全部来源公司</option>
                                <!-- 动态加载来源公司选项 -->
                            </select>
                        </div>
                    </div>
                    <div id="crossLocationContent">
                        <!-- 动态加载内容 -->
                    </div>
                </div>

                <!-- 我的异地线索 -->
                <div class="tab-pane fade" id="my-cross-location" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">我的异地线索</h5>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-secondary" onclick="refreshMyCrossLocation()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div id="myCrossLocationContent">
                        <!-- 动态加载内容 -->
                    </div>
                </div>

                <!-- 我推送的异地线索 -->
                <div class="tab-pane fade" id="my-pushed-cross-location" role="tabpanel">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">我推送的异地线索</h5>
                        <div class="d-flex gap-2">
                            <!-- 状态筛选 -->
                            <select class="form-select form-select-sm" id="pushedStatusFilter" onchange="filterMyPushedLeads()">
                                <option value="">全部状态</option>
                                <option value="pending">待认领</option>
                                <option value="claimed">已认领</option>
                                <option value="recalled">已撤回</option>
                            </select>
                            <button class="btn btn-sm btn-outline-secondary" onclick="refreshMyPushedCrossLocation()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div id="myPushedCrossLocationContent">
                        <!-- 动态加载内容 -->
                    </div>
                </div>

                {# 线索池管理内容已隐藏 - 系统自动管理，无需手动操作 #}
            </div>
        </div>
    </div>
</div>

<!-- 新增线索模态框 -->
<div class="modal fade" id="createLeadModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新增线索</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createLeadForm" data-skip-validation="true">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="leadName" class="form-label">姓名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="leadName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="leadPhone" class="form-label">电话 <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" id="leadPhone" name="phone" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="leadCompanyName" class="form-label">省份 <span class="text-danger">*</span></label>
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="leadCompanyName" name="company_name"
                                           placeholder="请输入或选择省份" autocomplete="off" required>
                                    <div id="modal-province-dropdown" class="dropdown-menu position-absolute w-100" style="max-height: 200px; overflow-y: auto; z-index: 1050;"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="leadEmail" class="form-label">城市 <span class="text-danger">*</span></label>
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="leadEmail" name="email"
                                           placeholder="请输入或选择城市" autocomplete="off" required>
                                    <div id="modal-city-dropdown" class="dropdown-menu position-absolute w-100" style="max-height: 200px; overflow-y: auto; z-index: 1050;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="leadOwner" class="form-label">负责人</label>
                        <select class="form-select" id="leadOwner" name="owner_id">
                            <option value="{{ current_user.id }}">{{ current_user.username }}（我自己）</option>
                            <!-- 动态加载其他用户选项 -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="leadNotes" class="form-label">备注</label>
                        <textarea class="form-control" id="leadNotes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">创建线索</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 导入线索模态框 -->
<div class="modal fade" id="importLeadsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量导入线索</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="importLeadsForm" enctype="multipart/form-data" data-skip-validation="true">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <div class="modal-body">
                    <!-- 导入目标选择 -->
                    <div class="mb-3">
                        <label class="form-label">导入目标</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="import_target" id="importToMyCompany" value="my_company" checked>
                            <label class="form-check-label" for="importToMyCompany">
                                <i class="bi bi-building"></i> 导入到我的公司
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="import_target" id="importToOtherCompany" value="other_company">
                            <label class="form-check-label" for="importToOtherCompany">
                                <i class="bi bi-send"></i> 请求导入到其他公司（需要对方同意）
                            </label>
                        </div>
                    </div>

                    <!-- 目标公司选择（仅在选择其他公司时显示） -->
                    <div class="mb-3" id="targetCompanySection" style="display: none;">
                        <label for="targetCompanySearch" class="form-label">目标公司 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="targetCompanySearch" placeholder="输入公司名称搜索..." autocomplete="off">
                        <input type="hidden" id="targetCompanyId" name="target_company_id">
                        <div id="companySearchResults" class="list-group mt-2" style="display: none; max-height: 200px; overflow-y: auto;"></div>
                        <div class="form-text text-warning">
                            <i class="bi bi-info-circle"></i> 向其他公司发起导入请求需要对方管理员审批同意
                        </div>
                    </div>

                    <!-- 请求说明（仅在选择其他公司时显示） -->
                    <div class="mb-3" id="requestNotesSection" style="display: none;">
                        <label for="requestNotes" class="form-label">请求说明</label>
                        <textarea class="form-control" id="requestNotes" name="notes" rows="3" placeholder="请简要说明导入原因和线索来源..."></textarea>
                        <div class="form-text">详细的说明有助于提高审批通过率</div>
                    </div>

                    <!-- 文件选择 -->
                    <div class="mb-3">
                        <label for="importFile" class="form-label">选择文件 <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="importFile" name="file" accept=".csv,.xlsx,.xls" required>
                        <div class="form-text">支持CSV、Excel格式文件，最大10MB</div>
                    </div>

                    <!-- 文件格式说明 -->
                    <div class="mb-3">
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle"></i> 文件格式要求：</h6>
                            <ul class="mb-0">
                                <li><strong>支持格式：</strong>CSV、XLSX文件</li>
                                <li><strong>必需列：</strong>姓名、电话</li>
                                <li><strong>可选列：</strong>省份、城市、备注</li>
                                <li><strong>格式：</strong>第一行为表头，数据从第二行开始</li>
                                <li><strong>城市匹配：</strong>系统会自动匹配城市名称（如"东莞"自动匹配为"东莞市"）</li>
                                <li><strong>编码：</strong>CSV文件建议使用UTF-8编码</li>
                            </ul>
                        </div>
                    </div>

                    <!-- 模板下载 -->
                    <div class="mb-3">
                        <a href="{{ url_for('leads.download_template') }}" class="btn btn-outline-info btn-sm">
                            <i class="bi bi-download"></i> 下载导入模板
                        </a>
                        <a href="{{ url_for('leads.import_requests_unified') }}" class="btn btn-outline-secondary btn-sm ms-2">
                            <i class="bi bi-list-check"></i> 查看导入请求
                        </a>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary" id="submitImportBtn">
                        <span id="submitBtnText">开始导入</span>
                        <span id="submitBtnSpinner" class="spinner-border spinner-border-sm ms-2" style="display: none;"></span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 批量操作模态框 -->
<div class="modal fade" id="batchOperationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="batchOperationContent">
                    <!-- 动态内容 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmBatchOperation">确认操作</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量分配模态框 -->
<div class="modal fade" id="batchAssignModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量分配线索</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="batchAssignForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="batchAssignUser" class="form-label">选择负责人 <span class="text-danger">*</span></label>
                        <select class="form-select" id="batchAssignUser" name="user_id" required>
                            <option value="">-- 请选择负责人 --</option>
                            <!-- 动态加载用户选项 -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">将要分配的线索</label>
                        <div id="selectedLeadsInfo" class="alert alert-info">
                            <!-- 显示选中的线索信息 -->
                        </div>
                    </div>
                    <input type="hidden" id="batchAssignLeadIds" name="lead_ids">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">确认分配</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 编辑线索模态框已移除，现在使用专门的编辑页面 -->

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
}

.nav-tabs .nav-link.active {
    background-color: transparent;
    border-bottom: 2px solid #0d6efd;
    color: #0d6efd;
}

.badge {
    font-size: 0.7em;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.lead-stage-badge {
    font-size: 0.75em;
    padding: 0.25em 0.5em;
}

.action-buttons {
    white-space: nowrap;
}

.action-buttons .btn {
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* 自定义线索阶段颜色 */
.stage-wechat {
    background-color: #fd7e14 !important;  /* 橙色 - 已加微信 */
    color: white !important;
}

.stage-intentional {
    background-color: #6f42c1 !important;  /* 紫色 - 意向客户 */
    color: white !important;
}

.stage-visited {
    background-color: #0d6efd !important;  /* 深蓝色 - 已到面 */
    color: white !important;
}

.stage-compliant {
    background-color: #20c997 !important;  /* 青色 - 信息合规 */
    color: white !important;
}

.stage-deal {
    background-color: #e67e22 !important;  /* 深橙色 - 已签约 */
    color: white !important;
}
</style>
{% endblock %}

{% block scripts %}
<script>
// 全局变量
window.userRole = '{{ current_user.role.code }}';

// ===== 核心功能函数定义（必须在页面加载前定义） =====

// 认领异地线索
function claimCrossLocationLead(leadId) {
    console.log('=== 认领异地线索函数开始 ===');
    console.log('leadId:', leadId);
    console.log('typeof leadId:', typeof leadId);

    // 立即显示一个测试提示
    alert('认领函数被调用，leadId: ' + leadId);

    if (!leadId) {
        console.error('leadId 为空');
        alert('线索ID无效');
        return;
    }

    if (confirm('确定要认领这个异地线索吗？')) {
        console.log('用户确认认领，开始发送请求');

        // 显示加载状态
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> 认领中...';
        button.disabled = true;

        fetch(`/api/leads/${leadId}/claim-cross-location`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            console.log('认领响应状态:', response.status);
            return response.json().then(data => {
                return { status: response.status, data: data };
            });
        })
        .then(result => {
            console.log('认领响应数据:', result);

            // 恢复按钮状态
            button.innerHTML = originalText;
            button.disabled = false;

            if (result.status === 200 && result.data.success) {
                alert('认领成功！');
                // 刷新页面
                location.reload();
            } else {
                const errorMsg = result.data.message || `HTTP ${result.status} 错误`;
                console.error('认领失败详情:', errorMsg);
                alert('认领失败：' + errorMsg);
            }
        })
        .catch(error => {
            console.error('认领异地线索失败:', error);

            // 恢复按钮状态
            button.innerHTML = originalText;
            button.disabled = false;

            alert('认领失败，请重试');
        });
    } else {
        console.log('用户取消认领');
    }
}

// 显示提示消息
function showAlert(type, message) {
    console.log('showAlert 被调用:', type, message);
    alert(message); // 简化为使用原生alert
}

// 测试函数
function testFunction() {
    alert('测试函数工作正常！');
    console.log('测试函数被调用');
}

// ===== 页面初始化 =====

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 加载默认标签页内容
    loadMyLeads();
    
    // 标签页切换事件
    document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(e) {
            const target = e.target.getAttribute('data-bs-target');
            switch(target) {
                case '#my-leads':
                    loadMyLeads();
                    break;
                case '#team-leads':
                    loadTeamLeads();
                    break;
                case '#public-sea':
                    loadPublicSeaLeads();
                    break;
                case '#cross-location':
                    loadCrossLocationLeads();
                    break;
                case '#my-cross-location':
                    loadMyCrossLocationLeads();
                    break;
                case '#my-pushed-cross-location':
                    loadMyPushedCrossLocationLeads();
                    break;
                // 线索池管理功能已移除 - 系统自动管理
            }
        });
    });
    
    // 搜索和筛选事件
    setupFilters();
});

// 加载我的线索
function loadMyLeads() {
    const content = document.getElementById('myLeadsContent');
    content.innerHTML = '<div class="text-center py-4"><div class="spinner-border" role="status"></div></div>';
    
    fetch('/api/leads/my-leads')
        .then(response => response.json())
        .then(data => {
            content.innerHTML = renderLeadsTable(data.leads, 'my');
        })
        .catch(error => {
            content.innerHTML = '<div class="alert alert-danger">加载失败，请重试</div>';
        });
}

// 加载团队线索
function loadTeamLeads() {
    const content = document.getElementById('teamLeadsContent');
    content.innerHTML = '<div class="text-center py-4"><div class="spinner-border" role="status"></div></div>';

    // 获取筛选条件
    const ownerFilter = document.getElementById('teamLeadsOwnerFilter')?.value || '';
    const stageFilter = document.getElementById('teamLeadsStageFilter')?.value || '';

    let url = '/api/leads/team-leads?';
    if (ownerFilter) url += `owner_id=${ownerFilter}&`;
    if (stageFilter) url += `stage=${stageFilter}&`;

    fetch(url)
        .then(response => response.json())
        .then(data => {
            content.innerHTML = renderLeadsTable(data.leads, 'team');
            // 加载负责人选项（如果还没有加载）
            loadOwnerOptions();
        })
        .catch(error => {
            content.innerHTML = '<div class="alert alert-danger">加载失败，请重试</div>';
        });
}

// 加载负责人选项
function loadOwnerOptions() {
    const select = document.getElementById('teamLeadsOwnerFilter');
    if (!select || select.children.length > 1) return; // 已经加载过了

    fetch('/api/users/list')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                data.users.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user.id;
                    // 优先显示真实姓名，如果没有则显示用户名
                    const displayName = user.name || user.username || `用户${user.id}`;
                    const departmentInfo = user.department ? ` (${user.department})` : '';
                    option.textContent = `${displayName}${departmentInfo}`;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('加载负责人选项失败:', error);
        });
}

// 加载公海线索
function loadPublicSeaLeads() {
    const content = document.getElementById('publicSeaContent');
    content.innerHTML = '<div class="text-center py-4"><div class="spinner-border" role="status"></div></div>';
    
    fetch('/api/leads/public-sea')
        .then(response => response.json())
        .then(data => {
            content.innerHTML = renderLeadsTable(data.leads, 'public-sea');
        })
        .catch(error => {
            content.innerHTML = '<div class="alert alert-danger">加载失败，请重试</div>';
        });
}

// 加载异地到店线索
function loadCrossLocationLeads() {
    const content = document.getElementById('crossLocationContent');
    content.innerHTML = '<div class="text-center py-4"><div class="spinner-border" role="status"></div></div>';

    fetch('/api/leads/cross-location')
        .then(response => response.json())
        .then(data => {
            content.innerHTML = renderLeadsTable(data.leads, 'cross-location');
        })
        .catch(error => {
            content.innerHTML = '<div class="alert alert-danger">加载失败，请重试</div>';
        });
}

// 加载我的异地线索
function loadMyCrossLocationLeads() {
    const content = document.getElementById('myCrossLocationContent');
    content.innerHTML = '<div class="text-center py-4"><div class="spinner-border" role="status"></div></div>';

    fetch('/api/leads/my-cross-location')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                content.innerHTML = renderLeadsTable(data.leads, 'my-cross-location');
                // 更新标签页上的数量显示
                document.getElementById('myCrossLocationCount').textContent = data.leads.length;
            } else {
                content.innerHTML = '<div class="alert alert-danger">加载失败：' + (data.message || '未知错误') + '</div>';
            }
        })
        .catch(error => {
            content.innerHTML = '<div class="alert alert-danger">加载失败，请重试</div>';
        });
}

// 加载我推送的异地线索
function loadMyPushedCrossLocationLeads() {
    const content = document.getElementById('myPushedCrossLocationContent');
    content.innerHTML = '<div class="text-center py-4"><div class="spinner-border" role="status"></div></div>';

    const statusFilter = document.getElementById('pushedStatusFilter')?.value || '';
    const params = new URLSearchParams();
    if (statusFilter) params.append('status', statusFilter);

    fetch('/api/leads/my-pushed-cross-location?' + params.toString())
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                content.innerHTML = renderMyPushedLeadsTable(data.leads);
                // 更新标签页上的数量显示
                const countElement = document.getElementById('myPushedCrossLocationCount');
                if (countElement) {
                    countElement.textContent = data.leads.length;
                }
            } else {
                content.innerHTML = '<div class="alert alert-danger">加载失败：' + (data.message || '未知错误') + '</div>';
            }
        })
        .catch(error => {
            content.innerHTML = '<div class="alert alert-danger">加载失败，请重试</div>';
        });
}

// 渲染我推送的异地线索表格
function renderMyPushedLeadsTable(leads) {
    if (!leads || leads.length === 0) {
        return '<div class="alert alert-info">暂无推送的异地线索</div>';
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>客户姓名</th>
                        <th>联系方式</th>
                        <th>省份/城市</th>
                        <th>目标公司</th>
                        <th>当前状态</th>
                        <th>当前负责人</th>
                        <th>推送时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
    `;

    leads.forEach(lead => {
        const statusClass = {
            'pending': 'warning',
            'claimed': 'success',
            'recalled': 'secondary'
        }[lead.status] || 'secondary';

        html += `
            <tr>
                <td>
                    <strong>${lead.name}</strong>
                </td>
                <td>
                    <div>${lead.phone || '未填写'}</div>
                </td>
                <td>
                    <div>${lead.company_name || '未填写'}</div>
                    <small class="text-muted">${lead.email || '未填写'}</small>
                </td>
                <td>${lead.target_company || '未知'}</td>
                <td>
                    <span class="badge bg-${statusClass}">${lead.status_text}</span>
                </td>
                <td>${lead.current_owner || '无'}</td>
                <td>
                    <small>${lead.push_time || '未知'}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <a href="/leads/${lead.id}" class="btn btn-outline-primary btn-sm" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </a>
                        ${lead.status === 'pending' || lead.status === 'claimed' ? `
                            <button class="btn btn-outline-warning btn-sm" onclick="recallCrossLocationLead(${lead.id})" title="撤回">
                                <i class="bi bi-arrow-return-left"></i>
                            </button>
                        ` : ''}
                        ${lead.status === 'recalled' || lead.status === 'pending' ? `
                            <button class="btn btn-outline-success btn-sm" onclick="showRePushModal(${lead.id}, '${lead.name}')" title="重新推送">
                                <i class="bi bi-send"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    return html;
}

// 筛选我推送的线索
function filterMyPushedLeads() {
    loadMyPushedCrossLocationLeads();
}

// 刷新我推送的异地线索
function refreshMyPushedCrossLocation() {
    loadMyPushedCrossLocationLeads();
}

// 撤回异地线索
function recallCrossLocationLead(leadId) {
    if (!confirm('确定要撤回这条异地线索吗？撤回后线索将回到原负责人名下。')) {
        return;
    }

    console.log('开始撤回异地线索:', leadId);

    fetch(`/api/leads/${leadId}/recall-cross-location`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        console.log('撤回响应状态:', response.status);
        return response.json().then(data => {
            return { status: response.status, data: data };
        });
    })
    .then(result => {
        console.log('撤回响应数据:', result);
        if (result.status === 200 && result.data.success) {
            showAlert('success', '异地线索撤回成功！');
            // 刷新相关标签页
            refreshMyPushedCrossLocation();
            // 同时刷新我的线索页面
            if (typeof loadMyLeads === 'function') {
                loadMyLeads();
            }
            // 如果存在异地到店线索标签页，也刷新它
            if (typeof loadCrossLocationLeads === 'function') {
                loadCrossLocationLeads();
            }
        } else {
            const errorMsg = result.data.message || `HTTP ${result.status} 错误`;
            console.error('撤回失败详情:', errorMsg);
            showAlert('danger', '撤回失败：' + errorMsg);
        }
    })
    .catch(error => {
        console.error('撤回异地线索失败:', error);
        showAlert('danger', '撤回失败，请重试');
    });
}

// 显示重新推送模态框
function showRePushModal(leadId, leadName) {
    // 创建模态框HTML
    const modalHtml = `
        <div class="modal fade" id="rePushModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">重新推送异地线索</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>线索：<strong>${leadName}</strong></p>
                        <div class="mb-3">
                            <label class="form-label">选择目标公司</label>
                            <div class="position-relative">
                                <input type="text" class="form-control" id="rePushCompanySearch"
                                       placeholder="输入公司名称搜索..." autocomplete="off">
                                <div id="rePushCompanySuggestions" class="dropdown-menu position-absolute w-100"
                                     style="max-height: 200px; overflow-y: auto; z-index: 1060;"></div>
                            </div>
                            <input type="hidden" id="rePushTargetCompanyId">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="executeRePush(${leadId})">确认推送</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('rePushModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('rePushModal'));
    modal.show();

    // 初始化公司搜索功能
    initRePushCompanySearch();
}

// 初始化重新推送的公司搜索
function initRePushCompanySearch() {
    const searchInput = document.getElementById('rePushCompanySearch');
    const suggestionsDiv = document.getElementById('rePushCompanySuggestions');
    const targetCompanyIdInput = document.getElementById('rePushTargetCompanyId');

    searchInput.addEventListener('input', function() {
        const query = this.value.trim();
        if (query.length < 2) {
            suggestionsDiv.classList.remove('show');
            return;
        }

        // 搜索公司
        fetch(`/api/companies/search?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                suggestionsDiv.innerHTML = '';

                if (data.companies && data.companies.length > 0) {
                    data.companies.forEach(company => {
                        const item = document.createElement('a');
                        item.className = 'dropdown-item';
                        item.href = '#';
                        item.textContent = company.name;
                        item.addEventListener('click', function(e) {
                            e.preventDefault();
                            searchInput.value = company.name;
                            targetCompanyIdInput.value = company.id;
                            suggestionsDiv.classList.remove('show');
                        });
                        suggestionsDiv.appendChild(item);
                    });
                    suggestionsDiv.classList.add('show');
                } else {
                    suggestionsDiv.innerHTML = '<div class="dropdown-item text-muted">未找到匹配的公司</div>';
                    suggestionsDiv.classList.add('show');
                }
            })
            .catch(error => {
                console.error('搜索公司失败:', error);
            });
    });

    // 点击外部隐藏建议
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !suggestionsDiv.contains(e.target)) {
            suggestionsDiv.classList.remove('show');
        }
    });
}

// 执行重新推送
function executeRePush(leadId) {
    const targetCompanyId = document.getElementById('rePushTargetCompanyId').value;

    if (!targetCompanyId) {
        showAlert('warning', '请选择目标公司');
        return;
    }

    const formData = new FormData();
    formData.append('target_company_id', targetCompanyId);

    fetch(`/leads/${leadId}/re-push-to-cross-location`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('rePushModal'));
            modal.hide();
            // 刷新列表
            refreshMyPushedCrossLocation();
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('重新推送失败:', error);
        showAlert('danger', '重新推送失败，请重试');
    });
}

// 刷新我的异地线索
function refreshMyCrossLocation() {
    loadMyCrossLocationLeads();
}

// 渲染线索表格
function renderLeadsTable(leads, type) {
    if (!leads || leads.length === 0) {
        return '<div class="alert alert-info">暂无线索数据</div>';
    }
    
    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        ${type === 'public-sea' || type === 'cross-location' || type === 'my' || type === 'team' || type === 'my-cross-location' ? '<th><input type="checkbox" class="form-check-input" id="selectAll"></th>' : ''}
                        <th>姓名</th>
                        <th>省份/城市</th>
                        <th>联系方式</th>
                        <th>当前阶段</th>
                        ${type === 'team' ? '<th>负责人</th>' : ''}
                        ${type === 'cross-location' || type === 'my-cross-location' ? '<th>来源公司</th>' : ''}
                        <th>最后跟进</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    leads.forEach(lead => {
        html += `
            <tr>
                ${type === 'public-sea' || type === 'cross-location' || type === 'my' || type === 'team' || type === 'my-cross-location' ? `<td><input type="checkbox" class="form-check-input lead-checkbox" value="${lead.id}"></td>` : ''}
                <td><strong>${lead.name}</strong></td>
                <td>${lead.company_name}${lead.email ? '/' + lead.email : ''}</td>
                <td>${lead.phone ? maskPhoneNumber(lead.phone) : '-'}</td>
                <td><span class="badge lead-stage-badge ${getStageClass(lead.current_stage)}">${lead.current_stage}</span></td>
                ${type === 'team' ? `<td>${lead.owner_name || '-'}</td>` : ''}
                ${type === 'cross-location' || type === 'my-cross-location' ? `<td><span class="badge bg-info">${lead.source_company}</span></td>` : ''}
                <td><small class="text-muted">${lead.last_activity || '-'}</small></td>
                <td>
                    <div class="action-buttons">
                        ${getActionButtons(lead, type)}
                    </div>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    return html;
}

// 手机号码脱敏函数
function maskPhoneNumber(phone) {
    if (!phone || phone.length < 7) {
        return phone;
    }
    // 保留前3位和后3位，中间用*代替
    return phone.substring(0, 3) + '*'.repeat(phone.length - 6) + phone.substring(phone.length - 3);
}

// 获取阶段样式类
function getStageClass(stage) {
    const stageClasses = {
        '新线索': 'bg-secondary',           // 灰色 - 初始状态
        '已拨电话': 'bg-info',             // 浅蓝色 - 开始联系
        '已接通': 'bg-primary',            // 蓝色 - 成功接通
        '有效通话': 'bg-warning',          // 黄色 - 有效沟通
        '已加微信': 'stage-wechat',        // 橙色 - 建立联系
        '意向客户': 'stage-intentional',   // 紫色 - 有意向
        '已到面': 'stage-visited',         // 深蓝色 - 到店
        '信息合规': 'stage-compliant',     // 青色 - 合规确认
        '已签约': 'stage-deal',            // 深橙色 - 签约
        '已提车': 'bg-success'             // 绿色 - 最终完成
    };
    return stageClasses[stage] || 'bg-secondary';
}

// 获取操作按钮
function getActionButtons(lead, type) {
    let buttons = '';

    if (type === 'my') {
        // 我的线索：查看、编辑、删除
        buttons += `<button class="btn btn-sm btn-outline-info me-1" onclick="viewLead(${lead.id})" title="查看详情">
                        <i class="bi bi-eye"></i> 查看
                    </button>`;
        buttons += `<button class="btn btn-sm btn-outline-primary me-1" onclick="editLead(${lead.id})" title="编辑线索">
                        <i class="bi bi-pencil"></i> 编辑
                    </button>`;
        buttons += `<button class="btn btn-sm btn-outline-danger" onclick="deleteLead(${lead.id})" title="删除线索">
                        <i class="bi bi-trash"></i> 删除
                    </button>`;
    } else if (type === 'team') {
        // 团队线索：查看、编辑（如果有权限）、删除（管理员）
        buttons += `<button class="btn btn-sm btn-outline-info me-1" onclick="viewLead(${lead.id})" title="查看详情">
                        <i class="bi bi-eye"></i> 查看
                    </button>`;

        // 检查用户权限（通过全局变量）
        if (window.userRole && ['super_admin', 'company_admin', 'department_admin'].includes(window.userRole)) {
            buttons += `<button class="btn btn-sm btn-outline-primary me-1" onclick="editLead(${lead.id})" title="编辑线索">
                            <i class="bi bi-pencil"></i> 编辑
                        </button>`;
            buttons += `<button class="btn btn-sm btn-outline-danger" onclick="deleteLead(${lead.id})" title="删除线索">
                            <i class="bi bi-trash"></i> 删除
                        </button>`;
        }
    } else if (type === 'public-sea') {
        // 公海线索：认领
        buttons += `<button class="btn btn-sm btn-success" onclick="claimLead(${lead.id})" title="认领线索">
                        <i class="bi bi-hand-thumbs-up"></i> 认领
                    </button>`;
    } else if (type === 'cross-location') {
        // 异地到店线索：认领
        buttons += `<button class="btn btn-sm btn-warning" onclick="claimCrossLocationLead(${lead.id})" title="认领异地线索">
                        <i class="bi bi-building"></i> 认领
                    </button>`;
    }

    return buttons;
}

// 设置筛选器
function setupFilters() {
    // 我的线索筛选
    document.getElementById('myLeadsStageFilter').addEventListener('change', function() {
        loadMyLeads();
    });

    document.getElementById('myLeadsSearch').addEventListener('input', debounce(function() {
        loadMyLeads();
    }, 300));

    // 团队线索筛选
    const teamOwnerFilter = document.getElementById('teamLeadsOwnerFilter');
    const teamStageFilter = document.getElementById('teamLeadsStageFilter');

    if (teamOwnerFilter) {
        teamOwnerFilter.addEventListener('change', function() {
            loadTeamLeads();
        });
    }

    if (teamStageFilter) {
        teamStageFilter.addEventListener('change', function() {
            loadTeamLeads();
        });
    }

    // 公海线索搜索
    const publicSeaSearch = document.getElementById('publicSeaSearch');
    if (publicSeaSearch) {
        publicSeaSearch.addEventListener('input', debounce(function() {
            loadPublicSeaLeads();
        }, 300));
    }

    // 异地线索筛选
    const crossLocationFilter = document.getElementById('crossLocationSourceFilter');
    if (crossLocationFilter) {
        crossLocationFilter.addEventListener('change', function() {
            loadCrossLocationLeads();
        });
    }
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 刷新数据
function refreshData() {
    location.reload();
}

// 认领线索
function claimLead(leadId) {
    if (confirm('确定要认领这个线索吗？')) {
        fetch(`/api/leads/${leadId}/claim`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', '认领成功！');
                loadPublicSeaLeads();
                loadMyLeads();
            } else {
                showAlert('danger', '认领失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('认领公海线索失败:', error);
            showAlert('danger', '认领失败，请重试');
        });
    }
}

// 获取CSRF Token
function getCsrfToken() {
    return document.querySelector('meta[name=csrf-token]').getAttribute('content');
}

// 显示创建线索模态框
function showCreateLeadModal() {
    // 加载用户选项
    loadUserOptions('leadOwner');

    // 初始化省份城市联动
    initProvinceCity();

    // 绑定表单提交事件（确保在模态框显示时绑定）
    bindCreateLeadFormSubmit();
}

// 绑定创建线索表单提交事件
function bindCreateLeadFormSubmit() {
    const form = document.getElementById('createLeadForm');
    if (!form) {
        console.error('找不到创建线索表单');
        return;
    }

    // 移除之前的事件监听器（如果存在）
    form.removeEventListener('submit', handleCreateLeadSubmit);

    // 添加新的事件监听器
    form.addEventListener('submit', handleCreateLeadSubmit);
    console.log('创建线索表单事件监听器已绑定');
}

// 处理创建线索表单提交
function handleCreateLeadSubmit(e) {
    console.log('表单提交事件被触发');
    e.preventDefault();

    const formData = new FormData(this);
    const data = Object.fromEntries(formData);

    // 调试：打印表单数据
    console.log('表单数据:', data);
    console.log('省份值:', data.company_name);
    console.log('城市值:', data.email);

    // 特别检查直辖市情况
    if (data.company_name && ["北京市", "上海市", "天津市", "重庆市"].includes(data.company_name)) {
        console.log('检测到直辖市:', data.company_name);
        console.log('城市字段是否正确填充:', data.email === data.company_name);
    }

    // 验证必填字段
    if (!data.name || !data.phone || !data.company_name || !data.email) {
        alert('请填写所有必填字段');
        return;
    }

    fetch('/api/leads', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('线索创建成功！');
            bootstrap.Modal.getInstance(document.getElementById('createLeadModal')).hide();
            document.getElementById('createLeadForm').reset();
            refreshCurrentTab();
        } else {
            alert('创建失败：' + data.message);
        }
    })
    .catch(error => {
        alert('创建失败，请重试');
        console.error('Error:', error);
    });
}

// 模态框关闭时清理表单
$('#createLeadModal').on('hidden.bs.modal', function () {
    const provinceInput = document.getElementById('leadCompanyName');
    const cityInput = document.getElementById('leadEmail');
    const provinceDropdown = document.getElementById('modal-province-dropdown');
    const cityDropdown = document.getElementById('modal-city-dropdown');

    // 清空输入框
    if (provinceInput) provinceInput.value = '';
    if (cityInput) {
        cityInput.value = '';
        cityInput.disabled = true;
        cityInput.readOnly = false;
        cityInput.style.backgroundColor = '';
        cityInput.placeholder = '请先选择省份';
    }

    // 隐藏下拉框
    if (provinceDropdown) provinceDropdown.classList.remove('show');
    if (cityDropdown) cityDropdown.classList.remove('show');

    // 清空表单其他字段
    document.getElementById('createLeadForm').reset();
});



// 显示导入模态框
function showImportModal() {
    const modal = new bootstrap.Modal(document.getElementById('importLeadsModal'));
    modal.show();
}

// 导入目标选择事件
document.addEventListener('DOMContentLoaded', function() {
    const importToMyCompany = document.getElementById('importToMyCompany');
    const importToOtherCompany = document.getElementById('importToOtherCompany');
    const targetCompanySection = document.getElementById('targetCompanySection');
    const requestNotesSection = document.getElementById('requestNotesSection');
    const submitBtnText = document.getElementById('submitBtnText');

    function toggleImportTarget() {
        if (importToOtherCompany.checked) {
            targetCompanySection.style.display = 'block';
            requestNotesSection.style.display = 'block';
            submitBtnText.textContent = '发送导入请求';
        } else {
            targetCompanySection.style.display = 'none';
            requestNotesSection.style.display = 'none';
            submitBtnText.textContent = '开始导入';
            // 清空目标公司选择
            document.getElementById('targetCompanySearch').value = '';
            document.getElementById('targetCompanyId').value = '';
        }
    }

    importToMyCompany.addEventListener('change', toggleImportTarget);
    importToOtherCompany.addEventListener('change', toggleImportTarget);

    // 公司搜索功能
    const companySearchInput = document.getElementById('targetCompanySearch');
    const companySearchResults = document.getElementById('companySearchResults');
    const targetCompanyIdInput = document.getElementById('targetCompanyId');

    let searchTimeout;
    companySearchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();

        if (query.length < 2) {
            companySearchResults.style.display = 'none';
            return;
        }

        searchTimeout = setTimeout(() => {
            searchCompanies(query);
        }, 300);
    });

    function searchCompanies(query) {
        fetch(`/api/companies/search?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                companySearchResults.innerHTML = '';

                // 处理错误响应
                if (!data.success) {
                    companySearchResults.innerHTML = '<div class="list-group-item text-danger">搜索出错，请重试</div>';
                    companySearchResults.style.display = 'block';
                    return;
                }

                // 检查是否有公司数据
                const companies = data.companies || [];
                if (companies.length > 0) {
                    displayCompanyResults(companies);
                } else {
                    companySearchResults.innerHTML = '<div class="list-group-item text-muted">未找到匹配的公司</div>';
                    companySearchResults.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('搜索公司失败:', error);
                companySearchResults.innerHTML = '<div class="list-group-item text-danger">网络错误，请重试</div>';
                companySearchResults.style.display = 'block';
            });
    }

    function displayCompanyResults(companies) {
        const html = companies.map(company => `
            <div class="list-group-item list-group-item-action" onclick="selectCompany(${company.id}, '${company.name}')">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${company.name}</strong>
                        <small class="text-muted d-block">${company.type || '未知类型'}</small>
                    </div>
                    <small class="text-muted">${company.code || ''}</small>
                </div>
            </div>
        `).join('');

        companySearchResults.innerHTML = html;
        companySearchResults.style.display = 'block';
    }

    // 全局函数，供onclick使用
    window.selectCompany = function(companyId, companyName) {
        companySearchInput.value = companyName;
        targetCompanyIdInput.value = companyId;
        companySearchResults.style.display = 'none';
    };

    // 点击外部隐藏搜索结果
    document.addEventListener('click', function(e) {
        if (!companySearchInput.contains(e.target) && !companySearchResults.contains(e.target)) {
            companySearchResults.style.display = 'none';
        }
    });
});

// 导入线索表单提交
document.getElementById('importLeadsForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const submitBtn = document.getElementById('submitImportBtn');
    const submitBtnText = document.getElementById('submitBtnText');
    const submitBtnSpinner = document.getElementById('submitBtnSpinner');
    const importTarget = document.querySelector('input[name="import_target"]:checked').value;

    // 验证
    if (importTarget === 'other_company') {
        const targetCompanyId = document.getElementById('targetCompanyId').value;
        if (!targetCompanyId) {
            alert('请选择目标公司');
            return;
        }
    }

    // 显示加载状态
    submitBtn.disabled = true;
    submitBtnSpinner.style.display = 'inline-block';

    const formData = new FormData(this);

    // 根据导入目标选择不同的API
    const apiUrl = importTarget === 'other_company' ? '/import-leads' : '/api/leads/import';

    fetch(apiUrl, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (importTarget === 'other_company') {
            // 跨公司导入返回页面重定向或错误信息
            if (response.ok) {
                alert('导入请求已发送，等待对方审批');
                bootstrap.Modal.getInstance(document.getElementById('importLeadsModal')).hide();
                this.reset();
                location.reload(); // 刷新页面显示最新状态
                return null; // 明确返回null，避免进入下一个then
            } else {
                // 处理错误响应
                return response.text().then(text => {
                    console.error('跨公司导入失败:', text);
                    throw new Error('导入请求失败，请检查文件格式或联系管理员');
                });
            }
        } else {
            // 本公司导入返回JSON
            if (!response.ok) {
                throw new Error('网络请求失败');
            }
            return response.json();
        }
    })
    .then(data => {
        // 只有本公司导入且data不为null时才处理
        if (importTarget === 'my_company' && data !== null) {
            if (data.success) {
                alert(data.message);
                bootstrap.Modal.getInstance(document.getElementById('importLeadsModal')).hide();
                this.reset();
                refreshCurrentTab();
            } else {
                alert('导入失败：' + data.message);
            }
        }
    })
    .catch(error => {
        alert('操作失败：' + error.message);
        console.error('Import Error:', error);
    })
    .finally(() => {
        // 恢复按钮状态
        submitBtn.disabled = false;
        submitBtnSpinner.style.display = 'none';
    });
});

// 导出线索
function exportLeads(format) {
    const currentTab = document.querySelector('.nav-link.active').getAttribute('data-bs-target');
    let filters = getCurrentFilters();

    fetch('/api/leads/export', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify({
            format: format,
            filters: filters
        })
    })
    .then(response => {
        if (response.ok) {
            if (format === 'json') {
                return response.json();
            } else {
                return response.blob();
            }
        }
        throw new Error('导出失败');
    })
    .then(data => {
        if (format === 'json') {
            // JSON格式直接显示或下载
            const blob = new Blob([JSON.stringify(data.data, null, 2)], {type: 'application/json'});
            downloadBlob(blob, `leads_${new Date().toISOString().slice(0,10)}.json`);
        } else {
            // CSV和Excel格式直接下载
            const filename = `leads_${new Date().toISOString().slice(0,10)}.${format === 'excel' ? 'xlsx' : 'csv'}`;
            downloadBlob(data, filename);
        }
    })
    .catch(error => {
        alert('导出失败，请重试');
        console.error('Error:', error);
    });
}

// 下载Blob文件
function downloadBlob(blob, filename) {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
}

// 获取当前筛选条件
function getCurrentFilters() {
    const currentTab = document.querySelector('.nav-link.active').getAttribute('data-bs-target');
    let filters = {};

    if (currentTab === '#my-leads') {
        const stage = document.getElementById('myLeadsStageFilter').value;
        const search = document.getElementById('myLeadsSearch').value;
        if (stage) filters.stage = stage;
        if (search) filters.search = search;
    } else if (currentTab === '#team-leads') {
        const owner = document.getElementById('teamLeadsOwnerFilter').value;
        const stage = document.getElementById('teamLeadsStageFilter').value;
        if (owner) filters.owner_id = owner;
        if (stage) filters.stage = stage;
    }

    return filters;
}

// 查看线索详情
function viewLead(leadId) {
    window.open(`/leads/${leadId}`, '_blank');
}

// 编辑线索 - 跳转到专门的编辑页面
function editLead(leadId) {
    console.log('编辑线索被调用，线索ID:', leadId);
    // 直接跳转到编辑页面
    window.location.href = `/leads/${leadId}/edit`;
}

// 编辑相关函数已移除，现在使用专门的编辑页面

// 删除线索
function deleteLead(leadId) {
    if (confirm('确定要删除这个线索吗？此操作不可恢复。')) {
        fetch(`/api/leads/${leadId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCsrfToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('线索删除成功！');
                refreshCurrentTab();
            } else {
                alert('删除失败：' + data.message);
            }
        })
        .catch(error => {
            alert('删除失败，请重试');
            console.error('Error:', error);
        });
    }
}

// 批量删除线索
function batchDeleteLeads() {
    const checkedCheckboxes = document.querySelectorAll('.lead-checkbox:checked');
    if (checkedCheckboxes.length === 0) {
        alert('请先选择要删除的线索');
        return;
    }

    const leadIds = Array.from(checkedCheckboxes).map(cb => cb.value);

    if (confirm(`确定要删除选中的 ${leadIds.length} 个线索吗？此操作不可恢复。`)) {
        fetch('/api/leads/batch-delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                lead_ids: leadIds
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`成功删除 ${data.deleted_count} 个线索！`);
                refreshCurrentTab();
            } else {
                alert('批量删除失败：' + data.message);
            }
        })
        .catch(error => {
            alert('批量删除失败，请重试');
            console.error('Error:', error);
        });
    }
}

// 批量操作
function batchOperation(operation) {
    const checkboxes = document.querySelectorAll('.lead-checkbox:checked');
    if (checkboxes.length === 0) {
        alert('请选择要操作的线索');
        return;
    }

    const leadIds = Array.from(checkboxes).map(cb => cb.value);

    let operationData = {
        operation: operation,
        lead_ids: leadIds
    };

    if (operation === 'assign') {
        const targetUserId = prompt('请输入目标用户ID：');
        if (!targetUserId) return;
        operationData.target_user_id = targetUserId;
    }

    if (confirm(`确定要${getOperationName(operation)} ${leadIds.length} 个线索吗？`)) {
        fetch('/api/leads/batch-operations', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(operationData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                refreshCurrentTab();
                // 清除所有选中的复选框
                document.querySelectorAll('.lead-checkbox:checked').forEach(cb => cb.checked = false);
            } else {
                showAlert('danger', '操作失败：' + data.message);
            }
        })
        .catch(error => {
            showAlert('danger', '操作失败，请重试');
            console.error('批量操作错误:', error);
        });
    }
}

// 获取操作名称
function getOperationName(operation) {
    const names = {
        'assign': '分配',
        'delete': '删除',
        'release': '释放到公海',
        'claim': '认领',
        'claim_cross_location': '认领异地线索'
    };
    return names[operation] || operation;
}

// 刷新当前标签页
function refreshCurrentTab() {
    const activeTab = document.querySelector('.nav-link.active');
    if (activeTab) {
        activeTab.click();
    }
}

// 加载用户选项
function loadUserOptions(selectId) {
    fetch('/api/users/list')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById(selectId);
                if (!select) {
                    console.error('未找到选择框元素:', selectId);
                    return;
                }

                // 清空现有选项
                select.innerHTML = '';

                // 添加默认选项
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = '-- 请选择负责人 --';
                select.appendChild(defaultOption);

                // 添加用户选项
                data.users.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user.id;
                    // 优先显示真实姓名(name)，如果没有则显示用户名(username)
                    const displayName = user.name || user.username || `用户${user.id}`;
                    const departmentInfo = user.department ? ` (${user.department})` : '';
                    option.textContent = `${displayName}${departmentInfo}`;
                    select.appendChild(option);
                });
            } else {
                console.error('用户列表API返回失败:', data.message);
            }
        })
        .catch(error => {
            console.error('加载用户列表失败:', error);
        });
}

// 显示批量分配模态框
function showBatchAssignModal(type) {
    const checkboxes = document.querySelectorAll('.lead-checkbox:checked');

    if (checkboxes.length === 0) {
        alert('请选择要分配的线索');
        return;
    }

    const leadIds = Array.from(checkboxes).map(cb => cb.value);

    // 设置选中的线索ID
    const leadIdsInput = document.getElementById('batchAssignLeadIds');
    if (leadIdsInput) {
        leadIdsInput.value = leadIds.join(',');
    }

    // 显示选中的线索信息
    const selectedLeadsInfo = document.getElementById('selectedLeadsInfo');
    if (selectedLeadsInfo) {
        selectedLeadsInfo.innerHTML = `
            <i class="bi bi-info-circle"></i> 已选择 <strong>${leadIds.length}</strong> 条线索进行分配
        `;
    }

    // 加载用户选项
    loadUserOptions('batchAssignUser');

    // 显示模态框
    const modalElement = document.getElementById('batchAssignModal');
    if (modalElement) {
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    }
}

// 批量分配表单提交
document.getElementById('batchAssignForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const userId = document.getElementById('batchAssignUser').value;
    const leadIds = document.getElementById('batchAssignLeadIds').value.split(',');

    if (!userId) {
        alert('请选择负责人');
        return;
    }

    fetch('/api/leads/batch-assign', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify({
            user_id: userId,
            lead_ids: leadIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('批量分配成功！');
            bootstrap.Modal.getInstance(document.getElementById('batchAssignModal')).hide();
            refreshCurrentTab();
        } else {
            alert('分配失败：' + data.message);
        }
    })
    .catch(error => {
        alert('分配失败，请重试');
        console.error('Error:', error);
    });
});

// 复选框选择事件处理
document.addEventListener('change', function(e) {
    if (e.target.id === 'selectAll') {
        // 全选/取消全选
        const checkboxes = document.querySelectorAll('.lead-checkbox');
        checkboxes.forEach(cb => cb.checked = e.target.checked);
        updateBatchButtons();
    } else if (e.target.classList.contains('lead-checkbox')) {
        // 单个复选框变化
        updateBatchButtons();

        // 更新全选状态
        const allCheckboxes = document.querySelectorAll('.lead-checkbox');
        const checkedCheckboxes = document.querySelectorAll('.lead-checkbox:checked');
        const selectAllCheckbox = document.getElementById('selectAll');

        if (selectAllCheckbox) {
            selectAllCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < allCheckboxes.length;
        }
    }
});

// 更新批量操作按钮状态
function updateBatchButtons() {
    const checkedCount = document.querySelectorAll('.lead-checkbox:checked').length;

    // 更新我的线索批量分配按钮
    const myLeadsBatchBtn = document.getElementById('myLeadsBatchAssignBtn');
    if (myLeadsBatchBtn) {
        myLeadsBatchBtn.style.display = checkedCount > 0 ? 'inline-block' : 'none';
    }

    // 更新我的线索批量释放按钮
    const myLeadsBatchReleaseBtn = document.getElementById('myLeadsBatchReleaseBtn');
    if (myLeadsBatchReleaseBtn) {
        myLeadsBatchReleaseBtn.style.display = checkedCount > 0 ? 'inline-block' : 'none';
    }

    // 更新我的线索批量删除按钮
    const myLeadsBatchDeleteBtn = document.getElementById('myLeadsBatchDeleteBtn');
    if (myLeadsBatchDeleteBtn) {
        myLeadsBatchDeleteBtn.style.display = checkedCount > 0 ? 'inline-block' : 'none';
    }

    // 更新团队线索批量分配按钮
    const teamLeadsBatchBtn = document.getElementById('teamLeadsBatchAssignBtn');
    if (teamLeadsBatchBtn) {
        teamLeadsBatchBtn.style.display = checkedCount > 0 ? 'inline-block' : 'none';
    }

    // 更新团队线索批量释放按钮
    const teamLeadsBatchReleaseBtn = document.getElementById('teamLeadsBatchReleaseBtn');
    if (teamLeadsBatchReleaseBtn) {
        teamLeadsBatchReleaseBtn.style.display = checkedCount > 0 ? 'inline-block' : 'none';
    }

    // 更新团队线索批量删除按钮
    const teamLeadsBatchDeleteBtn = document.getElementById('teamLeadsBatchDeleteBtn');
    if (teamLeadsBatchDeleteBtn) {
        teamLeadsBatchDeleteBtn.style.display = checkedCount > 0 ? 'inline-block' : 'none';
    }
}

// 通用的线索加载函数
function loadLeads(type) {
    switch(type) {
        case 'my':
            loadMyLeads();
            break;
        case 'team':
            loadTeamLeads();
            break;
        case 'public-sea':
            loadPublicSeaLeads();
            break;
        case 'cross-location':
            loadCrossLocationLeads();
            break;
        case 'my-cross-location':
            loadMyCrossLocationLeads();
            break;
        default:
            loadMyLeads();
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 默认加载我的线索
    loadLeads('my');
});

// 初始化省份城市联动功能 - 支持输入选择
function initProvinceCity() {
    const provinceInput = document.getElementById('leadCompanyName');
    const cityInput = document.getElementById('leadEmail');
    const provinceDropdown = document.getElementById('modal-province-dropdown');
    const cityDropdown = document.getElementById('modal-city-dropdown');

    // 直辖市列表
    const directMunicipalities = ["北京市", "上海市", "天津市", "重庆市"];

    let modalRegionsData = null;

    fetch('/data/china_regions.json')
        .then(response => response.json())
        .then(data => {
            modalRegionsData = data;

            // 初始化省份输入框事件
            initModalProvinceInput();
            // 初始化城市输入框事件
            initModalCityInput();
        })
        .catch(error => console.error('加载省份和城市数据失败:', error));

    // 初始化省份输入框
    function initModalProvinceInput() {
        // 输入事件 - 实时搜索
        provinceInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            showModalProvinceDropdown(query);
        });

        // 获得焦点时显示所有选项
        provinceInput.addEventListener('focus', function() {
            showModalProvinceDropdown('');
        });

        // 失去焦点时隐藏下拉框
        provinceInput.addEventListener('blur', function() {
            setTimeout(() => {
                hideModalDropdown(provinceDropdown);
            }, 200);
        });

        // 省份变化时更新城市
        provinceInput.addEventListener('change', function() {
            updateModalCityOptions(this.value);
        });
    }

    // 初始化城市输入框
    function initModalCityInput() {
        // 输入事件 - 实时搜索
        cityInput.addEventListener('input', function() {
            // 如果是只读状态（直辖市），不处理输入事件
            if (this.readOnly) return;

            const query = this.value.toLowerCase();
            const selectedProvince = provinceInput.value;
            showModalCityDropdown(selectedProvince, query);
        });

        // 获得焦点时显示选项
        cityInput.addEventListener('focus', function() {
            const selectedProvince = provinceInput.value;
            // 如果是只读状态（直辖市），不显示下拉菜单
            if (selectedProvince && !this.readOnly) {
                showModalCityDropdown(selectedProvince, '');
            }
        });

        // 失去焦点时隐藏下拉框
        cityInput.addEventListener('blur', function() {
            setTimeout(() => {
                hideModalDropdown(cityDropdown);
            }, 200);
        });
    }

    // 显示省份下拉选项
    function showModalProvinceDropdown(query) {
        if (!modalRegionsData) return;

        const filteredProvinces = modalRegionsData.provinces.filter(province =>
            province.name.toLowerCase().includes(query)
        );

        provinceDropdown.innerHTML = '';

        if (filteredProvinces.length === 0) {
            provinceDropdown.innerHTML = '<div class="dropdown-item text-muted">未找到匹配的省份</div>';
        } else {
            filteredProvinces.forEach(province => {
                const item = document.createElement('a');
                item.className = 'dropdown-item';
                item.href = '#';
                item.textContent = province.name;
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    provinceInput.value = province.name;
                    hideModalDropdown(provinceDropdown);
                    updateModalCityOptions(province.name);
                    provinceInput.focus();
                });
                provinceDropdown.appendChild(item);
            });
        }

        showModalDropdown(provinceDropdown);
    }

    // 显示城市下拉选项
    function showModalCityDropdown(selectedProvince, query) {
        if (!modalRegionsData || !selectedProvince) return;

        let cities = [];

        // 检查是否是直辖市
        if (directMunicipalities.includes(selectedProvince)) {
            cities = [selectedProvince];
        } else {
            const province = modalRegionsData.provinces.find(p => p.name === selectedProvince);
            if (province && province.cities) {
                cities = province.cities;
            }
        }

        const filteredCities = cities.filter(city =>
            city.toLowerCase().includes(query)
        );

        cityDropdown.innerHTML = '';

        if (filteredCities.length === 0) {
            cityDropdown.innerHTML = '<div class="dropdown-item text-muted">未找到匹配的城市</div>';
        } else {
            filteredCities.forEach(city => {
                const item = document.createElement('a');
                item.className = 'dropdown-item';
                item.href = '#';
                item.textContent = city;
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    cityInput.value = city;
                    hideModalDropdown(cityDropdown);
                    cityInput.focus();
                });
                cityDropdown.appendChild(item);
            });
        }

        showModalDropdown(cityDropdown);
    }

    // 更新城市选项
    function updateModalCityOptions(selectedProvince) {
        if (!selectedProvince) {
            cityInput.value = '';
            cityInput.placeholder = '请先选择省份';
            cityInput.disabled = true;
            return;
        }

        cityInput.disabled = false;
        cityInput.placeholder = '请输入或选择城市';

        // 如果是直辖市，自动设置城市
        if (directMunicipalities.includes(selectedProvince)) {
            cityInput.value = selectedProvince;
            cityInput.readOnly = true;
            cityInput.style.backgroundColor = '#f8f9fa';
            cityInput.placeholder = '直辖市自动填充';
        } else {
            cityInput.value = '';
            cityInput.readOnly = false;
            cityInput.style.backgroundColor = '';
            cityInput.placeholder = '请输入或选择城市';
        }
    }

    // 显示下拉框
    function showModalDropdown(dropdown) {
        dropdown.classList.add('show');
    }

    // 隐藏下拉框
    function hideModalDropdown(dropdown) {
        dropdown.classList.remove('show');
    }

    // 点击页面其他地方时隐藏所有下拉框
    document.addEventListener('click', function(e) {
        if (!provinceInput.contains(e.target) && !provinceDropdown.contains(e.target)) {
            hideModalDropdown(provinceDropdown);
        }
        if (!cityInput.contains(e.target) && !cityDropdown.contains(e.target)) {
            hideModalDropdown(cityDropdown);
        }
    });

}



</script>
{% endblock %}
