# -*- coding: utf-8 -*-
from flask import render_template, request, jsonify, current_app
from flask_login import current_user
from datetime import datetime, timedelta, timezone
from sqlalchemy import func, and_
from models import db, User, Company, Department, Lead, LeadStatusHistory, LeadTransferHistory
import json
import logging

def get_utc_now():
    """获取当前timezone.utc时间"""
    return datetime.now(timezone.utc)

def mask_phone_number(phone):
    """将电话号码中间部分用星号代替，保留前3位和后3位"""
    if not phone or len(phone) < 7: return phone
    return phone[:3] + '*' * (len(phone) - 6) + phone[-3:]

def get_dashboard_data():
    """获取仪表盘数据"""
    app = current_app
    app.logger.info('开始加载仪表盘')
    app.logger.info(f'当前用户: {current_user.username}, 角色: {current_user.role}')
    app.logger.info(f'用户角色代码: {current_user.role.code}')
    
    # 获取筛选参数
    company_id = request.args.get('company_id', type=int)
    department_id = request.args.get('department_id', type=int)
    user_id = request.args.get('user_id', type=int)
    
    # 获取日期筛选参数
    today = datetime.now().date()
    default_start_date = (today - timedelta(days=30)).strftime('%Y-%m-%d')  # 默认30天前
    default_end_date = today.strftime('%Y-%m-%d')  # 默认今天
    
    start_date_str = request.args.get('start_date', default_start_date)
    end_date_str = request.args.get('end_date', default_end_date)
    
    # 解析日期字符串为datetime对象
    try:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
        # 结束日期加上一天减去1秒，使其包含当天的所有时间
        end_date = end_date.replace(hour=23, minute=59, second=59)
    except ValueError:
        # 日期格式错误时，使用默认值
        start_date = today - timedelta(days=30)
        end_date = today.replace(hour=23, minute=59, second=59)
    
    stage = request.args.get('stage')
    sort_by = request.args.get('sort_by', 'total')  # 默认按总数排序
    sort_order = request.args.get('sort_order', 'desc')  # 默认降序
    view_mode = request.args.get('view_mode', 'department')  # 默认显示部门排名
    page = request.args.get('page', 1, type=int)
    
    # 构建基础查询
    base_query = Lead.query
    
    # 初始化部门统计数据列表
    department_stats = []
    company_stats = []
    
    # 获取用户放出线索的统计数据
    user_released_leads_query = db.session.query(Lead).distinct(Lead.id).\
                              join(LeadTransferHistory, Lead.id == LeadTransferHistory.lead_id).\
                              filter(LeadTransferHistory.from_user_id == current_user.id, 
                                   LeadTransferHistory.transfer_type == 'release')
    
    # 应用与其他查询相同的过滤条件
    user_released_leads_query = user_released_leads_query.filter(
        and_(
            Lead.owner_id.isnot(None),  # 有负责人的线索
            Lead.is_in_public_sea == False,  # 不在公海中
            Lead.pool_id.isnot(None)  # 确保有线索池
        )
    )
    
    # 获取已被认领的线索（使用子查询确保每条线索只统计一次）
    claimed_leads = db.session.query(Lead).distinct(Lead.id).\
                   join(LeadTransferHistory, Lead.id == LeadTransferHistory.lead_id).\
                   filter(LeadTransferHistory.from_user_id == current_user.id,
                         LeadTransferHistory.transfer_type == 'release',
                         Lead.owner_id != None)
    
    # 应用与其他查询相同的过滤条件
    claimed_leads = claimed_leads.filter(
        and_(
            Lead.is_in_public_sea == False,  # 不在公海中
            Lead.pool_id.isnot(None)  # 确保有线索池
        )
    ).count()
    
    # 获取签约的线索（使用子查询确保每条线索只统计一次）
    deal_done_leads_query = db.session.query(Lead).distinct(Lead.id).\
                     join(LeadTransferHistory, Lead.id == LeadTransferHistory.lead_id).\
                     filter(LeadTransferHistory.from_user_id == current_user.id,
                           LeadTransferHistory.transfer_type == 'release',
                           Lead.is_deal_done == True)
    
    # 应用与其他查询相同的过滤条件                       
    deal_done_leads_query = deal_done_leads_query.filter(
        and_(
            Lead.owner_id.isnot(None),  # 有负责人的线索
            Lead.is_in_public_sea == False,  # 不在公海中
            Lead.pool_id.isnot(None)  # 确保有线索池
        )
    )
    deal_done_leads = deal_done_leads_query.count()
    
    released_leads = {
        'total': user_released_leads_query.count(),  # 实际释放的不重复线索数
        'claimed': claimed_leads,  # 实际被认领的不重复线索数
        'deal_done': deal_done_leads  # 实际签约的不重复线索数
    }
    
    app.logger.info(f'释放线索统计: 总数={released_leads["total"]}, 被认领={released_leads["claimed"]}, 签约={released_leads["deal_done"]}')
    
    # 根据用户角色限制查询范围
    if current_user.role.code == 'super_admin':
        app.logger.info('超级管理员权限，获取所有公司统计')
        # 如果指定了公司，则只查看该公司数据
        if company_id:
            base_query = base_query.join(User, Lead.owner_id == User.id).filter(User.company_id == company_id)
            # 如果还指定了部门，则进一步筛选
            if department_id:
                base_query = base_query.filter(User.department_id == department_id)
        # 如果指定了用户，则只查看该用户数据
        if user_id:
            base_query = base_query.filter(Lead.owner_id == user_id)
        
        # 获取所有公司列表（用于筛选）
        companies = Company.query.all()
        # 获取部门列表（基于选择的公司）
        departments = Department.query.filter_by(company_id=company_id).all() if company_id else []
        # 获取用户列表（基于选择的部门）
        if department_id:
            users = User.query.filter_by(department_id=department_id).all()
        elif company_id:
            users = User.query.filter_by(company_id=company_id).all()
        else:
            users = User.query.all()
    
    elif current_user.role.code == 'company_admin':
        app.logger.info('公司管理员权限，获取本公司统计')
        # 限制只能查看自己公司的数据
        base_query = base_query.join(User, Lead.owner_id == User.id).filter(User.company_id == current_user.company_id)
        # 如果指定了部门，则进一步筛选
        if department_id:
            base_query = base_query.filter(User.department_id == department_id)
        # 如果指定了用户，则只查看该用户数据
        if user_id:
            base_query = base_query.filter(Lead.owner_id == user_id)
        
        # 获取公司列表（只有自己的公司）
        companies = [current_user.company]
        # 获取部门列表
        departments = Department.query.filter_by(company_id=current_user.company_id).all()
        # 获取用户列表
        if department_id:
            users = User.query.filter_by(company_id=current_user.company_id, department_id=department_id).all()
        else:
            users = User.query.filter_by(company_id=current_user.company_id).all()
    
    elif current_user.role.code == 'department_admin':
        app.logger.info('部门管理员权限，获取本部门统计')
        # 限制只能查看自己部门的数据
        base_query = base_query.join(User, Lead.owner_id == User.id).filter(User.department_id == current_user.department_id)
        # 如果指定了用户，则只查看该用户数据
        if user_id:
            base_query = base_query.filter(Lead.owner_id == user_id)
        
        # 获取公司列表（只有自己的公司）
        companies = [current_user.company]
        # 获取部门列表（只有自己的部门）
        departments = [current_user.department]
        # 获取用户列表（部门内的用户）
        users = User.query.filter_by(department_id=current_user.department_id).all()
    
    else:
        app.logger.info('普通用户权限，只获取个人统计')
        # 普通用户只能查看自己的数据
        base_query = base_query.filter(Lead.owner_id == current_user.id)
        
        # 获取公司列表（只有自己的公司）
        companies = [current_user.company]
        # 获取部门列表（只有自己的部门）
        departments = [current_user.department]
        # 获取用户列表（只有自己）
        users = [current_user]
    
    # 排除公海线索
    base_query = base_query.filter(
        and_(
            Lead.owner_id.isnot(None),  # 有负责人的线索
            Lead.is_in_public_sea == False,  # 不在公海中
            Lead.pool_id.isnot(None)  # 确保有线索池
        )
    )
    
    # 对于非超级管理员，限制只能查看自己公司的线索
    if current_user.role.code != 'super_admin':
        base_query = base_query.filter(Lead.company_id == current_user.company_id)  # 属于当前用户所在公司
        app.logger.info('过滤条件: owner_id.isnot(None), is_in_public_sea=False, pool_id.isnot(None), company_id=当前用户公司')
    else:
        app.logger.info('过滤条件: owner_id.isnot(None), is_in_public_sea=False, pool_id.isnot(None)')
    
    # 获取用户可见的总线索数
    total_leads = base_query.count()
    app.logger.info(f'用户可见总线索数: {total_leads}')
    
    # 获取漏斗阶段统计
    funnel_stats = {
        'called': base_query.filter(Lead.is_called == True).count(),
        'connected': base_query.filter(Lead.is_connected == True).count(),
        'valid_call': base_query.filter(Lead.is_valid_call == True).count(),
        'wechat_added': base_query.filter(Lead.is_wechat_added == True).count(),
        'intentional': base_query.filter(Lead.is_intentional == True).count(),
        'compliant': base_query.filter(Lead.is_compliant == True).count(),
        'visited': base_query.filter(Lead.is_visited == True).count(),
        'car_selected': base_query.filter(Lead.is_car_selected == True).count(),
        'deal_done': base_query.filter(Lead.is_deal_done == True).count()
    }
    
    # 获取每日新增线索统计
    try:
        # 使用与base_query相同的过滤条件，而不是直接查询所有线索
        daily_stats_query = db.session.query(
            func.date(Lead.created_at).label('date'),
            func.count(Lead.id).label('count')
        )
        
        # 应用与base_query相同的过滤条件
        daily_stats_query = daily_stats_query.filter(
            and_(
                Lead.owner_id.isnot(None),  # 有负责人的线索
                Lead.is_in_public_sea == False,  # 不在公海中
                Lead.pool_id.isnot(None),  # 确保有线索池
                Lead.created_at.between(start_date, end_date)  # 日期范围
            )
        )
        
        # 对于非超级管理员，限制只能查看自己公司的线索
        if current_user.role.code != 'super_admin':
            daily_stats_query = daily_stats_query.filter(Lead.company_id == current_user.company_id)
        
        # 根据用户角色添加额外过滤（与base_query保持一致）
        if current_user.role.code == 'super_admin':
            if company_id:
                daily_stats_query = daily_stats_query.join(User, Lead.owner_id == User.id).filter(User.company_id == company_id)
                if department_id:
                    daily_stats_query = daily_stats_query.filter(User.department_id == department_id)
            if user_id:
                daily_stats_query = daily_stats_query.filter(Lead.owner_id == user_id)
        elif current_user.role.code == 'company_admin':
            daily_stats_query = daily_stats_query.join(User, Lead.owner_id == User.id).filter(User.company_id == current_user.company_id)
            if department_id:
                daily_stats_query = daily_stats_query.filter(User.department_id == department_id)
            if user_id:
                daily_stats_query = daily_stats_query.filter(Lead.owner_id == user_id)
        elif current_user.role.code == 'department_admin':
            daily_stats_query = daily_stats_query.join(User, Lead.owner_id == User.id).filter(User.department_id == current_user.department_id)
            if user_id:
                daily_stats_query = daily_stats_query.filter(Lead.owner_id == user_id)
        else:
            daily_stats_query = daily_stats_query.filter(Lead.owner_id == current_user.id)
        
        # 执行查询
        daily_stats = daily_stats_query.group_by(func.date(Lead.created_at)).all()
        
        # 确保daily_stats.date是日期对象
        daily_dates = []
        daily_counts = []
        for stat in daily_stats:
            try:
                if isinstance(stat.date, str):
                    # 如果是字符串，尝试转换为日期对象
                    date_obj = datetime.strptime(stat.date, '%Y-%m-%d').date()
                    daily_dates.append(date_obj.strftime('%Y-%m-%d'))
                else:
                    # 如果已经是日期对象，直接格式化
                    daily_dates.append(stat.date.strftime('%Y-%m-%d'))
                daily_counts.append(stat.count)
            except Exception as e:
                app.logger.error(f"处理日期时出错: {str(e)}, 日期类型: {type(stat.date)}")
                # 跳过这条记录
                continue
    except Exception as e:
        app.logger.error(f"获取每日统计时出错: {str(e)}")
        daily_dates = []
        daily_counts = []
        daily_stats = []  # 确保daily_stats在异常情况下也有定义
    
    # 如果daily_dates和daily_counts已经在try块中填充，就不需要再次定义
    if not daily_dates and daily_stats:  # 只有当daily_dates为空但daily_stats有值时才需要重新处理
        # 保存原始日期对象用于后续格式化
        daily_dates_obj = [stat.date for stat in daily_stats]
        # 格式化为YYYY-MM-DD字符串用于显示
        daily_dates = [stat.date.strftime('%Y-%m-%d') for stat in daily_stats]
        daily_counts = [stat.count for stat in daily_stats]
    
    # 获取公司和部门统计
    company_stats = []
    if current_user.role.code in ['super_admin', 'company_admin']:
        # 确定要统计的公司列表
        target_companies = companies
        app.logger.info(f'获取到的公司数量: {len(target_companies)}')
        
        for company in target_companies:
            app.logger.info(f'处理公司统计: {company.name}')
            
            # 确定要统计的部门列表
            if current_user.role.code == 'super_admin' and department_id:
                target_departments = [dept for dept in company.departments if dept.id == department_id]
            else:
                target_departments = company.departments
            
            app.logger.info(f'获取到的部门数量: {len(target_departments)}')
            
            # 计算公司总体统计
            company_query = Lead.query.join(User, Lead.owner_id == User.id).filter(User.company_id == company.id)
            # 排除公海线索，并确保其他条件一致
            company_query = company_query.filter(
                and_(
                    Lead.owner_id.isnot(None),  # 有负责人的线索
                    Lead.is_in_public_sea == False,  # 不在公海中
                    Lead.pool_id.isnot(None),  # 确保有线索池
                    Lead.company_id == company.id  # 确保线索属于当前公司
                )
            )
            company_total = company_query.count()
            company_called = company_query.filter(Lead.is_called == True).count()
            company_valid_call = company_query.filter(Lead.is_valid_call == True).count()
            company_wechat_added = company_query.filter(Lead.is_wechat_added == True).count()
            company_intentional = company_query.filter(Lead.is_intentional == True).count()
            company_compliant = company_query.filter(Lead.is_compliant == True).count()
            company_visited = company_query.filter(Lead.is_visited == True).count()
            company_car_selected = company_query.filter(Lead.is_car_selected == True).count()
            company_deal_done = company_query.filter(Lead.is_deal_done == True).count()
            
            # 计算公司转化率
            company_conversion_rate = (company_deal_done / company_total * 100) if company_total > 0 else 0
            
            # 部门统计
            dept_stats = []
            for dept in target_departments:
                app.logger.info(f'处理部门统计: {dept.name}')
                dept_query = Lead.query.join(User, Lead.owner_id == User.id).filter(User.department_id == dept.id)
                # 排除公海线索，并确保其他条件一致
                dept_query = dept_query.filter(
                    and_(
                        Lead.owner_id.isnot(None),  # 有负责人的线索
                        Lead.is_in_public_sea == False,  # 不在公海中
                        Lead.pool_id.isnot(None),  # 确保有线索池
                        Lead.company_id == company.id  # 确保线索属于当前公司
                    )
                )
                
                dept_total = dept_query.count()
                dept_called = dept_query.filter(Lead.is_called == True).count()
                dept_valid_call = dept_query.filter(Lead.is_valid_call == True).count()
                dept_wechat_added = dept_query.filter(Lead.is_wechat_added == True).count()
                dept_intentional = dept_query.filter(Lead.is_intentional == True).count()
                dept_compliant = dept_query.filter(Lead.is_compliant == True).count()
                dept_visited = dept_query.filter(Lead.is_visited == True).count()
                dept_car_selected = dept_query.filter(Lead.is_car_selected == True).count()
                dept_deal_done = dept_query.filter(Lead.is_deal_done == True).count()
                
                # 计算部门转化率
                dept_conversion_rate = (dept_deal_done / dept_total * 100) if dept_total > 0 else 0
                
                dept_stats.append({
                    'id': dept.id,
                    'name': dept.name,
                    'total': dept_total,
                    'called': dept_called,
                    'valid_call': dept_valid_call,
                    'wechat_added': dept_wechat_added,
                    'intentional': dept_intentional,
                    'compliant': dept_compliant,
                    'visited': dept_visited,
                    'car_selected': dept_car_selected,
                    'deal_done': dept_deal_done,
                    'conversion_rate': dept_conversion_rate,
                    'deal_rate': (dept_deal_done / dept_total) if dept_total > 0 else 0  # 添加deal_rate字段用于排序
                })
            
            company_stats.append({
                'id': company.id,
                'name': company.name,
                'total': company_total,
                'called': company_called,
                'valid_call': company_valid_call,
                'wechat_added': company_wechat_added,
                'intentional': company_intentional,
                'compliant': company_compliant,
                'visited': company_visited,
                'car_selected': company_car_selected,
                'deal_done': company_deal_done,
                'conversion_rate': company_conversion_rate,
                'departments': dept_stats
            })
    
    # 获取用户统计
    user_stats = []
    if current_user.role.code in ['super_admin', 'company_admin', 'department_admin']:
        for user in users:
            app.logger.info(f'处理用户统计: {user.username}')
            user_query = base_query.filter(Lead.owner_id == user.id)
            
            user_total = user_query.count()
            user_called = user_query.filter(Lead.is_called == True).count()
            user_valid_call = user_query.filter(Lead.is_valid_call == True).count()
            user_wechat_added = user_query.filter(Lead.is_wechat_added == True).count()
            user_intentional = user_query.filter(Lead.is_intentional == True).count()
            user_compliant = user_query.filter(Lead.is_compliant == True).count()
            user_visited = user_query.filter(Lead.is_visited == True).count()
            user_car_selected = user_query.filter(Lead.is_car_selected == True).count()
            user_deal_done = user_query.filter(Lead.is_deal_done == True).count()
            
            # 计算用户转化率
            user_conversion_rate = (user_deal_done / user_total * 100) if user_total > 0 else 0
            
            user_stats.append({
                'id': user.id,
                'name': user.name or user.username,
                'username': user.username,
                'department': user.department.name if user.department else '',
                'total': user_total,
                'called': user_called,
                'valid_call': user_valid_call,
                'wechat_added': user_wechat_added,
                'intentional': user_intentional,
                'compliant': user_compliant,
                'visited': user_visited,
                'car_selected': user_car_selected,
                'deal_done': user_deal_done,
                'conversion_rate': user_conversion_rate
            })
        
        # 根据排序条件对用户进行排序
        if sort_by in ['total', 'called', 'connected', 'valid_call', 'wechat_added', 
                      'intentional', 'compliant', 'visited', 'car_selected', 'deal_done', 'conversion_rate']:
            reverse = sort_order == 'desc'
            user_stats.sort(key=lambda x: x[sort_by], reverse=reverse)
    
    # 构建线索列表查询（添加筛选条件）
    leads_query = base_query
    
    # 根据视图模式和排序条件对数据进行排序
    reverse = sort_order == 'desc'
    
    # 对部门统计数据排序
    if view_mode == 'department' and department_stats and sort_by in ['total', 'deal_done', 'conversion_rate']:
        # 如果是部门视图，且部门数据不为空
        if sort_by == 'conversion_rate':
            # 使用deal_rate字段进行排序
            department_stats.sort(key=lambda x: x['deal_rate'], reverse=reverse)
        else:
            # 使用total或deal_done字段排序
            department_stats.sort(key=lambda x: x[sort_by], reverse=reverse)
    
    # 对公司统计数据排序
    if view_mode == 'company' and company_stats and sort_by in ['total', 'deal_done', 'conversion_rate']:
        # 如果是公司视图，且公司数据不为空
        company_stats.sort(key=lambda x: x[sort_by], reverse=reverse)
    
    # 对用户统计数据排序
    if view_mode == 'user' and user_stats and sort_by in ['total', 'called', 'connected', 'valid_call', 'wechat_added', 
                  'intentional', 'compliant', 'visited', 'car_selected', 'deal_done', 'conversion_rate']:
        user_stats.sort(key=lambda x: x[sort_by], reverse=reverse)
    
    # 计算分页数量（只用于部门/公司/用户统计的分页展示）
    items_per_page = 10
    items_count = 0
    if view_mode == 'department':
        items_count = len(department_stats)
    elif view_mode == 'company':
        items_count = len(company_stats)
    elif view_mode == 'user':
        items_count = len(user_stats)
    
    pages = (items_count + items_per_page - 1) // items_per_page if items_count > 0 else 1
    
    app.logger.info('所有统计数据获取完成，准备渲染模板')
    
    try:
        # 初始化source_labels和source_counts变量，确保在任何情况下都能访问
        source_labels = []
        source_counts = []
        
        # 计算增长率和其他统计数据
        # 上月数据
        last_month_start = start_date - timedelta(days=30)
        last_month_end = start_date
        last_month_query = base_query.filter(Lead.created_at.between(last_month_start, last_month_end))
        last_month_total = last_month_query.count()
        
        # 计算增长率
        total_leads_growth = ((total_leads - last_month_total) / last_month_total) if last_month_total > 0 else 0
        
        # 本月新增
        current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        new_leads_this_month = base_query.filter(Lead.created_at >= current_month_start).count()
        
        # 上月同期新增
        last_month_same_period = base_query.filter(
            Lead.created_at >= current_month_start - timedelta(days=30),
            Lead.created_at < current_month_start
        ).count()
        
        # 新增增长率
        new_leads_growth = ((new_leads_this_month - last_month_same_period) / last_month_same_period) if last_month_same_period > 0 else 0
        
        # 平均转化时间（从创建到签约的天数）
        deal_done_leads_list = base_query.filter(Lead.is_deal_done == True).all()
        conversion_days = []
        for lead in deal_done_leads_list:
            # 查找状态变更历史
            deal_done_history = LeadStatusHistory.query.filter_by(
                lead_id=lead.id, 
                status_field='is_deal_done', 
                new_value=True  # 修改这里，使用布尔值True而不是字符串'True'
            ).first()
            
            if deal_done_history:
                days = (deal_done_history.created_at - lead.created_at).days
                if days >= 0:  # 确保天数有效
                    conversion_days.append(days)
        
        avg_conversion_days = sum(conversion_days) / len(conversion_days) if conversion_days else 0
        
        # 上月平均转化时间
        last_month_deal_done = last_month_query.filter(Lead.is_deal_done == True).all()
        last_month_days = []
        for lead in last_month_deal_done:
            deal_done_history = LeadStatusHistory.query.filter_by(
                lead_id=lead.id, 
                status_field='is_deal_done', 
                new_value=True  # 修改这里，使用布尔值True而不是字符串'True'
            ).first()
            
            if deal_done_history:
                days = (deal_done_history.created_at - lead.created_at).days
                if days >= 0:
                    last_month_days.append(days)
        
        last_month_avg_days = sum(last_month_days) / len(last_month_days) if last_month_days else 0
        avg_days_change = avg_conversion_days - last_month_avg_days
        avg_days_improved = avg_conversion_days <= last_month_avg_days
        
        # 漏斗阶段数据
        called_leads = base_query.filter(Lead.is_called == True).count()
        valid_call_leads = base_query.filter(Lead.is_valid_call == True).count()
        wechat_added_leads = base_query.filter(Lead.is_wechat_added == True).count()
        intentional_leads = base_query.filter(Lead.is_intentional == True).count()
        compliant_leads = base_query.filter(Lead.is_compliant == True).count()
        visited_leads = base_query.filter(Lead.is_visited == True).count()
        car_selected_leads = base_query.filter(Lead.is_car_selected == True).count()
        deal_done_leads = base_query.filter(Lead.is_deal_done == True).count()
        
        # 计算转化率
        called_rate = called_leads / total_leads if total_leads > 0 else 0
        valid_call_rate = valid_call_leads / total_leads if total_leads > 0 else 0
        wechat_added_rate = wechat_added_leads / total_leads if total_leads > 0 else 0
        intentional_rate = intentional_leads / total_leads if total_leads > 0 else 0
        compliant_rate = compliant_leads / total_leads if total_leads > 0 else 0
        visited_rate = visited_leads / total_leads if total_leads > 0 else 0
        car_selected_rate = car_selected_leads / total_leads if total_leads > 0 else 0
        deal_rate = deal_done_leads / total_leads if total_leads > 0 else 0
        
        # 准备图表数据 - 使用MM-DD格式的日期标签
        # 直接使用已经格式化好的日期字符串，不再尝试对字符串调用strftime
        daily_leads_labels = [d[-5:] for d in daily_dates] if daily_dates else []  # 提取MM-DD部分
        daily_leads_data = daily_counts if daily_counts else []
        
        # 将列表转换为JSON字符串，以便在JavaScript中使用
        daily_leads_labels_json = json.dumps(daily_leads_labels)
        daily_leads_data_json = json.dumps(daily_leads_data)
        # 确保source_labels和source_counts已经定义
        if 'source_labels' not in locals() or not source_labels:
            source_labels = []
        if 'source_counts' not in locals() or not source_counts:
            source_counts = []
        source_labels_json = json.dumps(source_labels)
        source_counts_json = json.dumps(source_counts)
        
        # 从公司统计中提取部门统计数据(如果之前还没有构建)
        if not department_stats:
            for dept_stat in company_stats:
                if 'departments' in dept_stat:
                    for dept in dept_stat['departments']:
                        department_stats.append({
                            'name': dept['name'],
                            'total': dept['total'],
                            'called': dept['called'],
                            'valid_call': dept['valid_call'],
                            'wechat_added': dept['wechat_added'],
                            'intentional': dept['intentional'],
                            'compliant': dept['compliant'],
                            'visited': dept['visited'],
                            'car_selected': dept['car_selected'],
                            'deal_done': dept['deal_done'],
                            'conversion_rate': dept['conversion_rate'],
                            'deal_rate': dept['deal_rate']
                        })
            
            # 对部门统计数据排序(如果之前没有排序)
            if department_stats and sort_by in ['total', 'deal_done', 'conversion_rate']:
                reverse = sort_order == 'desc'
                if sort_by == 'conversion_rate':
                    department_stats.sort(key=lambda x: x['deal_rate'], reverse=reverse)
                else:
                    department_stats.sort(key=lambda x: x[sort_by], reverse=reverse)
        
        # 确保分页信息存在
        page_count = 0
        
        # 将日期和计数转换为JSON安全的格式
        daily_leads_labels = json.dumps(daily_dates)
        daily_leads_data = json.dumps(daily_counts)
        
        # 获取线索来源分布
        try:
            # 使用与base_query相同的过滤条件
            source_stats_query = db.session.query(
                Lead.source,
                func.count(Lead.id).label('count')
            )
            
            # 应用与base_query相同的过滤条件
            source_stats_query = source_stats_query.filter(
                and_(
                    Lead.owner_id.isnot(None),  # 有负责人的线索
                    Lead.is_in_public_sea == False,  # 不在公海中
                    Lead.pool_id.isnot(None),  # 确保有线索池
                    Lead.created_at.between(start_date, end_date)  # 日期范围
                )
            )
            
            # 对于非超级管理员，限制只能查看自己公司的线索
            if current_user.role.code != 'super_admin':
                source_stats_query = source_stats_query.filter(Lead.company_id == current_user.company_id)
            
            # 根据用户角色添加额外过滤（与base_query保持一致）
            if current_user.role.code == 'super_admin':
                if company_id:
                    source_stats_query = source_stats_query.join(User, Lead.owner_id == User.id).filter(User.company_id == company_id)
                    if department_id:
                        source_stats_query = source_stats_query.filter(User.department_id == department_id)
                if user_id:
                    source_stats_query = source_stats_query.filter(Lead.owner_id == user_id)
            elif current_user.role.code == 'company_admin':
                source_stats_query = source_stats_query.join(User, Lead.owner_id == User.id).filter(User.company_id == current_user.company_id)
                if department_id:
                    source_stats_query = source_stats_query.filter(User.department_id == department_id)
                if user_id:
                    source_stats_query = source_stats_query.filter(Lead.owner_id == user_id)
            elif current_user.role.code == 'department_admin':
                source_stats_query = source_stats_query.join(User, Lead.owner_id == User.id).filter(User.department_id == current_user.department_id)
                if user_id:
                    source_stats_query = source_stats_query.filter(Lead.owner_id == user_id)
            else:
                source_stats_query = source_stats_query.filter(Lead.owner_id == current_user.id)
            
            # 执行查询
            source_stats = source_stats_query.group_by(Lead.source).all()
            
            source_labels = json.dumps([stat.source for stat in source_stats])
            source_counts = json.dumps([stat.count for stat in source_stats])
        except Exception as e:
            app.logger.error(f"获取线索来源分布时出错: {str(e)}")
            source_labels = json.dumps([])
            source_counts = json.dumps([])
        
        return {
            'total_leads': total_leads,
            'total_leads_growth': total_leads_growth,
            'new_leads_this_month': new_leads_this_month,
            'new_leads_growth': new_leads_growth,
            'avg_conversion_days': avg_conversion_days,
            'avg_days_improved': avg_days_improved,
            'avg_days_change': avg_days_change,
            'called_leads': called_leads,
            'valid_call_leads': valid_call_leads,
            'wechat_added_leads': wechat_added_leads,
            'intentional_leads': intentional_leads,
            'compliant_leads': compliant_leads,
            'visited_leads': visited_leads,
            'car_selected_leads': car_selected_leads,
            'deal_done_leads': deal_done_leads,
            'called_rate': called_rate,
            'valid_call_rate': valid_call_rate,
            'wechat_added_rate': wechat_added_rate,
            'intentional_rate': intentional_rate,
            'compliant_rate': compliant_rate,
            'visited_rate': visited_rate,
            'car_selected_rate': car_selected_rate,
            'deal_rate': deal_rate,
            'daily_leads_labels': daily_leads_labels,
            'daily_leads_data': daily_leads_data,
            'source_labels': source_labels,
            'source_counts': source_counts,
            'company_stats': company_stats,
            'department_stats': department_stats,
            'user_stats': user_stats,
            'pages': page_count,
            'page': page,
            'companies': companies,
            'departments': departments,
            'users': users,
            'selected_company_id': company_id,
            'selected_department_id': department_id,
            'selected_user_id': user_id,
            'default_start_date': default_start_date,
            'default_end_date': default_end_date,
            'selected_start_date': start_date_str,
            'selected_end_date': end_date_str,
            'selected_stage': stage,
            'sort_by': sort_by,
            'sort_order': sort_order,
            'view_mode': view_mode,
            'released_leads': released_leads
        }
    except Exception as e:
        app.logger.error(f"仪表盘数据计算出错: {str(e)}")
        # 提供简化版仪表盘
        daily_leads_labels = json.dumps([])
        daily_leads_data = json.dumps([])
        source_labels = json.dumps([])
        source_counts = json.dumps([])
        
        # 创建基本的公司和部门统计数据
        basic_company_stats = []
        basic_department_stats = []
        
        try:
            # 获取基本的部门统计数据
            if current_user.role.code in ['super_admin', 'company_admin']:
                for company in companies:
                    dept_stats = []
                    for dept in company.departments:
                        dept_query = Lead.query.join(User, Lead.owner_id == User.id).filter(User.department_id == dept.id)
                        dept_total = dept_query.count()
                        dept_deal_done = dept_query.filter(Lead.is_deal_done == True).count()
                        
                        dept_stat = {
                            'name': dept.name,
                            'total': dept_total,
                            'called': dept_query.filter(Lead.is_called == True).count(),
                            'valid_call': dept_query.filter(Lead.is_valid_call == True).count(),
                            'wechat_added': dept_query.filter(Lead.is_wechat_added == True).count(),
                            'intentional': dept_query.filter(Lead.is_intentional == True).count(),
                            'compliant': dept_query.filter(Lead.is_compliant == True).count(),
                            'visited': dept_query.filter(Lead.is_visited == True).count(),
                            'car_selected': dept_query.filter(Lead.is_car_selected == True).count(),
                            'deal_done': dept_deal_done,
                            'deal_rate': dept_deal_done / dept_total if dept_total > 0 else 0
                        }
                        dept_stats.append(dept_stat)
                        basic_department_stats.append(dept_stat)
                    
                    company_query = Lead.query.join(User, Lead.owner_id == User.id).filter(User.company_id == company.id)
                    company_total = company_query.count()
                    company_deal_done = company_query.filter(Lead.is_deal_done == True).count()
                    
                    basic_company_stats.append({
                        'id': company.id,
                        'name': company.name,
                        'total': company_total,
                        'called': company_query.filter(Lead.is_called == True).count(),
                        'valid_call': company_query.filter(Lead.is_valid_call == True).count(),
                        'wechat_added': company_query.filter(Lead.is_wechat_added == True).count(),
                        'intentional': company_query.filter(Lead.is_intentional == True).count(),
                        'compliant': company_query.filter(Lead.is_compliant == True).count(),
                        'visited': company_query.filter(Lead.is_visited == True).count(),
                        'car_selected': company_query.filter(Lead.is_car_selected == True).count(),
                        'deal_done': company_deal_done,
                        'conversion_rate': company_deal_done / company_total if company_total > 0 else 0,
                        'departments': dept_stats
                    })
        except Exception as inner_e:
            app.logger.error(f"生成基本统计数据时出错: {str(inner_e)}")
            # 如果生成基本统计数据也失败，使用空列表
            basic_company_stats = []
            basic_department_stats = []
        
        # 确保分页信息存在
        page_count = 0
        
        return {
            'total_leads': total_leads,
            'total_leads_growth': 0,
            'new_leads_this_month': 0,
            'new_leads_growth': 0,
            'avg_conversion_days': 0,
            'avg_days_improved': False,
            'avg_days_change': 0,
            'called_leads': funnel_stats['called'] if 'called' in funnel_stats else 0,
            'valid_call_leads': funnel_stats['valid_call'] if 'valid_call' in funnel_stats else 0,
            'wechat_added_leads': funnel_stats['wechat_added'] if 'wechat_added' in funnel_stats else 0,
            'intentional_leads': funnel_stats['intentional'] if 'intentional' in funnel_stats else 0,
            'compliant_leads': funnel_stats['compliant'] if 'compliant' in funnel_stats else 0,
            'visited_leads': funnel_stats['visited'] if 'visited' in funnel_stats else 0,
            'car_selected_leads': funnel_stats['car_selected'] if 'car_selected' in funnel_stats else 0,
            'deal_done_leads': funnel_stats['deal_done'] if 'deal_done' in funnel_stats else 0,
            'called_rate': funnel_stats['called'] / total_leads if total_leads > 0 else 0,
            'valid_call_rate': funnel_stats['valid_call'] / total_leads if total_leads > 0 else 0,
            'wechat_added_rate': funnel_stats['wechat_added'] / total_leads if total_leads > 0 else 0,
            'intentional_rate': funnel_stats['intentional'] / total_leads if total_leads > 0 else 0,
            'compliant_rate': funnel_stats['compliant'] / total_leads if total_leads > 0 else 0,
            'visited_rate': funnel_stats['visited'] / total_leads if total_leads > 0 else 0,
            'car_selected_rate': funnel_stats['car_selected'] / total_leads if total_leads > 0 else 0,
            'deal_rate': funnel_stats['deal_done'] / total_leads if total_leads > 0 else 0,
            'daily_leads_labels': daily_leads_labels,
            'daily_leads_data': daily_leads_data,
            'source_labels': source_labels,
            'source_counts': source_counts,
            'company_stats': basic_company_stats,
            'department_stats': basic_department_stats,
            'user_stats': [],
            'pages': page_count,
            'page': page,
            'companies': companies,
            'departments': departments,
            'users': users,
            'selected_company_id': company_id,
            'selected_department_id': department_id,
            'selected_user_id': user_id,
            'default_start_date': default_start_date,
            'default_end_date': default_end_date,
            'selected_start_date': start_date_str,
            'selected_end_date': end_date_str,
            'selected_stage': stage,
            'sort_by': sort_by,
            'sort_order': sort_order,
            'view_mode': view_mode,
            'released_leads': {'total': 0, 'claimed': 0, 'deal_done': 0}
        } 