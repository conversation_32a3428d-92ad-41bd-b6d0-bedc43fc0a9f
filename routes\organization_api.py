from flask import Blueprint, jsonify, request, current_app
from models import db, Company, Department, User, Role
from flask_login import login_required, current_user

bp = Blueprint('organization_api', __name__)

@bp.route('/companies')
@login_required
def get_companies():
    try:
        companies = Company.query.all()
        return jsonify([{
            'id': company.id,
            'name': company.name,
            'code': company.code,
            'type': company.type
        } for company in companies])
    except Exception as e:
        current_app.logger.error(f'获取公司列表失败: {str(e)}')
        return jsonify({'error': '获取公司列表失败'}), 500

@bp.route('/companies/search')
@login_required
def search_companies():
    try:
        query = request.args.get('q', '')
        if not query or len(query) < 2:
            return jsonify([])

        # 使用ILIKE进行不区分大小写的模糊匹配，并排除当前用户所在公司
        companies = Company.query.filter(
            Company.name.ilike(f'%{query}%'),
            Company.id != current_user.company_id
        ).limit(10).all()

        # 直接返回数组格式，与前端期望的格式一致
        return jsonify([{
            'id': company.id,
            'name': company.name,
            'code': company.code
        } for company in companies])

    except Exception as e:
        current_app.logger.error(f'搜索公司时出错: {str(e)}')
        return jsonify({'error': '搜索公司失败'}), 500

@bp.route('/companies/<int:company_id>/departments')
@login_required
def get_company_departments(company_id):
    try:
        # 记录请求信息
        current_app.logger.info(f'用户 {current_user.username} (角色: {current_user.role.code}, 公司ID: {current_user.company_id}) 请求公司 {company_id} 的部门信息')

        # 检查权限 - 超级管理员可以访问所有公司，公司管理员可以访问自己的公司
        if current_user.role.code == 'super_admin':
            # 超级管理员可以访问所有公司
            pass
        elif current_user.role.code == 'company_admin' and current_user.company_id == company_id:
            # 公司管理员可以访问自己的公司
            pass
        elif current_user.role.code in ['department_admin', 'employee'] and current_user.company_id == company_id:
            # 部门管理员和员工可以访问自己公司的部门（用于查看）
            pass
        else:
            current_app.logger.warning(f'用户 {current_user.username} 尝试访问公司 {company_id} 的部门信息被拒绝')
            return jsonify({'error': '没有权限访问此公司的部门信息'}), 403

        departments = Department.query.filter_by(company_id=company_id).all()
        current_app.logger.info(f'为公司 {company_id} 找到 {len(departments)} 个部门')

        result = [{
            'id': dept.id,
            'name': dept.name,
            'code': dept.code
        } for dept in departments]

        return jsonify(result)
    except Exception as e:
        current_app.logger.error(f'获取公司 {company_id} 的部门列表失败: {str(e)}')
        return jsonify({'error': '获取部门列表失败'}), 500

@bp.route('/roles')
@login_required
def get_roles():
    try:
        # 根据用户角色过滤可见的角色列表
        if current_user.role.code == 'super_admin':
            roles = Role.query.all()
        else:
            roles = Role.query.filter(Role.code != 'super_admin').all()
        
        return jsonify([{
            'id': role.id,
            'name': role.name,
            'code': role.code
        } for role in roles])
    except Exception as e:
        current_app.logger.error(f'获取角色列表失败: {str(e)}')
        return jsonify({'error': '获取角色列表失败'}), 500

@bp.route('/users/<int:user_id>', methods=['PUT'])
@login_required
def update_user(user_id):
    try:
        user = User.query.get_or_404(user_id)
        
        # 检查权限
        if current_user.role.code != 'super_admin' and current_user.company_id != user.company_id:
            current_app.logger.warning(f'用户 {current_user.username} 尝试更新用户 {user_id} 的信息被拒绝')
            return jsonify({'error': '没有权限修改此用户'}), 403
        
        data = request.get_json()
        
        if 'email' in data:
            user.email = data['email']
        if 'company_id' in data:
            user.company_id = data['company_id']
        if 'department_id' in data:
            user.department_id = data['department_id']
        if 'role_id' in data:
            user.role_id = data['role_id']
        if 'password' in data and data['password']:
            user.set_password(data['password'])
        
        db.session.commit()
        return jsonify({'message': '用户更新成功'})
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'更新用户 {user_id} 失败: {str(e)}')
        return jsonify({'error': '更新用户失败'}), 500