from datetime import datetime, timezone
from sqlalchemy import or_
from models import db, Lead, LeadPool, Activity
from app import app
from utils.timezone_compat import get_shanghai_now

def move_leads_to_public_sea():
    """将超过保护期的线索移动到公海池"""
    try:
        with app.app_context():
            # 获取公海池
            public_sea = LeadPool.query.filter_by(is_public_sea=True).first()
            if not public_sea:
                app.logger.error('公海池不存在')
                return
            
            # 查找需要移动到公海的线索
            now = get_shanghai_now()
            leads_to_move = Lead.query.filter(
                or_(
                    # 已过保护期的线索
                    Lead.protection_end_time < now,
                    # 超过30天未跟进的线索
                    Lead.last_followup_time < now - datetime.timedelta(days=30)
                ),
                Lead.owner_id.isnot(None),  # 有负责人的线索
                Lead.is_in_public_sea == False,  # 不在公海中的线索
                Lead.deal_status == 'pending'  # 未签约的线索
            ).all()
            
            for lead in leads_to_move:
                # 记录原负责人
                lead.last_owner_id = lead.owner_id
                # 移除负责人
                lead.owner_id = None
                # 更新公海状态
                lead.is_in_public_sea = True
                lead.public_sea_time = now
                lead.pool_id = public_sea.id
                
                # 添加操作记录
                activity = Activity(
                    lead_id=lead.id,
                    description='系统自动将线索移入公海池'
                )
                db.session.add(activity)
            
            db.session.commit()
            app.logger.info(f'成功将{len(leads_to_move)}条线索移动到公海池')
    
    except Exception as e:
        db.session.rollback()
        app.logger.error(f'移动线索到公海失败：{str(e)}', exc_info=True)