{% extends 'base.html' %}

{% block title %}转移线索 - CRM系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-6 offset-md-3">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">转移线索</h4>
            </div>
            <div class="card-body">
                <h5 class="mb-3">线索信息</h5>
                <div class="mb-4">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 30%">名称：</th>
                            <td>{{ lead.name }}</td>
                        </tr>
                        <tr>
                            <th>公司：</th>
                            <td>{{ lead.company or '未填写' }}</td>
                        </tr>
                        <tr>
                            <th>当前负责人：</th>
                            <td>{{ lead.owner.username if lead.owner else '未分配' }}</td>
                        </tr>
                    </table>
                </div>
                
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> 注意：转移线索后，原负责人将无法访问此线索。
                </div>
                
                <form method="post" data-skip-validation="true">
                    <div class="mb-3">
                        <label for="owner_id" class="form-label">选择新负责人</label>
                        <select class="form-select" id="owner_id" name="owner_id" required>
                            <option value="">-- 请选择 --</option>
                            {% for user in users %}
                            <option value="{{ user.id }}" {% if lead.owner_id == user.id %}selected{% endif %}>{{ user.username }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">转移原因</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="请说明转移原因"></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('leads.view_lead', lead_id=lead.id) }}" class="btn btn-secondary">返回</a>
                        <button type="submit" class="btn btn-primary">确认转移</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}