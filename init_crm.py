#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CRM系统完整初始化脚本
用于服务器部署后的完整初始化，包括权限系统、用户账户和演示数据
"""

import os
import sys
import argparse
from datetime import datetime
from werkzeug.security import generate_password_hash

def init_crm_system(admin_password='admin123', create_demo_data=False):
    """完整初始化CRM系统"""
    try:
        # 导入应用和模型
        from app import create_app
        from config import Config
        from models import db, User, Company, Department, Role, Permission
        from utils.permission_setup import setup_roles_and_permissions

        app = create_app(Config)

        with app.app_context():
            print("🚀 CRM系统完整初始化")
            print("="*60)
            print(f"📅 初始化时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print()
            
            # 1. 确保数据库表存在
            db.create_all()
            print("✅ 数据库表已确保存在")

            # 2. 初始化权限系统
            print("🔧 初始化权限系统...")
            setup_roles_and_permissions(app)
            print("✅ 权限系统初始化完成")
            
            # 3. 验证权限数据
            roles = Role.query.all()
            permissions = Permission.query.all()
            print(f"✅ 权限验证: {len(roles)} 个角色, {len(permissions)} 个权限")
            
            # 4. 创建默认公司
            default_company = Company.query.filter_by(code='DEFAULT').first()
            if not default_company:
                default_company = Company(
                    name='默认公司',
                    code='DEFAULT',
                    type='headquarters'
                )
                db.session.add(default_company)
                db.session.commit()
                print("✅ 默认公司已创建")
            
            # 5. 创建超级管理员
            super_admin_role = Role.query.filter_by(code='super_admin').first()
            admin_user = User.query.filter_by(username='admin').first()
            
            admin_password = 'admin123'
            
            if not admin_user:
                admin_user = User(
                    username='admin',
                    name='系统管理员',
                    email='<EMAIL>',
                    password_hash=generate_password_hash(admin_password),
                    role_id=super_admin_role.id,
                    status='active'  # 使用status字段而不是is_active
                )
                db.session.add(admin_user)
                action = "创建"
            else:
                admin_user.password_hash = generate_password_hash(admin_password)
                admin_user.status = 'active'  # 确保用户状态为活跃
                action = "更新"
            
            db.session.commit()

            # 6. 创建演示数据（可选）
            if create_demo_data:
                print("\n6️⃣ 创建演示数据...")

                # 创建演示公司
                demo_company = Company.query.filter_by(code='DEMO_COMPANY').first()
                if not demo_company:
                    demo_company = Company(
                        name='演示公司',
                        code='DEMO_COMPANY',
                        type='headquarters'
                    )
                    db.session.add(demo_company)
                    db.session.commit()
                    print("  ✅ 创建演示公司: 演示公司")

                # 创建演示部门
                demo_dept = Department.query.filter_by(code='DEMO_SALES').first()
                if not demo_dept:
                    demo_dept = Department(
                        name='销售部',
                        code='DEMO_SALES',
                        company_id=demo_company.id,
                        description='演示销售部门'
                    )
                    db.session.add(demo_dept)
                    db.session.commit()
                    print("  ✅ 创建演示部门: 销售部")

                # 创建演示用户
                company_admin_role = Role.query.filter_by(code='company_admin').first()
                employee_role = Role.query.filter_by(code='sales').first()  # 使用sales角色

                demo_users = [
                    {
                        'username': 'company_admin',
                        'name': '公司管理员',
                        'email': '<EMAIL>',
                        'role_id': company_admin_role.id,
                        'company_id': demo_company.id,
                        'department_id': None
                    },
                    {
                        'username': 'employee1',
                        'name': '员工一',
                        'email': '<EMAIL>',
                        'role_id': employee_role.id,
                        'company_id': demo_company.id,
                        'department_id': demo_dept.id
                    },
                    {
                        'username': 'employee2',
                        'name': '员工二',
                        'email': '<EMAIL>',
                        'role_id': employee_role.id,
                        'company_id': demo_company.id,
                        'department_id': demo_dept.id
                    }
                ]

                for user_data in demo_users:
                    user = User.query.filter_by(username=user_data['username']).first()
                    if not user:
                        user = User(
                            username=user_data['username'],
                            name=user_data['name'],
                            email=user_data['email'],
                            password_hash=generate_password_hash('123456'),
                            role_id=user_data['role_id'],
                            company_id=user_data['company_id'],
                            department_id=user_data['department_id'],
                            status='active'
                        )
                        db.session.add(user)
                        print(f"  ✅ 创建演示用户: {user_data['username']} ({user_data['name']})")

                db.session.commit()

            print("\n" + "="*60)
            print("🎉 初始化完成！")
            print("="*60)
            print("📋 创建的账户:")
            print(f"  🔑 超级管理员: admin / {admin_password}")

            if create_demo_data:
                print("  🔑 公司管理员: company_admin / 123456")
                print("  🔑 演示员工1: employee1 / 123456")
                print("  🔑 演示员工2: employee2 / 123456")

            print("\n📝 重要提醒:")
            print("  - 请及时修改默认密码")
            print("  - 建议创建自己的公司和部门")
            if create_demo_data:
                print("  - 演示数据仅供测试使用")
            print("="*60)
            
            return True
            
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CRM系统完整初始化脚本')
    parser.add_argument('--password', '-p', default='admin123',
                       help='超级管理员密码 (默认: admin123)')
    parser.add_argument('--demo', '-d', action='store_true',
                       help='创建演示数据')
    parser.add_argument('--force', '-f', action='store_true',
                       help='强制重新初始化')

    args = parser.parse_args()

    # 检查是否在项目根目录
    if not os.path.isfile('app.py'):
        print("❌ 错误: 请在项目根目录下运行此脚本！")
        print("   当前目录:", os.getcwd())
        print("   请确保在包含 app.py 的目录下运行")
        sys.exit(1)

    print(f"📍 当前目录: {os.getcwd()}")
    print(f"⏰ 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 确认操作
    if not args.force:
        print("\n⚠️  此操作将初始化CRM系统，可能会覆盖现有的管理员账户。")
        confirm = input("是否继续？(y/N): ").lower().strip()
        if confirm not in ['y', 'yes']:
            print("操作已取消")
            sys.exit(0)

    print()

    # 执行完整初始化
    success = init_crm_system(
        admin_password=args.password,
        create_demo_data=args.demo
    )
    
    if success:
        print("\n🎯 下一步操作:")
        print("   1. 启动应用: python app.py 或使用 uwsgi")
        print("   2. 访问系统: http://your-domain.com")
        print(f"   3. 使用 admin/{args.password} 登录")
        print("   4. 修改默认密码并创建组织结构")
        if args.demo:
            print("   5. 测试演示账户功能")
        print("\n✅ 初始化成功！现在可以使用创建的账户登录系统。")
        sys.exit(0)
    else:
        print("\n❌ 初始化失败！请检查:")
        print("   1. 数据库连接是否正常")
        print("   2. 依赖包是否已安装")
        print("   3. 配置文件是否正确")
        sys.exit(1)

if __name__ == '__main__':
    main()
