"""
线索模型优化工具
包含查询优化、缓存策略、性能监控等功能
"""

from datetime import datetime, timedelta
from flask import current_app
from sqlalchemy import text, and_, or_
from models import db, Lead, LeadPool, User, Company, Activity
import redis
import json
import logging

logger = logging.getLogger(__name__)

class LeadQueryOptimizer:
    """线索查询优化器"""
    
    def __init__(self):
        self.redis_client = None
        try:
            self.redis_client = redis.Redis(
                host=current_app.config.get('REDIS_HOST', 'localhost'),
                port=current_app.config.get('REDIS_PORT', 6379),
                db=current_app.config.get('REDIS_DB', 0),
                decode_responses=True
            )
        except Exception as e:
            logger.warning(f"Redis连接失败，将使用内存缓存: {e}")
    
    def get_optimized_leads_query(self, user, filters=None):
        """获取优化的线索查询"""
        filters = filters or {}
        
        # 基础查询构建
        query = Lead.query
        
        # 根据用户角色优化查询
        if user.role.code == 'super_admin':
            pass  # 超级管理员可以查看所有线索
        elif user.role.code == 'company_admin':
            query = query.filter(Lead.company_id == user.company_id)
        elif user.role.code == 'department_admin':
            # 使用子查询优化部门管理员的查询
            department_user_ids = db.session.query(User.id).filter(
                User.department_id == user.department_id,
                User.status == 'active'
            ).subquery()
            query = query.filter(
                Lead.company_id == user.company_id,
                Lead.owner_id.in_(department_user_ids)
            )
        else:
            query = query.filter(Lead.owner_id == user.id)
        
        # 应用筛选条件
        if filters.get('stage'):
            query = self._apply_stage_filter(query, filters['stage'])
        
        if filters.get('owner_id'):
            query = query.filter(Lead.owner_id == filters['owner_id'])
        
        if filters.get('search'):
            search_term = f"%{filters['search']}%"
            query = query.filter(or_(
                Lead.name.ilike(search_term),
                Lead.company_name.ilike(search_term),
                Lead.phone.ilike(search_term)
            ))
        
        if filters.get('date_range'):
            start_date, end_date = filters['date_range']
            query = query.filter(Lead.created_at.between(start_date, end_date))
        
        return query
    
    def _apply_stage_filter(self, query, stage):
        """应用阶段筛选"""
        stage_conditions = {
            'new': Lead.is_called == False,
            'called': and_(Lead.is_called == True, Lead.is_connected == False),
            'connected': and_(Lead.is_connected == True, Lead.is_valid_call == False),
            'valid_call': and_(Lead.is_valid_call == True, Lead.is_wechat_added == False),
            'wechat_added': and_(Lead.is_wechat_added == True, Lead.is_intentional == False),
            'intentional': and_(Lead.is_intentional == True, Lead.is_visited == False),
            'visited': and_(Lead.is_visited == True, Lead.is_compliant == False),
            'compliant': and_(Lead.is_compliant == True, Lead.is_deal_done == False),
            'deal_done': and_(Lead.is_deal_done == True, Lead.is_car_selected == False),
            'car_selected': Lead.is_car_selected == True
        }

        if stage in stage_conditions:
            query = query.filter(stage_conditions[stage])
        
        return query
    
    def get_cached_statistics(self, user, cache_key_suffix=""):
        """获取缓存的统计数据"""
        cache_key = f"lead_stats:{user.id}:{user.company_id}:{cache_key_suffix}"
        
        if self.redis_client:
            try:
                cached_data = self.redis_client.get(cache_key)
                if cached_data:
                    return json.loads(cached_data)
            except Exception as e:
                logger.warning(f"Redis缓存读取失败: {e}")
        
        # 计算统计数据
        stats = self._calculate_statistics(user)
        
        # 缓存结果（5分钟过期）
        if self.redis_client:
            try:
                self.redis_client.setex(
                    cache_key, 
                    300,  # 5分钟
                    json.dumps(stats)
                )
            except Exception as e:
                logger.warning(f"Redis缓存写入失败: {e}")
        
        return stats
    
    def _calculate_statistics(self, user):
        """计算统计数据"""
        stats = {}
        
        # 基础查询
        base_query = self.get_optimized_leads_query(user)
        
        # 我的线索统计
        my_leads_query = Lead.query.filter(Lead.owner_id == user.id)
        stats['my_leads_count'] = my_leads_query.count()
        stats['my_pending_count'] = my_leads_query.filter(Lead.is_deal_done == False).count()
        
        # 公海线索统计
        if user.role.code == 'super_admin':
            public_sea_query = Lead.query.filter(Lead.is_in_public_sea == True)
        else:
            public_sea_query = Lead.query.filter(
                Lead.is_in_public_sea == True,
                Lead.company_id == user.company_id
            )
        stats['public_sea_count'] = public_sea_query.count()
        
        # 异地到店统计
        cross_location_pool = LeadPool.query.filter_by(
            company_id=user.company_id,
            pool_type='CROSS_LOCATION_PENDING'
        ).first()
        
        if cross_location_pool:
            stats['cross_location_pending_count'] = Lead.query.filter(
                Lead.pool_id == cross_location_pool.id,
                Lead.owner_id == None
            ).count()
        else:
            stats['cross_location_pending_count'] = 0
        
        # 本月签约统计
        month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        if user.role.code == 'super_admin':
            monthly_deals = Lead.query.filter(
                Lead.is_deal_done == True,
                Lead.updated_at >= month_start
            ).count()
            total_leads_this_month = Lead.query.filter(Lead.created_at >= month_start).count()
        else:
            monthly_deals = Lead.query.filter(
                Lead.is_deal_done == True,
                Lead.updated_at >= month_start,
                Lead.company_id == user.company_id
            ).count()
            total_leads_this_month = Lead.query.filter(
                Lead.created_at >= month_start,
                Lead.company_id == user.company_id
            ).count()
        
        stats['monthly_deals_count'] = monthly_deals
        stats['conversion_rate'] = round(
            (monthly_deals / total_leads_this_month * 100) if total_leads_this_month > 0 else 0,
            1
        )

        # 本月提车统计
        if user.role.code == 'super_admin':
            monthly_deliveries = Lead.query.filter(
                Lead.is_car_selected == True,
                Lead.updated_at >= month_start
            ).count()
        else:
            monthly_deliveries = Lead.query.filter(
                Lead.is_car_selected == True,
                Lead.updated_at >= month_start,
                Lead.company_id == user.company_id
            ).count()

        stats['monthly_deliveries_count'] = monthly_deliveries

        return stats
    
    def invalidate_cache(self, user_id, company_id):
        """清除缓存"""
        if self.redis_client:
            try:
                pattern = f"lead_stats:{user_id}:{company_id}:*"
                keys = self.redis_client.keys(pattern)
                if keys:
                    self.redis_client.delete(*keys)
            except Exception as e:
                logger.warning(f"清除缓存失败: {e}")

class LeadBatchProcessor:
    """线索批量处理器"""
    
    @staticmethod
    def batch_assign_leads(lead_ids, new_owner_id, current_user):
        """批量分配线索"""
        try:
            # 验证新负责人
            new_owner = User.query.get(new_owner_id)
            if not new_owner:
                raise ValueError("指定的负责人不存在")
            
            # 批量更新
            updated_count = db.session.query(Lead).filter(
                Lead.id.in_(lead_ids)
            ).update({
                'owner_id': new_owner_id,
                'company_id': new_owner.company_id,
                'updated_at': datetime.utcnow()
            }, synchronize_session=False)
            
            # 批量添加活动记录
            activities = []
            for lead_id in lead_ids:
                activities.append(Activity(
                    lead_id=lead_id,
                    user_id=current_user.id,
                    description=f'批量分配给 {new_owner.username}',
                    created_at=datetime.utcnow()
                ))
            
            db.session.bulk_insert_mappings(Activity, [
                {
                    'lead_id': activity.lead_id,
                    'user_id': activity.user_id,
                    'description': activity.description,
                    'created_at': activity.created_at
                } for activity in activities
            ])
            
            db.session.commit()
            return updated_count
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"批量分配线索失败: {e}")
            raise
    
    @staticmethod
    def batch_release_to_public_sea(lead_ids, current_user):
        """批量释放到公海"""
        try:
            # 获取公海池
            public_sea_pool = LeadPool.query.filter_by(
                company_id=current_user.company_id,
                is_public_sea=True
            ).first()
            
            if not public_sea_pool:
                raise ValueError("公海池不存在")
            
            now = datetime.utcnow()
            
            # 批量更新线索状态
            updated_count = db.session.query(Lead).filter(
                Lead.id.in_(lead_ids)
            ).update({
                'owner_id': None,
                'is_in_public_sea': True,
                'public_sea_time': now,
                'pool_id': public_sea_pool.id,
                'updated_at': now
            }, synchronize_session=False)
            
            # 批量添加活动记录
            activities = []
            for lead_id in lead_ids:
                activities.append({
                    'lead_id': lead_id,
                    'user_id': current_user.id,
                    'description': '批量释放到公海',
                    'created_at': now
                })
            
            db.session.bulk_insert_mappings(Activity, activities)
            db.session.commit()
            
            return updated_count
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"批量释放到公海失败: {e}")
            raise

class LeadPerformanceMonitor:
    """线索性能监控器"""
    
    @staticmethod
    def log_slow_query(query_name, execution_time, threshold=1.0):
        """记录慢查询"""
        if execution_time > threshold:
            logger.warning(f"慢查询检测: {query_name} 执行时间: {execution_time:.2f}秒")
    
    @staticmethod
    def get_database_statistics():
        """获取数据库统计信息"""
        try:
            stats = {}
            
            # 表大小统计
            result = db.session.execute(text("""
                SELECT 
                    table_name,
                    table_rows,
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
                AND table_name IN ('lead', 'lead_pool', 'activity', 'lead_status_history')
                ORDER BY size_mb DESC
            """))
            
            stats['table_sizes'] = [dict(row) for row in result]
            
            # 索引使用统计
            result = db.session.execute(text("""
                SELECT 
                    table_name,
                    index_name,
                    cardinality
                FROM information_schema.statistics 
                WHERE table_schema = DATABASE()
                AND table_name IN ('lead', 'lead_pool', 'activity')
                ORDER BY cardinality DESC
            """))
            
            stats['index_usage'] = [dict(row) for row in result]
            
            return stats
            
        except Exception as e:
            logger.error(f"获取数据库统计信息失败: {e}")
            return {}

# 全局实例
lead_optimizer = LeadQueryOptimizer()
lead_batch_processor = LeadBatchProcessor()
lead_performance_monitor = LeadPerformanceMonitor()
