{% extends 'base.html' %}

{% block title %}线索详情 - CRM系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题区域 -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">
                    <i class="fas fa-user-circle me-2"></i>{{ lead.name }}
                </h1>
                <p class="page-subtitle">线索详情 - {{ lead.company_name }} | 当前阶段：{{ lead.current_stage }}</p>
            </div>
            <div class="text-end">
                <div class="btn-group-theme">
                <a href="{{ url_for('leads.edit_lead', lead_id=lead.id) }}" class="btn btn-theme-primary">
                    <i class="fas fa-edit me-1"></i>编辑
                </a>
                {% if current_user.role.code in ['super_admin', 'company_admin', 'department_admin'] %}
                <a href="{{ url_for('leads.assign_lead_route', lead_id=lead.id) }}" class="btn btn-theme-info">
                    <i class="fas fa-user-plus me-1"></i>分配
                </a>
                <button type="button" class="btn btn-theme-danger" id="deleteLeadBtn">
                    <i class="fas fa-trash me-1"></i>删除
                </button>
                {% endif %}

                {# 添加异地到店推送按钮 - 仅在满足条件时显示 #}
                {% if lead.is_intentional and lead.company_id == current_user.company_id and (lead.owner_id == current_user.id or current_user.role.code in ['super_admin', 'company_admin', 'department_admin']) %}
                <button type="button" class="btn btn-theme-warning" id="pushToCrossBtn">
                    <i class="fas fa-share me-1"></i>推送到异地到店
                </button>
                {% endif %}

                {# 如果是待处理的异地线索，显示认领按钮 #}
                {% if lead.pool_id and lead.current_processing_company_id == current_user.company_id and not lead.owner_id %}
                <button type="button" class="btn btn-theme-success claim-cross-location-btn"
                        data-lead-id="{{ lead.id }}"
                        data-lead-name="{{ lead.name }}">
                    <i class="fas fa-hand-paper me-1"></i>认领异地线索
                </button>
                {% endif %}

                {# 如果是已认领的异地线索，显示释放按钮 #}
                {% if lead.owner_id == current_user.id and lead.company_id != current_user.company_id %}
                <form action="{{ url_for('leads.release_leads_to_pool_route') }}" method="POST" style="display:inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <input type="hidden" name="lead_ids" value="{{ lead.id }}">
                    <button type="submit" class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-1"></i>释放异地线索
                    </button>
                </form>
                {% endif %}

                {# 如果是已推送的异地线索，且用户有权限，显示撤回按钮 #}
                {% if lead.current_processing_company_id and lead.current_processing_company_id != current_user.company_id and lead.company_id == current_user.company_id and current_user.role.code in ['super_admin', 'company_admin', 'department_admin'] %}
                <form action="{{ url_for('leads.recall_cross_location_lead_route', lead_id=lead.id) }}" method="POST" style="display:inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-secondary">
                        <i class="fas fa-undo-alt me-1"></i>撤回异地线索
                    </button>
                </form>
                {% endif %}

                <a href="{{ url_for('leads.leads_unified') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回线索管理
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">

                    <div class="row">
                        <div class="col-md-6">
                            <h5>基本信息</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <th style="width: 30%">名称：</th>
                                    <td>{{ lead.name }}</td>
                                </tr>
                                <tr>
                                    <th>省份：</th>
                                    <td>{{ lead.company_name }}</td>
                                </tr>
                                <tr>
                                    <th>城市：</th>
                                    <td>{{ lead.email }}</td>
                                </tr>
                                <tr>
                                    <th>电话：</th>
                                    <td>{{ lead.phone }}</td>
                                </tr>
                                {# 如果是异地线索，显示原始公司信息 #}
                                {% if lead.company_id != current_user.company_id %}
                                <tr>
                                    <th>线索来源：</th>
                                    <td><span class="badge bg-info">{{ lead.company.name }}</span> 异地到店</td>
                                </tr>
                                {% endif %}
                                {# 如果线索已被推送，显示处理公司信息 #}
                                {% if lead.current_processing_company_id and lead.current_processing_company_id != lead.company_id %}
                                <tr>
                                    <th>处理公司：</th>
                                    <td><span class="badge bg-warning">{{ lead.current_processing_company.name }}</span></td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>

                        <div class="col-md-6">
                            <h5>跟进状态</h5>
                            <div class="funnel-stages">
                                <div class="stage {% if lead.is_called %}completed{% endif %}">
                                    <span class="stage-name">已拨电话</span>
                                    <span class="stage-status">{% if lead.is_called %}✓{% else %}{% endif %}</span>
                                </div>
                                <div class="stage {% if lead.is_valid_call %}completed{% endif %}">
                                    <span class="stage-name">有效通话</span>
                                    <span class="stage-status">{% if lead.is_valid_call %}✓{% else %}{% endif %}</span>
                                </div>
                                <div class="stage {% if lead.is_wechat_added %}completed{% endif %}">
                                    <span class="stage-name">添加微信</span>
                                    <span class="stage-status">{% if lead.is_wechat_added %}✓{% else %}{% endif %}</span>
                                </div>
                                <div class="stage {% if lead.is_intentional %}completed{% endif %}">
                                    <span class="stage-name">意向客户</span>
                                    <span class="stage-status">{% if lead.is_intentional %}✓{% else %}{% endif %}</span>
                                </div>
                                <div class="stage {% if lead.is_visited %}completed{% endif %}">
                                    <span class="stage-name">确认到面</span>
                                    <span class="stage-status">{% if lead.is_visited %}✓{% else %}{% endif %}</span>
                                </div>
                                <div class="stage {% if lead.is_compliant %}completed{% endif %}">
                                    <span class="stage-name">信息合规</span>
                                    <span class="stage-status">{% if lead.is_compliant %}✓{% else %}{% endif %}</span>
                                </div>
                                <div class="stage {% if lead.is_deal_done %}completed{% endif %}">
                                    <span class="stage-name">完成签约</span>
                                    <span class="stage-status">{% if lead.is_deal_done %}✓{% else %}{% endif %}</span>
                                </div>
                                <div class="stage {% if lead.is_car_selected %}completed{% endif %}">
                                    <span class="stage-name">完成提车</span>
                                    <span class="stage-status">{% if lead.is_car_selected %}✓{% else %}{% endif %}</span>
                                </div>
                            </div>
                            <div class="mt-3">
                                <p><strong>当前阶段：</strong>{{ lead.current_stage }}</p>
                            </div>
                        </div>
                    </div>

                    {# 新增：租赁计划详情显示区域 #}
                    {% if lead.is_deal_done %}
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>租赁计划详情</h5>
                                    </div>
                                    <div class="card-body">
                                        {% if lead.lease_plan %}
                                            <table class="table table-borderless">
                                                {% if lead.lease_plan.term_months is not none %}
                                                <tr>
                                                    <th style="width: 30%">租期：</th>
                                                    <td>{{ lead.lease_plan.term_months }} 个月</td>
                                                </tr>
                                                {% endif %}
                                                {% if lead.lease_plan.free_rent_days is not none %}
                                                <tr>
                                                    <th>免租天数：</th>
                                                    <td>{{ lead.lease_plan.free_rent_days }} 天</td>
                                                </tr>
                                                {% endif %}
                                                {% if lead.lease_plan.custom_notes %}
                                                <tr>
                                                    <th>其他赠送/备注：</th>
                                                    <td>{{ lead.lease_plan.custom_notes | nl2br }}</td> {# 假设nl2br过滤器已注册或将注册 #}
                                                </tr>
                                                {% endif %}
                                            </table>
                                        {% else %}
                                            <div class="alert alert-info" role="alert">
                                                此线索签约，但尚未录入租赁计划详情。
                                                <a href="{{ url_for('leads.edit_lead', lead_id=lead.id) }}#lease_plan_section" class="alert-link">点此补充</a>。
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                    {# 租赁计划详情显示区域结束 #}

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h5>跟进记录</h5>
                            <div class="timeline">
                                {% for activity in activities %}
                                <div class="timeline-item">
                                    <div class="timeline-date">{{ activity.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                                    <div class="timeline-content">
                                        <div class="timeline-user">{{ activity.user.username }}</div>
                                        <div class="timeline-description">{{ activity.description }}</div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h5>添加跟进记录</h5>
                            <form method="POST" action="{{ url_for('leads.add_activity', lead_id=lead.id) }}">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <div class="mb-3">
                                    <textarea class="form-control" name="description" rows="3" required></textarea>
                                </div>
                                <button type="submit" class="btn btn-theme-primary">添加记录</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除线索 <strong>{{ lead.name }}</strong> 吗？</p>
                <p class="text-danger">警告：此操作不可撤销，线索的所有相关记录也将被删除！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                <form action="{{ url_for('leads.delete_lead_route', lead_id=lead.id) }}" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-theme-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 异地到店推送模态框 -->
<div class="modal fade" id="pushToCrossModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">推送到异地到店</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('leads.push_lead_to_cross_location_route', lead_id=lead.id) }}" method="POST" id="pushToCrossForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-body">
                    <p>确定要将线索 <strong>{{ lead.name }}</strong> 推送到异地到店池吗？</p>
                    <p>请选择目标公司：</p>
                    <div class="mb-3">
                        <input type="text" class="form-control" id="companySearchInput" placeholder="输入公司名称关键词搜索..." autocomplete="off">
                        <input type="hidden" name="target_company_id" id="targetCompanyIdHidden" required>
                        <div class="dropdown mt-1">
                            <ul class="dropdown-menu" id="companySuggestionsMenu" aria-labelledby="companySearchInput" style="width: 100%; max-height: 200px; overflow-y: auto;">
                                {# 搜索建议将通过JS动态填充到这里 #}
                            </ul>
                        </div>
                        <div id="companySearchError" class="invalid-feedback" style="display: none;">请从建议列表中选择一个有效的目标公司。</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-theme-warning">确认推送</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.funnel-stages {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.stage {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-radius: 5px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.stage.completed {
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.stage-name {
    font-weight: 500;
}

.stage-status {
    font-weight: bold;
}

.timeline {
    position: relative;
    margin: 20px 0;
    padding: 20px;
    border-left: 2px solid #dee2e6;
}

.timeline-item {
    margin-bottom: 20px;
    position: relative;
    padding-left: 20px;
}

.timeline-item:before {
    content: '';
    position: absolute;
    left: -11px;
    top: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #fff;
    border: 2px solid #007bff;
}

.timeline-date {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 5px;
}

.timeline-user {
    font-weight: 500;
    margin-bottom: 5px;
}

.timeline-description {
    color: #212529;
}
</style>
{% endblock %}

{% block scripts %}
{{ super() }} {# 确保继承父模板的scripts #}
<script>
    // 自定义过滤器，将换行符转换为<br>标签
    function nl2br(str) {
        if (typeof str !== 'string') return '';
        return str.replace(/\n/g, '<br>');
    }

    // 初始化删除确认模态框
    document.addEventListener('DOMContentLoaded', function() {
        const deleteBtn = document.getElementById('deleteLeadBtn');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', function() {
                const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
                modal.show();
            });
        }
        
        // 初始化异地到店推送模态框
        const pushToCrossBtn = document.getElementById('pushToCrossBtn');
        if (pushToCrossBtn) {
            pushToCrossBtn.addEventListener('click', function() {
                const modal = new bootstrap.Modal(document.getElementById('pushToCrossModal'));
                // 重置搜索框和隐藏字段状态，在模态框显示时
                document.getElementById('companySearchInput').value = '';
                document.getElementById('targetCompanyIdHidden').value = '';
                document.getElementById('companySuggestionsMenu').innerHTML = '';
                document.getElementById('companySuggestionsMenu').classList.remove('show');
                document.getElementById('companySearchError').style.display = 'none';
                document.getElementById('companySearchInput').classList.remove('is-invalid');
                modal.show();
            });
        }

        // 认领异地线索按钮处理
        const claimCrossLocationBtn = document.querySelector('.claim-cross-location-btn');
        if (claimCrossLocationBtn) {
            claimCrossLocationBtn.addEventListener('click', function(e) {
                e.preventDefault();

                const leadId = this.dataset.leadId;
                const leadName = this.dataset.leadName;
                const button = this;

                console.log('点击认领按钮，线索ID:', leadId, '线索名称:', leadName);

                if (!leadId) {
                    alert('线索ID无效');
                    return;
                }

                if (confirm(`确定要认领线索"${leadName}"吗？认领后该线索将分配给您处理。`)) {
                    // 显示加载状态
                    const originalText = button.innerHTML;
                    button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> 认领中...';
                    button.disabled = true;

                    // 发送认领请求
                    fetch(`/api/leads/${leadId}/claim-cross-location`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('认领响应:', data);
                        if (data.success) {
                            alert('认领成功！');
                            location.reload();
                        } else {
                            alert('认领失败：' + (data.message || '未知错误'));
                            // 恢复按钮状态
                            button.innerHTML = originalText;
                            button.disabled = false;
                        }
                    })
                    .catch(error => {
                        console.error('认领请求失败:', error);
                        alert('认领失败，请重试');

                        // 恢复按钮状态
                        button.innerHTML = originalText;
                        button.disabled = false;
                    });
                }
            });
        }
    });

    // 公司搜索逻辑
    const companySearchInput = document.getElementById('companySearchInput');
    const companySuggestionsMenu = document.getElementById('companySuggestionsMenu');
    const targetCompanyIdHidden = document.getElementById('targetCompanyIdHidden');
    const companySearchError = document.getElementById('companySearchError');
    const pushToCrossForm = document.getElementById('pushToCrossForm');

    {% set company_list_for_js = [] %}
    {% for company in companies %}
        {% if company.id != current_user.company_id %}
            {# 在Jinja内部构建Python字典，确保company.name是字符串 #}
            {% set company_name_str = company.name if company.name is defined and company.name is not none else '' %}
            {% set _ = company_list_for_js.append({'id': company.id, 'name': company_name_str}) %}
        {% endif %}
    {% endfor %}
    const allCompanies = JSON.parse('{{ company_list_for_js | tojson | safe }}');

    if (companySearchInput) {
        companySearchInput.addEventListener('input', function() {
            const inputValue = this.value.trim().toLowerCase();
            companySuggestionsMenu.innerHTML = ''; // 清空旧建议
            targetCompanyIdHidden.value = ''; // 清空已选ID，因为用户正在重新输入
            companySearchInput.classList.remove('is-invalid');
            companySearchError.style.display = 'none';

            if (inputValue.length === 0) {
                companySuggestionsMenu.classList.remove('show');
                return;
            }

            const filteredCompanies = allCompanies.filter(company => 
                company.name.toLowerCase().includes(inputValue)
            );

            if (filteredCompanies.length > 0) {
                filteredCompanies.forEach(company => {
                    const listItem = document.createElement('li');
                    const linkItem = document.createElement('a');
                    linkItem.classList.add('dropdown-item');
                    linkItem.href = '#';
                    linkItem.textContent = company.name;
                    linkItem.dataset.companyId = company.id;
                    linkItem.dataset.companyName = company.name;
                    
                    linkItem.addEventListener('click', function(e) {
                        e.preventDefault();
                        companySearchInput.value = this.dataset.companyName;
                        targetCompanyIdHidden.value = this.dataset.companyId;
                        companySuggestionsMenu.classList.remove('show');
                        companySearchInput.classList.remove('is-invalid');
                        companySearchError.style.display = 'none';
                    });
                    listItem.appendChild(linkItem);
                    companySuggestionsMenu.appendChild(listItem);
                });
                companySuggestionsMenu.classList.add('show');
            } else {
                const listItem = document.createElement('li');
                listItem.innerHTML = '<span class="dropdown-item-text">无匹配结果</span>';
                companySuggestionsMenu.appendChild(listItem);
                companySuggestionsMenu.classList.add('show');
            }
        });

        // 点击外部关闭建议列表
        document.addEventListener('click', function(event) {
            if (companySearchInput && !companySearchInput.contains(event.target) && companySuggestionsMenu && !companySuggestionsMenu.contains(event.target)) {
                companySuggestionsMenu.classList.remove('show');
            }
        });
    }

    if (pushToCrossForm) {
        pushToCrossForm.addEventListener('submit', function(event) {
            if (!targetCompanyIdHidden.value) {
                event.preventDefault(); // 阻止表单提交
                companySearchError.style.display = 'block';
                companySearchInput.classList.add('is-invalid');
            }
        });
    }
</script>
{% endblock %}