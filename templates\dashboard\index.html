{% extends 'base.html' %}

{% block title %}仪表盘 - CRM系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">仪表盘</h1>
        </div>
    </div>
    
    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-4 col-xl-3 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <h5 class="card-title">总线索数</h5>
                    <h2 class="card-text" id="total-leads">{{ stats.total_leads }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4 col-xl-3 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <h5 class="card-title">今日新增</h5>
                    <h2 class="card-text" id="new-today">{{ stats.new_today }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4 col-xl-3 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <h5 class="card-title">待处理线索</h5>
                    <h2 class="card-text" id="pending-leads">{{ stats.pending_leads }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4 col-xl-3 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <h5 class="card-title">签约线索</h5>
                    <h2 class="card-text" id="won-leads">{{ stats.won_leads }}</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表 -->
    <div class="row mb-4">
        <div class="col-xl-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">最近7天线索趋势</h5>
                </div>
                <div class="card-body">
                    <canvas id="leads-trend-chart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-xl-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">线索状态分布</h5>
                </div>
                <div class="card-body">
                    <canvas id="leads-status-chart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近线索列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">最近线索</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>名称</th>
                                    <th>联系人</th>
                                    <th>电话</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                </tr>
                            </thead>
                            <tbody id="recent-leads">
                                {% for lead in stats.recent_leads %}
                                <tr>
                                    <td>{{ lead.name }}</td>
                                    <td>{{ lead.contact }}</td>
                                    <td>{{ lead.phone }}</td>
                                    <td>{{ lead.status }}</td>
                                    <td>{{ lead.created_at }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取图表数据
    fetch('{{ url_for("dashboard.get_dashboard_stats") }}')
        .then(response => response.json())
        .then(data => {
            // 更新统计数据
            document.getElementById('total-leads').textContent = data.total_leads;
            document.getElementById('new-today').textContent = data.new_today;
            document.getElementById('pending-leads').textContent = data.pending_leads;
            document.getElementById('won-leads').textContent = data.won_leads;

            // 绘制趋势图
            const trendCtx = document.getElementById('leads-trend-chart').getContext('2d');
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: data.last_7_days.map(day => day.date),
                    datasets: [{
                        label: '新增线索',
                        data: data.last_7_days.map(day => day.new),
                        borderColor: 'rgb(75, 192, 192)',
                        tension: 0.1
                    }, {
                        label: '签约线索',
                        data: data.last_7_days.map(day => day.won),
                        borderColor: 'rgb(54, 162, 235)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // 绘制状态分布图
            const statusCtx = document.getElementById('leads-status-chart').getContext('2d');
            new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['待处理', '跟进中', '签约', '已失败'],
                    datasets: [{
                        data: [
                            data.pending_leads,
                            data.following_leads,
                            data.won_leads,
                            data.lost_leads
                        ],
                        backgroundColor: [
                            'rgb(255, 205, 86)',
                            'rgb(75, 192, 192)',
                            'rgb(54, 162, 235)',
                            'rgb(255, 99, 132)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // 更新最近线索列表
            const recentLeadsBody = document.getElementById('recent-leads');
            recentLeadsBody.innerHTML = data.recent_leads.map(lead => `
                <tr>
                    <td>${lead.name}</td>
                    <td>${lead.contact}</td>
                    <td>${lead.phone}</td>
                    <td>${lead.status}</td>
                    <td>${lead.created_at}</td>
                </tr>
            `).join('');
        });
});
</script>
{% endblock %} 