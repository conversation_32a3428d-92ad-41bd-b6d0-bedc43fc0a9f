def register_blueprints(app, csrf):  # 注册所有蓝图
    from routes.organization import bp as organization_bp
    from routes.organization_unified import bp as organization_unified_bp  # 导入统一组织管理蓝图
    from routes.company import company_bp  # 导入公司管理蓝图
    from routes.api import bp as api_bp
    from routes.organization_api import bp as organization_api_bp
    from routes.rbac import bp as rbac_bp
    from routes.lead_pool import bp as lead_pool_bp
    from routes.pool_management import bp as pool_management_bp
    from utils.lead_utils import bp as leads_bp
    from routes.behavior_captcha import bp as behavior_captcha_bp  # 导入行为验证码蓝图
    from utils.notification_utils import notifications_bp
    from blueprints.admin_settings import admin_settings_bp  # 导入管理员设置蓝图


    # 注册统一组织管理蓝图（新的主要入口）
    app.register_blueprint(organization_unified_bp, url_prefix='/organization')
    # 注册原有组织管理蓝图（保留作为备用）
    app.register_blueprint(organization_bp, url_prefix='/organization_legacy')
    app.register_blueprint(company_bp)  # 注册公司管理蓝图
    app.register_blueprint(api_bp)
    app.register_blueprint(organization_api_bp, url_prefix='/organization_api')
    app.register_blueprint(rbac_bp, url_prefix='/rbac')
    app.register_blueprint(lead_pool_bp, url_prefix='/lead_pool')
    app.register_blueprint(pool_management_bp, url_prefix='/pool-management')
    app.register_blueprint(leads_bp)
    app.register_blueprint(behavior_captcha_bp)  # 注册行为验证码蓝图
    app.register_blueprint(notifications_bp)
    app.register_blueprint(admin_settings_bp)  # 注册管理员设置蓝图
    
    # 为所有蓝图添加CSRF保护
    csrf.init_app(app)
    csrf.exempt(api_bp)  # API路由豁免CSRF检查
    csrf.exempt(behavior_captcha_bp)  # 行为验证码API豁免CSRF检查
    csrf.exempt(leads_bp)  # 线索API路由豁免CSRF检查