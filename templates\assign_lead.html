{% extends 'base.html' %}

{% block title %}分配线索 - CRM系统{% endblock %}

{% block content %}
<div class="container">
    <!-- 页面标题区域 -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">
                    <i class="fas fa-user-plus me-2"></i>分配线索
                </h1>
                <p class="page-subtitle">将线索分配给指定的销售人员或部门</p>
            </div>
            <div class="text-end">
                <div class="btn-group-theme">
                    <a href="{{ url_for('leads.leads_unified') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回线索管理
                    </a>
                </div>
            </div>
        </div>
    </div>

<style>
    #company_search_results_assign {
        max-height: 200px;
        overflow-y: auto;
        position: absolute;
        z-index: 1000;
        background-color: #fff;
        border: 1px solid #ced4da;
        width: 100%; /* Ensure it spans the container */
    }
    .selected-info {
        padding: 5px;
        background-color: #f8f9fa;
        border-radius: 3px;
        border: 1px solid #dee2e6;
    }
</style>
<div class="row">
    <div class="col-md-6 offset-md-3">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">分配线索</h4>
            </div>
            <div class="card-body">
                <h5 class="mb-3">线索信息</h5>
                <div class="mb-4">
                    <table class="table table-borderless">
                        <tr>
                            <th style="width: 30%">名称：</th>
                            <td>{{ lead.name }}</td>
                        </tr>
                        <tr>
                            <th>公司：</th>
                            <td>{{ lead.company_name or '未填写' }}</td>
                        </tr>
                        <tr>
                            <th>当前负责人：</th>
                            <td>{{ lead.owner.username if lead.owner else '未分配' }}</td>
                        </tr>
                    </table>
                </div>
                
                <form method="post" data-skip-validation="true">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    
                    {% if current_user.role.code in ['super_admin', 'company_admin'] %}
                    <div class="mb-3">
                        <label class="form-label">分配类型</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="assignment_type" id="same_company" value="same_company" checked>
                            <label class="form-check-label" for="same_company">
                                本公司内分配
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="assignment_type" id="cross_company" value="cross_company">
                            <label class="form-check-label" for="cross_company">
                                跨公司分配
                            </label>
                        </div>
                    </div>
                    
                    <div id="company_select_container" class="mb-3 d-none">
                        <label for="target_company_search_assign" class="form-label">搜索目标公司</label>
                        <div class="input-group input-group-sm">
                            <input type="text" class="form-control form-control-sm" id="target_company_search_assign" placeholder="输入公司名称...">
                            <input type="hidden" id="target_company_id_assign" name="company_id">
                            <button class="btn btn-outline-secondary btn-sm" type="button" id="clear_company_btn_assign">清除</button>
                        </div>
                        <div id="company_search_results_assign" class="dropdown-menu"></div>
                        <div id="selected_company_info_assign" class="selected-info mt-1 small" style="display:none; font-size: 0.8rem;">
                            推送至: <span id="selected_company_name_assign"></span>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <label for="owner_id" class="form-label">选择新负责人</label>
                        <select class="form-select" id="owner_id" name="owner_id" required>
                            <option value="">-- 请选择 --</option>
                            {% for user in users %}
                            <option value="{{ user.id }}" data-company-id="{{ user.company_id }}" {% if lead.owner_id == user.id %}selected{% endif %}>
                                {{ user.username }} ({{ user.company.name if user.company else '无公司' }})
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">备注</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="请输入分配备注（可选）"></textarea>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-theme-primary">确认分配</button>
                        <a href="{{ url_for('leads.view_lead', lead_id=lead.id) }}" class="btn btn-outline-secondary">取消</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const assignmentTypeRadios = document.querySelectorAll('input[name="assignment_type"]');
    const companySelectContainer = document.getElementById('company_select_container');
    const ownerSelect = document.getElementById('owner_id');
    const ownerOptions = Array.from(ownerSelect.options);
    
    // New elements for company search
    const targetCompanySearchInput = document.getElementById('target_company_search_assign');
    const targetCompanyIdInput = document.getElementById('target_company_id_assign');
    const companySearchResultsDiv = document.getElementById('company_search_results_assign');
    const selectedCompanyInfoDiv = document.getElementById('selected_company_info_assign');
    const selectedCompanyNameSpan = document.getElementById('selected_company_name_assign');
    const clearCompanyBtn = document.getElementById('clear_company_btn_assign');
    
    // 初始化显示本公司内用户
    filterOwnerOptions();
    
    // 监听分配类型变化
    if (assignmentTypeRadios.length > 0) {
        assignmentTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'cross_company') {
                    companySelectContainer.classList.remove('d-none');
                    ownerSelect.innerHTML = '<option value="">-- 请先搜索并选择公司 --</option>';
                } else {
                    companySelectContainer.classList.add('d-none');
                    clearSelectedCompanyAssign();
                    filterOwnerOptions();
                }
            });
        });
    }
    
    // Company Search Logic (Adapted from lead_form.html)
    function clearSelectedCompanyAssign() {
        targetCompanySearchInput.value = '';
        targetCompanyIdInput.value = '';
        selectedCompanyInfoDiv.style.display = 'none';
        selectedCompanyNameSpan.textContent = '';
        companySearchResultsDiv.innerHTML = '';
        companySearchResultsDiv.classList.remove('show');
        // After clearing, if cross_company is still selected, prompt to select a company for owners
        const assignmentType = document.querySelector('input[name="assignment_type"]:checked');
        if (assignmentType && assignmentType.value === 'cross_company') {
             ownerSelect.innerHTML = '<option value="">-- 请先搜索并选择公司 --</option>';
        }
    }

    if (clearCompanyBtn) {
        clearCompanyBtn.addEventListener('click', function() {
            clearSelectedCompanyAssign();
            // Potentially re-filter owners if a "select company" prompt is desired
            const assignmentType = document.querySelector('input[name="assignment_type"]:checked');
            if (assignmentType && assignmentType.value === 'cross_company') {
                 ownerSelect.innerHTML = '<option value="">-- 请先搜索并选择公司 --</option>';
            } else {
                filterOwnerOptions(); // Default to current company users
            }
        });
    }

    if (targetCompanySearchInput) {
        targetCompanySearchInput.addEventListener('input', function() {
            const query = this.value.trim();
            if (query.length < 2) {
                companySearchResultsDiv.innerHTML = '';
                companySearchResultsDiv.classList.remove('show');
                return;
            }

            fetch('/organization_api/companies/search?q=' + encodeURIComponent(query))
                .then(response => response.json())
                .then(data => {
                    companySearchResultsDiv.innerHTML = '';

                    // 处理错误响应
                    if (data.error) {
                        companySearchResultsDiv.innerHTML = '<div class="dropdown-item text-danger">搜索出错，请重试</div>';
                        companySearchResultsDiv.classList.add('show');
                        return;
                    }

                    // 确保data是数组格式
                    const companies = Array.isArray(data) ? data : (data.companies || []);

                    if (companies.length === 0) {
                        const noResultsItem = document.createElement('div');
                        noResultsItem.classList.add('dropdown-item', 'text-muted');
                        noResultsItem.textContent = '未找到匹配的公司';
                        companySearchResultsDiv.appendChild(noResultsItem);
                    } else {
                        companies.forEach(company => {
                            const item = document.createElement('a');
                            item.classList.add('dropdown-item');
                            item.href = '#';
                            item.textContent = company.name;
                            item.dataset.id = company.id;
                            item.addEventListener('click', function(e) {
                                e.preventDefault();
                                targetCompanySearchInput.value = company.name;
                                targetCompanyIdInput.value = company.id;
                                selectedCompanyNameSpan.textContent = company.name;
                                selectedCompanyInfoDiv.style.display = 'block';
                                companySearchResultsDiv.innerHTML = '';
                                companySearchResultsDiv.classList.remove('show');
                                // Load owners for the selected company
                                loadCompanyEmployees(company.id);
                            });
                            companySearchResultsDiv.appendChild(item);
                        });
                    }
                    companySearchResultsDiv.classList.add('show');
                })
                .catch(error => {
                    console.error('搜索公司时出错:', error);
                    companySearchResultsDiv.innerHTML = '<div class="dropdown-item text-danger">网络错误，请重试</div>';
                    companySearchResultsDiv.classList.add('show');
                });
        });

        // Hide results when clicking outside
        document.addEventListener('click', function(e) {
            if (!companySelectContainer.contains(e.target)) {
                companySearchResultsDiv.classList.remove('show');
            }
        });
    }
    
    // 根据公司过滤用户选项
    function filterOwnerOptions(companyId = null) {
        // 清空当前选项
        ownerSelect.innerHTML = '';

        // 添加默认选项
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = '-- 请选择 --';
        ownerSelect.appendChild(defaultOption);

        // 当前选择的分配类型
        const assignmentType = document.querySelector('input[name="assignment_type"]:checked');

        // 筛选选项
        const filteredOptions = ownerOptions.filter(option => {
            const optionCompanyId = option.getAttribute('data-company-id');

            if (!assignmentType || assignmentType.value === 'same_company') {
                // 本公司用户
                return optionCompanyId == "{{ current_user.company_id }}";
            } else if (companyId) {
                // 指定公司用户
                return optionCompanyId == companyId;
            }

            return false;
        });

        // 添加过滤后的选项
        filteredOptions.forEach(option => {
            ownerSelect.appendChild(option.cloneNode(true));
        });
    }

    // 动态加载指定公司的员工列表
    function loadCompanyEmployees(companyId) {
        // 显示加载状态
        ownerSelect.innerHTML = '<option value="">-- 正在加载员工列表... --</option>';

        // 调用API获取公司员工
        fetch(`/api/companies/${companyId}/employees`)
            .then(response => response.json())
            .then(data => {
                // 清空选项
                ownerSelect.innerHTML = '';

                // 添加默认选项
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = '-- 请选择负责人 --';
                ownerSelect.appendChild(defaultOption);

                // 添加员工选项
                if (data && data.length > 0) {
                    data.forEach(employee => {
                        const option = document.createElement('option');
                        option.value = employee.id;
                        option.textContent = `${employee.name || employee.username}`;
                        option.setAttribute('data-company-id', companyId);
                        ownerSelect.appendChild(option);
                    });
                } else {
                    const noEmployeesOption = document.createElement('option');
                    noEmployeesOption.value = '';
                    noEmployeesOption.textContent = '-- 该公司暂无可分配的员工 --';
                    noEmployeesOption.disabled = true;
                    ownerSelect.appendChild(noEmployeesOption);
                }
            })
            .catch(error => {
                console.error('获取公司员工列表失败:', error);
                ownerSelect.innerHTML = '<option value="">-- 获取员工列表失败 --</option>';
            });
    }
});
</script>
{% endblock %}