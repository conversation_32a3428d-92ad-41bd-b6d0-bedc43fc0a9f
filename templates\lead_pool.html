{% extends 'base.html' %}

{% block title %}线索公海 - CRM系统{% endblock %}

{% block content %}
<!-- 页面标题区域 -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">
                <i class="fas fa-water me-2"></i>线索公海
            </h1>
            <p class="page-subtitle">查看和认领公司内的公共线索资源</p>
        </div>
        <div class="text-end">
            <div class="btn-group">
                <a href="{{ url_for('leads.leads_unified') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回线索管理
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    
    <!-- 搜索表单 -->
    <form class="mb-4" method="get">
        <div class="input-group">
            <input type="text" class="form-control" name="search" value="{{ search }}" placeholder="搜索线索...">
            <button class="btn btn-primary" type="submit">搜索</button>
        </div>
    </form>

    <!-- 线索列表 -->
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>姓名</th>
                    <th>公司</th>
                    <th>联系方式</th>
                    <th>进入公海时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for lead in leads %}
                <tr>
                    <td>
                        <a href="{{ url_for('leads.view_lead', lead_id=lead.id) }}">{{ lead.name }}</a>
                    </td>
                    <td>{{ lead.company_name }}</td>
                    <td>
                        {% if lead.email %}
                        <div>{{ lead.email }}</div>
                        {% endif %}
                        {% if lead.phone %}
                        <div>{{ lead.masked_phone }}</div>
                        {% endif %}
                    </td>
                    <td>{{ lead.public_sea_time.strftime('%Y-%m-%d %H:%M') }}</td>
                    <td>
                        <form action="{{ url_for('lead_pool.claim', lead_id=lead.id) }}" method="post" style="display: inline;">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-sm btn-primary">认领</button>
                        </form>
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="5" class="text-center">暂无线索</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- 分页 -->
    {% if pagination.pages > 1 %}
    <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">
            {% for page in pagination.iter_pages() %}
                {% if page %}
                    <li class="page-item {% if page == pagination.page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('lead_pool.index', page=page, search=search) }}">{{ page }}</a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                {% endif %}
            {% endfor %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
    // 添加Bootstrap图标库
    document.addEventListener('DOMContentLoaded', function() {
        const iconLink = document.createElement('link');
        iconLink.rel = 'stylesheet';
        iconLink.href = 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.0/font/bootstrap-icons.css';
        document.head.appendChild(iconLink);
    });
</script>
{% endblock %} 