from datetime import datetime
from . import db

class Company(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(50), unique=True, nullable=False)
    parent_id = db.Column(db.In<PERSON>ger, db.ForeignKey('company.id'))
    manager_id = db.Column(db.Integer, db.ForeignKey('user.id'))  # 公司管理员
    type = db.Column(db.String(20), nullable=False)  # 'headquarters' or 'subsidiary'
    is_default = db.Column(db.Bo<PERSON>an, default=False)  # 是否为默认公司
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    parent = db.relationship('Company', remote_side=[id], back_populates='subsidiaries')
    subsidiaries = db.relationship('Company', back_populates='parent')
    manager = db.relationship('User', foreign_keys=[manager_id], post_update=True, backref='managed_company')  # 公司管理员
    departments = db.relationship('Department', back_populates='company')
    users = db.relationship('User', foreign_keys='User.company_id', back_populates='company')
    lead_pools = db.relationship('LeadPool', back_populates='company')  # 添加与线索池的关系

    # 与 LeadImportRequest 的级联删除关系
    sent_import_requests = db.relationship('LeadImportRequest', 
                                           foreign_keys='LeadImportRequest.requester_company_id', 
                                           back_populates='requester_company', 
                                           cascade="all, delete-orphan")
    received_import_requests = db.relationship('LeadImportRequest', 
                                               foreign_keys='LeadImportRequest.target_company_id', 
                                               back_populates='target_company', 
                                               cascade="all, delete-orphan")

class Department(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(50), nullable=False)
    company_id = db.Column(db.Integer, db.ForeignKey('company.id'), nullable=False)
    parent_id = db.Column(db.Integer, db.ForeignKey('department.id'))
    manager_id = db.Column(db.Integer, db.ForeignKey('user.id'))  # 部门管理员
    type = db.Column(db.String(20), default='department')  # 'department' 或 'group'
    description = db.Column(db.Text)  # 部门描述
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    parent = db.relationship('Department', remote_side=[id], back_populates='subdepartments')
    subdepartments = db.relationship('Department', back_populates='parent')
    manager = db.relationship('User', foreign_keys=[manager_id], post_update=True, backref='managed_department')  # 部门管理员
    company = db.relationship('Company', back_populates='departments')
    users = db.relationship('User', foreign_keys='User.department_id', back_populates='department')
    
    # 确保部门编码在同一公司内唯一
    __table_args__ = (db.UniqueConstraint('company_id', 'code'),)

# 新增线索池模型
class LeadPool(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.String(200))
    company_id = db.Column(db.Integer, db.ForeignKey('company.id'), nullable=False)
    is_public_sea = db.Column(db.Boolean, default=False)  # 是否为公海
    pool_type = db.Column(db.String(50), nullable=False, default='PRIVATE')  # 新增字段: PRIVATE, PUBLIC_SEA, CROSS_LOCATION_PENDING
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    company = db.relationship('Company', back_populates='lead_pools')
    leads = db.relationship('Lead', back_populates='pool')  # 将在Lead模型中定义