from flask import Blueprint, jsonify, request, current_app, session
from utils.captcha import CaptchaGenerator
from flask_wtf.csrf import csrf_exempt

bp = Blueprint('captcha', __name__)

@bp.route('/captcha')
def get_captcha():
    """获取验证码"""
    try:
        captcha = CaptchaGenerator(current_app.redis)
        session_id = captcha.get_session_id()
        code = captcha.generate_code()
        image_base64 = captcha.generate_image(code)
        captcha.save_code(session_id, code)
        
        return jsonify({
            'image': image_base64,
            'session_id': session_id
        })
    except Exception as e:
        current_app.logger.error('生成验证码失败: {}'.format(str(e)))
        return jsonify({'error': '生成验证码失败'}), 500

@csrf_exempt
@bp.route('/verify-captcha', methods=['POST'])
def verify_captcha():
    """验证验证码"""
    try:
        # 检查验证码功能是否启用
        enable_captcha = current_app.config.get('ENABLE_CAPTCHA', False)
        
        data = request.get_json()
        code = data.get('code')
        session_id = data.get('session_id')
        
        if not code or not session_id:
            return jsonify({'success': False, 'message': '验证码不能为空'})
        
        # 如果验证码功能关闭，直接返回验证成功
        if not enable_captcha:
            return jsonify({'success': True})
        
        # 验证码功能开启时的验证逻辑
        captcha = CaptchaGenerator(current_app.redis)
        if captcha.verify_code(session_id, code):
            return jsonify({'success': True})
        return jsonify({'success': False, 'message': '验证码错误'})
    except Exception as e:
        current_app.logger.error(f'验证验证码失败: {str(e)}')
        return jsonify({'error': '验证失败'}), 500 