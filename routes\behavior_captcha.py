from flask import Blueprint, jsonify, request, current_app
from utils.behavior_captcha import BehaviorCaptcha

bp = Blueprint('behavior_captcha', __name__)

@bp.route('/api/behavior-captcha/get', methods=['GET'])
def get_behavior_captcha():  # 获取行为验证码任务
    try:
        captcha = BehaviorCaptcha()
        result = captcha.generate()
        return jsonify({'success': True, 'data': result})
    except Exception as e:
        return jsonify({'success': False, 'message': '生成验证码失败'}), 500

@bp.route('/api/behavior-captcha/verify', methods=['POST'])
def verify_behavior_captcha():  # 验证用户行为
    try:
        # 检查验证码功能是否启用
        enable_captcha = current_app.config.get('ENABLE_CAPTCHA', False)
        
        data = request.get_json()
        token = data.get('token')
        behavior_data = data.get('behavior_data')
        
        if not token or not behavior_data:
            return jsonify({'success': False, 'message': '参数错误'})
        
        # 如果验证码功能关闭，直接返回验证成功
        if not enable_captcha:
            return jsonify({'success': True})
            
        # 验证码功能开启时的验证逻辑
        captcha = BehaviorCaptcha()
        if captcha.verify(token, behavior_data):
            return jsonify({'success': True})
        return jsonify({'success': False, 'message': '验证失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': '验证失败'}), 500 